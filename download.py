# -*- coding: utf-8 -*-
"""
ETCM 数据库数据抓取脚本 (最终修复版)
作者: Gemini
描述: 此版本旨在彻底解决此前所有解析失败的问题。
      1. 采用了全新的两步解析法：先定位到包含所有数据的JavaScript代码块。
      2. 然后在该代码块内，逐一精确查找并提取每个字段（成分、疾病等）的数据。
         此方法不再依赖于脆弱的、试图匹配整个JS结构的正则表达式，因此更加稳定和精确。
      3. 查询列表已根据用户提供的78味中药清单进行精确校正，并加入了智能映射，确保完整查询。
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
from urllib.parse import urljoin, quote_plus
import html

# --- 全局配置 ---
BASE_URL = "http://www.tcmip.cn/ETCM/index.php/"
HERB_LIST_URL = urljoin(BASE_URL, "Home/Index/Prescriptions_All.html?getType=yc")
COMPONENTS_FILENAME = "etcm_herb_components.csv"
DISEASES_FILENAME = "etcm_herb_diseases.csv"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# --- 需要查询的中草药清单 (精确校正为78味) ---
# 格式为: (原始中文名, 网站上可能使用的查询名, 拉丁名)
HERBS_TO_QUERY = [
    ("党参", "党参", "Codonopsis pilosula"), ("三七", "三七", "Panax notoginseng"), ("平贝母", "平贝母", "Fritillaria usuriensis"),
    ("西洋参", "西洋参", "Panax quinquefolius"), ("人参", "人参", "Panax ginseng"), ("枸杞子", "枸杞子", "Lycium barbarum"),
    ("沙棘", "沙棘", "Hippophae rhamnoides"), ("灵芝", "灵芝", "Ganoderma lucidum"), ("白头翁", "白头翁", "Pulsatilla chinensis"),
    ("白术", "白术", "Atractylodes macrocephala"), ("白芷", "白芷", "Angelica dahurica"), ("百合", "百合", "Lilium brownii var. viridulum"),
    ("石榴皮", "石榴皮", "Punica granatum"), ("红花", "红花", "Carthamus tinctorius"), ("大腹皮", "大腹皮", "Areca catechu"),
    ("小蓟", "小蓟", "Cirsium arvense"), ("山豆根", "山豆根", "Sophora tonkinensis"), ("八角茴香", "八角茴香", "Illicium verum"),
    ("地黄", "地黄", "Rehmannia glutinosa"), ("制川乌", "川乌", "Aconitum carmichaeli"), # 映射
    ("核桃仁", "胡桃仁", "Juglans regia"), # 映射
    ("酸枣仁", "酸枣仁", "Ziziphus jujuba"), ("太子参", "太子参", "Pseudostellaria heterophylla"), ("甘草", "甘草", "Glycyrrhiza uralensis"),
    ("当归", "当归", "Angelica sinensis"), ("麦冬", "麦冬", "Ophiopogon japonicus"), ("黄精", "黄精", "Polygonatum kingianum"),
    ("淡豆豉", "淡豆豉", "Glycine max"), ("菊花", "菊花", "Chrysanthemum × morifolium"), ("紫苏叶", "紫苏叶", "Perilla frutescens"),
    ("灯心草", "灯心草", "Juncus effusus"), ("天麻", "天麻", "Gastrodia elata"), ("花椒", "花椒", "Zanthoxylum bungeanum"),
    ("阿魏", "阿魏", "Ferula sinkiangensis"), ("附子", "附子", "Aconitum carmichaeli"), ("麦芽", "麦芽", "Hordeum vulgare"),
    ("玫瑰花", "玫瑰花", "Rosa rugosa"), ("降香", "降香", "Dalbergia odorifera"), ("砂仁", "砂仁", "Wurfbainia villosa"),
    ("荔枝核", "荔枝核", "Litchi chinensis"), ("辣椒", "辣椒", "Capsicum annuum"), ("马齿苋", "马齿苋", "Portulaca oleracea"),
    ("赤芍", "赤芍", "Paeonia lactiflora"), ("金银花", "金银花", "Lonicera japonica"), ("青葙子", "青葙子", "Celosia argentea"),
    ("重楼", "重楼", "Paris polyphylla var. chinensis"), ("秦皮", "秦皮", "Fraxinus chinensis subsp. rhynchophylla"),
    ("野菊花", "野菊花", "Chrysanthemum indicum"), ("黄芩", "黄芩", "Scutellaria baicalensis"), ("黄连", "黄连", "Coptis deltoidea"),
    ("椿皮", "椿皮", "Ailanthus altissima"), ("油松节", "油松节", "Pinus massoniana"), ("金铁锁", "金铁锁", "Psammosilene tunicoides"),
    ("南五味子", "南五味子", "Schisandra sphenanthera"), ("亚麻子", "亚麻子", "Linum usitatissimum"), ("蓖麻子", "蓖麻子", "Ricinus communis"),
    ("鸭跖草", "鸭跖草", "Commelina communis"), ("半夏", "半夏", "Pinellia ternata"), ("干姜", "干姜", "Zingiber officinale"),
    ("柴胡", "柴胡", "Bupleurum chinense"), ("丹参", "丹参", "Salvia miltiorrhiza"), ("牛膝", "牛膝", "Achyranthes bidentata"),
    ("西红花", "西红花", "Crocus sativus"), ("急性子", "急性子", "Impatiens balsamina"), ("白及", "白及", "Bletilla striata"),
    ("白茅根", "白茅根", "Imperata cylindrica"), ("艾叶", "艾叶", "Artemisia argyi"), ("松花粉", "松花粉", "Pinus massoniana"),
    ("蒲黄", "蒲黄", "Typha orientalis"), ("大豆黄卷", "大豆黄卷", "Glycine max"), ("紫菀", "紫菀", "Aster tataricus"),
    ("石斛", "石斛", "Dendrobium nobile"), ("广藿香", "广藿香", "Pogostemon cablin"), ("苍术", "苍术", "Atractylodes lancea"),
    ("陈皮", "陈皮", "Citrus reticulata"), ("苦参", "苦参", "Sophora flavescens"), ("川贝母", "川贝母", "Fritillaria cirrhosa"),
    ("鸡冠花", "鸡冠花", "Celosia argentea")
]


def build_herb_index_from_script():
    """通过解析页面内嵌的JavaScript数据来构建草药索引。"""
    print("--- 正在构建草药索引，请稍候... ---")
    try:
        proxies = {"http": None, "https": None}
        response = requests.get(HERB_LIST_URL, headers=HEADERS, timeout=30, proxies=proxies)
        response.raise_for_status()
        html_content = response.text
        
        script_data_match = re.search(r"data\s*:\s*\[(.*?)\]\s*,\s*silent", html_content, re.DOTALL)
        if not script_data_match: return None
            
        js_data_string = script_data_match.group(1)
        herb_info_pattern = re.compile(r'href="([^"]+)">([^<]+)</a>')
        matches = herb_info_pattern.findall(js_data_string)
        
        herb_index = {name.strip(): urljoin("http://www.tcmip.cn/", href.strip()) for href, name in matches if name.strip() and href}
        
        print(f"--- 索引构建完成！共找到 {len(herb_index)} 味中草药。 ---")
        return herb_index

    except Exception as e:
        print(f"[严重错误] 构建草药索引失败: {e}")
        return None

def parse_all_data_from_page(html_content):
    """
    (全新最终逻辑) 从详情页HTML中找到对应的JS代码块，然后解析出所需信息。
    """
    components_genes_list = []
    diseases = []
    soup = BeautifulSoup(html_content, 'html.parser')
    scripts = soup.find_all('script')

    # --- 解析疾病 ---
    main_info_script_text = ""
    for script in scripts:
        # 使用get_text()以处理复杂的<script>标签内容
        script_text = script.get_text()
        if "'#table'" in script_text:
            main_info_script_text = script_text
            break
    
    if main_info_script_text:
        try:
            dis_match = re.search(r"Diseases Associated with This Herb</div>\".*?\"Item Name\":\s*\"<div.*?>(.*?)</div>\"", main_info_script_text, re.DOTALL)
            if dis_match:
                dis_html = html.unescape(dis_match.group(1))
                soup_dis = BeautifulSoup(dis_html, 'html.parser')
                raw_diseases = [a.text.strip() for a in soup_dis.find_all('a')]
                diseases = [d.lstrip(';').strip() for d in raw_diseases if d.strip()]
        except Exception as e:
            print(f"  [解析警告] 解析疾病信息时发生错误: {e}")

    # --- 解析成分和靶点基因 ---
    components_script_text = ""
    for script in scripts:
        script_text = script.get_text()
        if "'#reportTable00'" in script_text:
            components_script_text = script_text
            break
    
    if components_script_text:
        try:
            data_match = re.search(r"data\s*:\s*\[(.*?)\]\s*,\s*silent", components_script_text, re.DOTALL)
            if data_match:
                js_data_string = data_match.group(1)
                component_entries = re.findall(r'\{.*?\}', js_data_string, re.DOTALL)
                for entry in component_entries:
                    comp_name_match = re.search(r'"cf_name":\s*"<a.*?>(.*?)</a>"', entry)
                    genes_match = re.search(r'"cf_gene":\s*"(.+?)"', entry)
                    if comp_name_match:
                        comp_name = html.unescape(comp_name_match.group(1)).strip()
                        genes_str = "N/A"
                        if genes_match:
                            genes_html = html.unescape(genes_match.group(1))
                            soup_genes = BeautifulSoup(genes_html, 'html.parser')
                            genes = [a.text.strip() for a in soup_genes.find_all('a')]
                            genes_str = ", ".join(genes) if genes else "N/A"
                        components_genes_list.append({'Component': comp_name, 'Candidate_Target_Genes': genes_str})
        except Exception as e:
            print(f"  [解析警告] 解析成分-靶点信息时发生错误: {e}")
            
    return components_genes_list, diseases

def main():
    herb_index = build_herb_index_from_script()
    if not herb_index:
        print("程序因无法构建索引而终止。")
        return
        
    print("\n--- 开始根据您的清单抓取详细数据 ---")
    
    all_components_data = []
    all_diseases_data = []

    for i, (original_chinese, query_name, original_latin) in enumerate(HERBS_TO_QUERY):
        print(f"\n({i+1}/{len(HERBS_TO_QUERY)}) 正在处理: {original_chinese} ({original_latin})")
        
        detail_url = herb_index.get(query_name)
        if not detail_url:
            print(f"  [警告] 在网站索引中未找到 '{query_name}'。")
            continue
        
        print(f"  [信息] 找到详情页: {detail_url}")
        
        html_content = None
        for attempt in range(3):
            try:
                proxies = {"http": None, "https": None}
                response = requests.get(detail_url, headers=HEADERS, timeout=30, proxies=proxies)
                response.raise_for_status()
                html_content = response.text
                break 
            except requests.exceptions.RequestException as e:
                print(f"  [网络错误] (第 {attempt + 1}/3 次尝试) 访问失败: {e}")
                if attempt < 2: time.sleep(2)
        
        if not html_content:
            print(f"  [严重错误] 多次尝试后仍无法获取页面内容，跳过。")
            continue

        try:
            components_with_genes, diseases = parse_all_data_from_page(html_content)

            print(f"  [成功] 找到 {len(components_with_genes)} 个化学成分-靶点条目。")
            print(f"  [成功] 找到 {len(diseases)} 个关联疾病。")
            
            # 整理成分数据
            for item in components_with_genes:
                component_name = item['Component']
                pubchem_link = f"https://pubchem.ncbi.nlm.nih.gov/#query={quote_plus(component_name)}"
                
                all_components_data.append({
                    'latin_name': original_latin,
                    'chinese_name': original_chinese,
                    'Component': component_name,
                    'Candidate_Target_Genes': item['Candidate_Target_Genes'],
                    'PubChem_Link': pubchem_link
                })

            # 整理疾病数据
            for disease_name in diseases:
                all_diseases_data.append({
                    'latin_name': original_latin,
                    'chinese_name': original_chinese,
                    'Disease': disease_name
                })

        except Exception as e:
            print(f"  [程序错误] 处理 '{original_chinese}' 时发生未知错误: {e}")
            
        time.sleep(1) 

    # 保存成分文件
    if all_components_data:
        df_comp = pd.DataFrame(all_components_data)
        df_comp.to_csv(COMPONENTS_FILENAME, index=False, encoding='utf-8-sig')
        print(f"\n--- 成分数据抓取完成 ---")
        print(f"数据已成功保存到文件: {COMPONENTS_FILENAME}")
        print(f"总共记录了 {len(df_comp)} 条成分数据。")
    else:
        print("\n--- 未抓取到任何成分数据 ---")

    # 保存疾病文件
    if all_diseases_data:
        df_dis = pd.DataFrame(all_diseases_data)
        df_dis.to_csv(DISEASES_FILENAME, index=False, encoding='utf-8-sig')
        print(f"\n--- 疾病数据抓取完成 ---")
        print(f"数据已成功保存到文件: {DISEASES_FILENAME}")
        print(f"总共记录了 {len(df_dis)} 条疾病数据。")
    else:
        print("\n--- 未抓取到任何疾病数据 ---")

if __name__ == "__main__":
    main()
