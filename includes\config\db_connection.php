<?php
// 允许跨域请求的头部信息
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// 数据库连接参数
$servername = "localhost";
$username = "root";
$password = "Lp82nlf.";
$dbname = "HRMA";

// 连接池配置
$max_connections = 10; // 最大连接数
$persistent = true;    // 使用持久连接

// 创建数据库连接
$conn = null;
try {
    // 使用持久连接以提高性能
    if ($persistent) {
        $conn = mysqli_connect("p:".$servername, $username, $password, $dbname);
    } else {
        $conn = mysqli_connect($servername, $username, $password, $dbname);
    }
    
    // 检查连接是否成功
    if (!$conn) {
        throw new Exception("数据库连接失败: " . mysqli_connect_error());
    }
    
    // 设置字符集为 utf8mb4，支持完整的 Unicode 字符集
    mysqli_set_charset($conn, 'utf8mb4');
    
    // 设置等待超时，避免长时间等待
    $conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 10);
    
    // 设置查询缓存和性能优化配置
    $conn->query("SET SESSION query_cache_type=1");
    $conn->query("SET SESSION query_cache_size=10485760"); // 设置查询缓存大小为10MB
    $conn->query("SET SESSION query_cache_limit=1048576"); // 单个查询缓存限制为1MB
    
    // 设置连接池和性能相关参数
    $conn->query("SET SESSION interactive_timeout=3600"); // 连接空闲超时时间
    $conn->query("SET SESSION wait_timeout=3600");
    $conn->query("SET SESSION max_allowed_packet=16777216"); // 最大允许的数据包大小
    $conn->query("SET SESSION net_buffer_length=16384"); // 网络缓冲区大小
    
    // 返回连接对象
    return $conn;
} catch (Exception $e) {
    // 记录错误日志
    error_log("数据库连接错误: " . $e->getMessage());
    
    // 如果在API中，返回JSON错误
    if (strpos($_SERVER['REQUEST_URI'], '/api/') !== false) {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code(500);
        echo json_encode([
            'error' => '数据库连接失败',
            'status' => 500,
            'total' => 0,
            'rows' => []
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 如果在页面中，返回错误页面
    echo "数据库连接失败，请稍后再试或联系管理员。";
    exit;
}
?>