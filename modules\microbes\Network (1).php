<?php
// 设置缓存控制
$cache_time = 3600; // 1小时缓存
header("Cache-Control: public, max-age=$cache_time");
header("Expires: " . gmdate("D, d M Y H:i:s", time() + $cache_time) . " GMT");

// 生成ETag用于验证缓存
$etag = md5($_SERVER['REQUEST_URI'] . filemtime(__FILE__));
header("ETag: \"$etag\"");

// 检查客户端缓存是否有效
if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && 
    trim($_SERVER['HTTP_IF_NONE_MATCH']) == "\"$etag\"") {
    header("HTTP/1.1 304 Not Modified");
    exit;
}

// 引入数据库连接配置
$conn = require_once '../../includes/config/db_connection.php';

// 获取可用的项目列表
$bacteria_projects = [];
$fungi_projects = [];
$project_metadata = []; // 存储项目元数据

// 检查缓存文件
$cache_dir = sys_get_temp_dir() . '/network_cache';
if (!is_dir($cache_dir)) {
    mkdir($cache_dir, 0755, true);
}
$cache_file = $cache_dir . '/projects_' . md5(__FILE__) . '.cache';

// 尝试从缓存加载数据
$cache_valid = false;
if (file_exists($cache_file) && (time() - filemtime($cache_file) < 3600)) { // 1小时缓存
    $cache_data = unserialize(file_get_contents($cache_file));
    if (is_array($cache_data) && isset($cache_data['bacteria']) && isset($cache_data['fungi']) && isset($cache_data['metadata'])) {
        $bacteria_projects = $cache_data['bacteria'];
        $fungi_projects = $cache_data['fungi'];
        $project_metadata = $cache_data['metadata'];
        $cache_valid = true;
        error_log("加载项目数据从缓存文件: " . $cache_file);
    }
}

// 如果缓存无效，从数据库加载
if (!$cache_valid) {
    // 优化SQL查询 - 使用索引并限制字段
    // 获取细菌网络项目
    $sql = "SELECT id, project_id, level FROM bacteria_networks 
            WHERE id > 0 
            ORDER BY project_id 
            LIMIT 500"; // 限制返回行数避免过大结果集
    
    $result = $conn->query($sql);
    if ($result && $result->num_rows > 0) {
        $processed_projects = []; // 用于追踪已处理的项目ID
        
        while ($row = $result->fetch_assoc()) {
            $project_id = $row['project_id'];
            
            // 格式化项目ID：确保以SRID-开头
            if (strpos($project_id, 'SRID-') !== 0) {
                $project_id = 'SRID-' . preg_replace('/^SRID-/', '', $project_id);
            }
            
            // 防止重复
            if (!in_array($project_id, $processed_projects)) {
                $processed_projects[] = $project_id;
                $bacteria_projects[] = $project_id;
                $project_metadata[$project_id] = [
                    'type' => 'bacteria',
                    'level' => $row['level'],
                    'db_id' => $row['id']
                ];
            }
        }
        $result->free(); // 释放结果集
    }
    
    // 获取真菌网络项目
    $sql = "SELECT id, project_id, level FROM fungi_networks 
            WHERE id > 0 
            ORDER BY project_id 
            LIMIT 500"; // 限制返回行数避免过大结果集
    
    $result = $conn->query($sql);
    if ($result && $result->num_rows > 0) {
        $processed_projects = []; // 用于追踪已处理的项目ID
        
        while ($row = $result->fetch_assoc()) {
            $project_id = $row['project_id'];
            
            // 格式化项目ID：确保以SRID-开头
            if (strpos($project_id, 'SRID-') !== 0) {
                $project_id = 'SRID-' . preg_replace('/^SRID-/', '', $project_id);
            }
            
            // 防止重复
            if (!in_array($project_id, $processed_projects)) {
                $processed_projects[] = $project_id;
                $fungi_projects[] = $project_id;
                $project_metadata[$project_id] = [
                    'type' => 'fungi',
                    'level' => $row['level'],
                    'db_id' => $row['id']
                ];
            }
        }
        $result->free(); // 释放结果集
    }
    
    // 按照SRID-数字大小排序
    usort($bacteria_projects, function($a, $b) {
        $a_num = intval(preg_replace('/^SRID-/', '', $a));
        $b_num = intval(preg_replace('/^SRID-/', '', $b));
        return $a_num - $b_num;
    });
    
    usort($fungi_projects, function($a, $b) {
        $a_num = intval(preg_replace('/^SRID-/', '', $a));
        $b_num = intval(preg_replace('/^SRID-/', '', $b));
        return $a_num - $b_num;
    });
    
    // 保存到缓存
    $cache_data = [
        'bacteria' => $bacteria_projects,
        'fungi' => $fungi_projects,
        'metadata' => $project_metadata,
        'timestamp' => time()
    ];
    file_put_contents($cache_file, serialize($cache_data));
    error_log("保存项目数据到缓存文件: " . $cache_file);
}

// 添加调试信息，记录去重后的项目数量
error_log("Projects loaded (cache: " . ($cache_valid ? 'hit' : 'miss') . "): " . json_encode([
    'bacteria' => count($bacteria_projects),
    'fungi' => count($fungi_projects)
]));
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microbial Network Visualization</title>
    <!-- 添加浏览器缓存控制 -->
    <meta http-equiv="Cache-Control" content="max-age=3600, public">
    <!-- 添加favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../../public/css/common.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../map/echarts.min.js"></script>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .content-section {
            flex: 1;
            padding: 2rem;
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
        }
        .container-fluid {
            width: 100% !important;
            margin: 0 auto;
        }
        .control-panel {
            height: auto; /* 改为自动高度以适应不同屏幕尺寸 */
            min-height: 100px;
            padding: 15px;
            background-color: #ffffff;
            border-radius: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid #f0f0f0;
        }
        .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            padding: 8px 12px;
            font-size: 14px;
        }
        .form-select:focus {
            border-color: #002060;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        .btn-success {
            background-color: #002060;
            border-color: #002060;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background-color: #002060;
            border-color: #002060;
            transform: translateY(-1px);
        }
        .btn-outline-dark {
            border-color: #343a40;
            color: #343a40;
            transition: all 0.3s ease;
        }
        .btn-outline-dark:hover {
            background-color: #343a40;
            color: white;
            transform: translateY(-1px);
        }
        .gap-2 {
            gap: 0.5rem !important;
        }
        .row.justify-content-center {
            flex: 1;
            display: flex;
        }
        .col-11 {
            display: flex;
            flex-direction: column;
        }
        footer {
            margin-top: auto;
            background-color: #002060;
            color: white;
            padding: 10px 0;
        }
        .legend-item {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 10px;
        }
        .legend-color {
            display: inline-block;
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border-radius: 3px;
            vertical-align: middle;
        }
        .network-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .network-footer {
            text-align: center;
            margin-top: 10px;
            color: #666;
            font-size: 12px;
            line-height: 1.5;
        }
        .network-legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 10px;
        }
        .link-tooltip {
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            padding: 8px 12px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            pointer-events: none;
            color: #333;
            font-size: 12px;
            line-height: 1.4;
            z-index: 9999;
        }
        #network-container {
            width: 100%;
            height: calc(90vh - 200px) !important; /* 减去header、control-panel和footer的高度 */
            margin: 0 auto;
            margin-top: 15px;
            border: 1px solid #eee;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            transform: scale(1.0);
            transform-origin: center center;
            display: block !important;
        }
        .control-panel .row {
        margin: 0;
        gap: 0;
        }
        .control-panel .col-auto {
        padding: 0;
        }
        .control-panel .col-auto:first-child {
         margin-right: 10px;
        }
        
        /* 响应式布局调整 */
        @media (max-width: 768px) {
            .control-panel .row {
                flex-direction: column;
            }
            .control-panel .col-md-5, 
            .control-panel .col-md-2 {
                width: 100%;
                margin-bottom: 10px;
            }
            .control-panel .mb-0 {
                margin-bottom: 10px !important;
            }
            .control-panel .d-flex.justify-content-end {
                justify-content: center !important;
            }
            #network-container {
                height: calc(60vh - 180px) !important;
            }
        }
        
        header, footer {
            width: 100% !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
            max-width: none !important;
        }
        
        footer p {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <!-- 导航区域 -->
    <header>
         <!-- 左侧Logo和标题区域 -->
         <div class="left-content">
            <!-- Logo容器 -->
            <div class="logo-container">
                <img src="../../home/<USER>/logo.png" alt="HRMA Logo" class="logo-image">
            </div>
            <!-- 标题和副标题容器 -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">HRMA</h1>
                <p class="mb-0 small">Herbal Rhizosphere Microbiome Atlas</p>
            </div>
        </div>
        <!-- 汉堡包按钮 -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- 导航链接区域 -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="../browse/browse.php" class="nav-link">Browse</a></li>
                <li class="nav-item"><a href="../browse/Project.php" class="nav-link">Herbs</a></li>
                <li class="nav-item"><a href="Microbes.php" class="nav-link">Microbes</a></li>
                <li class="nav-item"><a href="Network.php" class="nav-link active">Network</a></li>
                <li class="nav-item"><a href="../../map/map.html" class="nav-link">Map</a></li>
                <li class="nav-item"><a href="#" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

    <!-- 添加页面主标题区域 -->
    <div class="content-section">
        <div class="container-fluid py-3 text-center" style="background-color: #f8f9fa;">
            <h2 style="font-size: 1.5rem;color: #002060; font-weight: 600;">Microbial Co-occurrence Network</h2>
        </div>
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="control-panel">
                        <div class="row align-items-center w-100">
                            <div class="col-auto" style="margin-right: 20px;">
                                <div class="mb-0">
                                    <label for="networkType" class="form-label" style="font-weight: 900; color: #002060;">Microbial Type</label>
                                    <select class="form-select form-select-sm" id="networkType" style="font-weight: 900; width: 150px;">
                                        <option value="bacteria" selected>Bacteria</option>
                                        <option value="fungi">Fungi</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-auto" style="margin-right: 20px;">
                                <div class="mb-0">
                                    <label for="projectID" class="form-label" style="font-weight: 900; color: #002060;">Project Selection</label>
                                    <select class="form-select form-select-sm" id="projectID" style="font-weight: 900; width: 150px;">
                                        <?php foreach ($bacteria_projects as $project): ?>
                                        <option value="<?php echo htmlspecialchars($project); ?>"><?php echo htmlspecialchars($project); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-auto d-flex align-items-end" style="margin-top: 28px;">
                                <button class="btn btn-outline-primary btn-sm mx-1" id="zoomInBtn" title="Zoom In">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm mx-1" id="zoomOutBtn" title="Zoom Out">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button class="btn btn-outline-success btn-sm mx-1" id="downloadBtn" title="Download PNG">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-outline-dark btn-sm mx-1" id="resetBtn" title="Reset View">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="btn btn-primary btn-sm mx-1" id="compareBtn" title="Compare Analysis" style="background-color: #002060; border-color: #002060; font-weight: 900;">
                                    Compare
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 主网络容器 -->
                    <div id="network-container"></div>
                    
                    <!-- 对比分析模态框 -->
                    <div class="modal fade" id="compareModal" tabindex="-1" aria-labelledby="compareModalLabel">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="compareModalLabel" style="color: #002060; font-weight: 900;">Network Comparison Analysis</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="container-fluid">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="firstProject" class="form-label">First Project</label>
                                                <select class="form-select" id="firstProject">
                                                    <!-- Options dynamically added by JS -->
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="secondProject" class="form-label">Second Project</label>
                                                <select class="form-select" id="secondProject">
                                                    <!-- Options dynamically added by JS -->
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" id="startCompareBtn">Start Comparison</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 对比视图容器 -->
                    <div id="compare-container" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 1050; background-color: #f8f9fa; padding: 70px 20px 20px 20px; overflow: auto;">
                        <div class="container-fluid h-100">
                            <div class="row h-100">
                                <div class="col-12 text-center mb-4">
                                    <h2 style="font-size: 2rem; color: #002060; font-weight: 700;">Network Comparison View</h2>
                                </div>
                                <div class="col-md-6 h-100">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h3 id="network-title-1" class="mb-0" style="color: #002060; font-weight: 700; font-size: 1.5rem;"></h3>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-primary btn-sm mx-1" id="zoomInBtn1" title="Zoom In">
                                                <i class="fas fa-search-plus"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm mx-1" id="zoomOutBtn1" title="Zoom Out">
                                                <i class="fas fa-search-minus"></i>
                                            </button>
                                            <button class="btn btn-outline-success btn-sm mx-1" id="downloadBtn1" title="Download PNG">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-dark btn-sm mx-1" id="resetBtn1" title="Reset View">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="network-container-1" style="width: 100%; height: calc(100vh - 250px); border: 1px solid #eee; background-color: white; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); position: relative;">
                                        <div style="position: absolute; bottom: 20px; right: 20px; background: rgba(255,255,255,0.9); padding: 10px; border-radius: 5px; font-size: 14px; color: #002060; text-align: left;">
                                            <strong>Tips:</strong><br>
                                            <span style="color: #3498db;">Blue edges</span>: Positive correlation<br>
                                            <span style="color: #e67e22;">Orange edges</span>: Negative correlation
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 h-100">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h3 id="network-title-2" class="mb-0" style="color: #002060; font-weight: 700; font-size: 1.5rem;"></h3>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-primary btn-sm mx-1" id="zoomInBtn2" title="Zoom In">
                                                <i class="fas fa-search-plus"></i>
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm mx-1" id="zoomOutBtn2" title="Zoom Out">
                                                <i class="fas fa-search-minus"></i>
                                            </button>
                                            <button class="btn btn-outline-success btn-sm mx-1" id="downloadBtn2" title="Download PNG">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-dark btn-sm mx-1" id="resetBtn2" title="Reset View">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="network-container-2" style="width: 100%; height: calc(100vh - 250px); border: 1px solid #eee; background-color: white; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); position: relative;">
                                        <div style="position: absolute; bottom: 20px; right: 20px; background: rgba(255,255,255,0.9); padding: 10px; border-radius: 5px; font-size: 14px; color: #002060; text-align: left;">
                                            <strong>Tips:</strong><br>
                                            <span style="color: #3498db;">Blue edges</span>: Positive correlation<br>
                                            <span style="color: #e67e22;">Orange edges</span>: Negative correlation
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 text-center mt-3">
                                    <button class="btn btn-outline-dark" id="exitCompareBtn" style="font-size: 1.1rem; padding: 8px 16px;">
                                        <i class="fas fa-times"></i> Exit Comparison Mode
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚区域 -->
    <footer class="py-3 text-center" style="background-color: #002060; color: white; width: 100%; margin: 0; max-width: none;">
        <p>Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
    </footer>

    <!-- 脚本引入 -->
    <script>
        function toggleNav() {
            const nav = document.querySelector('nav');
            const hamburger = document.querySelector('.hamburger');
            nav.classList.toggle('show');
            hamburger.classList.toggle('active');
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 网络图相关脚本 -->
    <script>
        // 初始化变量
        let myChart = null;
        let networkData = null;
        let bacteriaProjects = <?php echo json_encode($bacteria_projects); ?>;
        let fungiProjects = <?php echo json_encode($fungi_projects); ?>;
        let projectMetadata = <?php echo json_encode($project_metadata); ?>;
        
        // 客户端数据缓存
        const dataCache = {};
        
        // 去除可能的重复项
        bacteriaProjects = [...new Set(bacteriaProjects)];
        fungiProjects = [...new Set(fungiProjects)];
        
        console.log('PHP传递的数据(去重后):', {
            bacteriaProjects: bacteriaProjects,
            fungiProjects: fungiProjects,
            projectMetadata: projectMetadata
        });
        
        // 如果项目列表为空，添加测试数据
        if (!bacteriaProjects || bacteriaProjects.length === 0) {
            console.log('细菌项目列表为空，添加测试数据');
            bacteriaProjects = ['SRID-1', 'SRID-2', 'SRID-3'];
        }
        
        if (!fungiProjects || fungiProjects.length === 0) {
            console.log('真菌项目列表为空，添加测试数据');
            fungiProjects = ['SRID-4', 'SRID-5'];
        }
        
        // 添加项目元数据
        if (!projectMetadata || Object.keys(projectMetadata).length === 0) {
            console.log('项目元数据为空，添加测试数据');
            projectMetadata = {};
            
            bacteriaProjects.forEach(project => {
                projectMetadata[project] = {
                    type: 'bacteria',
                    level: 'genus',
                    db_id: project.replace('SRID-', '')
                };
            });
            
            fungiProjects.forEach(project => {
                projectMetadata[project] = {
                    type: 'fungi',
                    level: 'genus',
                    db_id: project.replace('SRID-', '')
                };
            });
        }
        
        // 预定义的颜色映射，使用更深沉的配色
        const predefinedColors = {
            'Actinobacteria': '#3b4da8',    // 蓝色
            'Bacteroidetes': '#97c64b',     // 浅绿色
            'Proteobacteria': '#f0b241',    // 黄/橙色
            'Firmicutes': '#ea4c46',        // 红色
            'Verrucomicrobia': '#69acd5',   // 浅蓝色
            'Fusobacteria': '#2a7e5f',      // 深绿色
            'Deinococcus-Thermus': '#ef7d36', // 橙色
            'Spirochaetes': '#9a5cb4',      // 紫色
            'Lentisphaerae': '#f783b0',     // 粉色
            'Fibrobacteres': '#4952b5',     // 深蓝色
            'Nonbacteria': '#8bc267'        // 浅绿色
        };
        
        // 颜色映射
        const colorMap = {};
        
        // 初始化图表
        function initChart() {
            try {
                console.log('开始初始化图表...');
                
                if (myChart !== null) {
                    console.log('销毁现有图表实例');
                    myChart.dispose();
                }
                
                const container = document.getElementById('network-container');
                console.log('容器状态:', {
                    found: !!container,
                    width: container?.clientWidth,
                    height: container?.clientHeight,
                    style: container?.style?.cssText
                });
                
                if (!container) {
                    throw new Error('找不到网络图容器元素！');
                }
                
                // 确保容器有明确的尺寸
                container.style.width = '100%';
                container.style.height = 'calc(90vh - 200px)';
                container.style.minHeight = '500px';
                
                console.log('初始化ECharts实例...');
                myChart = echarts.init(container);
                console.log('ECharts实例创建成功:', !!myChart);
                
                myChart.showLoading({
                    text: '正在加载网络数据...',
                    color: '#002060',
                    textColor: '#333',
                    maskColor: 'rgba(255, 255, 255, 0.8)'
                });
                
                return true;
            } catch (error) {
                console.error('图表初始化失败:', error);
                alert('图表初始化失败: ' + error.message);
                return false;
            }
        }
        
        // 生成颜色
        function getRandomColor(category) {
            // 如果有预定义颜色，优先使用
            if (predefinedColors[category]) {
                return predefinedColors[category];
            }
            
            // 如果已经有分配的颜色，使用已有的
            if (colorMap[category]) {
                return colorMap[category];
            }
            
            // 对于Unknown或其他未指定的类别，生成随机颜色
            const letters = '0123456789ABCDEF';
            let color = '#';
            // 避免颜色太浅
            for (let i = 0; i < 6; i++) {
                color += letters[Math.floor(Math.random() * 10) + 6];
            }
            colorMap[category] = color;
            return color;
        }
        
        // 加载网络数据
        async function loadNetworkData(type, level, projectId) {
            try {
                console.log('开始加载数据:', { type, level, projectId });
                
                if (!myChart) {
                    console.log('图表实例不存在，重新初始化');
                    if (!initChart()) {
                        throw new Error('图表初始化失败');
                    }
                }
                
                // 检查缓存中是否已有数据
                const cacheKey = `${type}_${level}_${projectId}`;
                if (dataCache[cacheKey]) {
                    console.log('使用缓存数据:', cacheKey);
                    networkData = dataCache[cacheKey];
                    await renderNetwork(projectId);
                    return;
                }
                
                // 从projectId中提取数字部分
                const projectNumber = projectId.replace(/^SRID-/, '');
                // 使用完整的SRID格式构建URL
                const url = `../../api/get_network_data.php?type=${type}&level=${level}&project=SRID-${projectNumber}`;
                
                console.log('API请求URL:', url);
                
                // 显示加载提示
                myChart.showLoading({
                    text: '正在加载网络数据...',
                    color: '#002060',
                    textColor: '#333',
                    maskColor: 'rgba(255, 255, 255, 0.8)'
                });
                
                try {
                    // 使用带有缓存控制的fetch
                    const response = await fetch(url, {
                        cache: 'default', // 使用浏览器默认缓存策略
                        headers: {
                            'Cache-Control': 'max-age=3600' // 1小时客户端缓存
                        }
                    });
                    
                    console.log('API响应状态:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    if (!data || !data.nodes || !data.links) {
                        console.error('API响应数据不完整:', data);
                        throw new Error('API返回的数据格式不正确或不完整');
                    }
                    
                    console.log('数据加载成功:', {
                        hasNodes: !!data.nodes,
                        nodesCount: data.nodes?.length,
                        hasLinks: !!data.links,
                        linksCount: data.links?.length,
                        hasCategories: !!data.categories,
                        categoriesCount: data.categories?.length
                    });
                    
                    // 保存到客户端缓存
                    dataCache[cacheKey] = data;
                    networkData = data;
                    
                    if (!myChart) {
                        console.warn('图表实例不存在，重新初始化');
                        initChart();
                    }
                    
                    console.log('开始渲染网络图');
                    await renderNetwork(projectId);
                } catch (fetchError) {
                    console.error('API请求失败:', fetchError);
                    throw new Error(`API请求失败: ${fetchError.message}`);
                }
                
            } catch (error) {
                console.error('数据加载失败:', error);
                if (myChart) {
                    myChart.hideLoading();
                    myChart.setOption({
                        title: {
                            text: '加载失败',
                            subtext: `错误: ${error.message}`,
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#ff4444' }
                        }
                    });
                }
            }
        }
        
        // 渲染网络图
        function renderNetwork(projectId) {
            if (!networkData) return;
            
            // 为每个类别分配唯一颜色
            const categories = networkData.categories;
            categories.forEach(category => {
                category.itemStyle = {
                    color: getRandomColor(category.name)
                };
            });
            
            // 过滤节点，移除没有连接的孤立节点
            const connectedNodeIds = new Set();
            
            // 找出所有有连接的节点ID
            networkData.links.forEach(link => {
                connectedNodeIds.add(link.source);
                connectedNodeIds.add(link.target);
            });
            
            // 过滤掉没有连接的节点
            const filteredNodes = networkData.nodes.filter(node => connectedNodeIds.has(node.id));
            
            console.log(`过滤前节点数量: ${networkData.nodes.length}, 过滤后: ${filteredNodes.length}`);
            
            // 设置圆形环形布局
            const radius = Math.min(myChart.getWidth(), myChart.getHeight()) * 0.125; // 将半径缩小一半，从0.25到0.125
            const centerX = myChart.getWidth() / 2;
            const centerY = myChart.getHeight() / 2;
            // 所有节点均匀分布在圆周上
            const allNodes = filteredNodes;
            const totalNodes = allNodes.length;
            const angleStep = (Math.PI * 2) / totalNodes; // 等分圆周

            // 计算每个节点的位置，均匀分布在圆周上
            allNodes.forEach((node, i) => {
                // 计算节点在圆上的角度，均匀分布
                const angle = i * angleStep;
                
                // 设置节点位置
                node.x = centerX + radius * Math.cos(angle);
                node.y = centerY + radius * Math.sin(angle);
                node.fixed = true; // 固定节点位置
                
                // 存储节点的初始角度信息
                node.angle = angle;
                node.initialAngle = angle; // 保存初始角度，用于恢复
                
                // 计算标签的旋转角度
                let rotate = angle * 180 / Math.PI;
                
                // 调整角度使文字沿着径向线排布
                if (rotate > 90 && rotate < 270) {
                    rotate += 180;
                }
                
                // 规范化角度在0-360范围内
                rotate = rotate % 360;
                
                // 保存标签旋转角度
                node.labelRotate = rotate;
            });
            
            // 获取当前项目信息
            const metadata = projectMetadata[projectId] || {};
            const projectLevel = metadata.level || 'genus';
            
            const option = {
                title: {
                    subtext: document.getElementById('networkType').options[document.getElementById('networkType').selectedIndex].text + ' - Phylum',
                    top: 'top',
                    left: 'center',
                    subtextStyle: {
                        color: '#002060',
                        fontSize: 18,
                        fontWeight: '900'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#ccc',
                    borderWidth: 1,
                    padding: 15,
                    textStyle: {
                        color: '#002060',
                        fontSize: 16,
                        fontWeight: '900'
                    },
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            const abundanceValue = params.data.value;
                            const formattedAbundance = abundanceValue.toFixed(4);
                            const name = params.name.replace(/_unclassified$/, '').replace(/_uncultured$/, '');
                            
                            return `
                                <div style="text-align:center; font-weight:bold; margin-bottom:5px; color:#002060;">
                                    Genus
                                </div>
                                <div style="text-align:center; font-weight:bold; margin-bottom:10px; color:#002060;">
                                    ${name}
                                </div>
                                <div style="text-align:center; color:#002060;">
                                    ${formattedAbundance}
                                </div>
                            `;
                        } else {
                            const source = params.data.source.replace(/_unclassified$/, '').replace(/_uncultured$/, '');
                            const target = params.data.target.replace(/_unclassified$/, '').replace(/_uncultured$/, '');
                            const correlation = params.data.originalCorr;
                            const correlationType = correlation >= 0 ? 'Positive' : 'Negative';
                            return `
                                <div style="text-align:center; padding:5px; color:#002060;">
                                    <div style="margin-bottom:5px;">${source} > ${target}</div>
                                    <div>Spearman Correlation: ${correlation.toFixed(3)}</div>
                                    <div>${correlationType} Correlation</div>
                                </div>
                            `;
                        }
                    }
                },
                legend: {
                    data: categories.map(cat => cat.name),
                    orient: 'horizontal',
                    top: 50,
                    itemWidth: 15,
                    itemHeight: 10,
                    textStyle: {
                        fontSize: 12,
                        fontWeight: '900',
                        color: '#002060'
                    }
                },
                animationDuration: 1500,
                animationEasingUpdate: 'quinticInOut',
                series: [{
                    name: 'Microbial Network',
                    type: 'graph',
                    layout: 'none', // 使用自定义布局
                    data: filteredNodes,
                    links: networkData.links.map(link => {
                        // 找到源节点和目标节点
                        const sourceNode = networkData.nodes.find(node => node.id === link.source);
                        const targetNode = networkData.nodes.find(node => node.id === link.target);
                        
                        // 确保找到了节点
                        if (!sourceNode || !targetNode) return link;
                        
                        // 确定相关系数的正负
                        let originalCorr = link.value;
                        
                        // 检查link.originalCorr是否已经存在，如果存在则使用它
                        if (link.originalCorr !== undefined) {
                            originalCorr = link.originalCorr;
                        } else {
                            // 根据lineStyle判断正负相关
                            if (link.lineStyle) {
                                if (link.lineStyle.color && (link.lineStyle.color.includes('255, 64, 64') || link.lineStyle.color.includes('#e67e22'))) {
                                    originalCorr = -Math.abs(originalCorr);
                                } else {
                                    originalCorr = Math.abs(originalCorr);
                                }
                            }
                        }
                        
                        // 确保相关系数的绝对值大于等于0.5
                        // 注意：后端API已经做了过滤，此处为前端二次确认
                        if (Math.abs(originalCorr) < 0.5) {
                            return null; // 返回null将在后续filter步骤中被过滤掉
                        }
                        
                        // 确定是否为正相关
                        const isPositive = originalCorr >= 0;
                        
                        // 计算连线的起点和终点与圆心的角度
                        const sourceAngle = sourceNode.angle || 0;
                        const targetAngle = targetNode.angle || 0;
                        
                        // 计算连线样式
                        const lineStyle = {
                            width: 0.5 + Math.abs(originalCorr) * 3.5, // 线宽直接根据相关系数确定
                            color: isPositive ? '#3498db' : '#e67e22' // 蓝色和橙色
                        };
                        
                        // 计算曲率
                        const angleDiff = Math.abs(sourceAngle - targetAngle);
                        
                        // 计算连线曲率 - 角度差越大，曲率越大
                        if (angleDiff > Math.PI) {
                            lineStyle.curveness = 0.3;
                        } else if (angleDiff > Math.PI/2) {
                            lineStyle.curveness = 0.2;
                        } else if (angleDiff > Math.PI/4) {
                            lineStyle.curveness = 0.1;
                        } else {
                            lineStyle.curveness = 0;
                        }
                        
                        return {
                            ...link,
                            value: Math.abs(originalCorr),
                            originalCorr: originalCorr, // 保存原始相关系数（带正负号）
                            lineStyle: lineStyle
                        };
                    }).filter(link => link !== null), // 过滤掉相关系数小于0.5的连接
                    categories: categories,
                    center: ['50%', '50%'], // 确保图表居中
                    zoom: 0.6,              // 设置初始缩放比例为60%
                    roam: true,
                    focusNodeAdjacency: true,
                    draggable: true, // 允许拖拽节点
                    cursor: 'pointer',
                    label: {
                        show: false  // 禁用ECharts原有的标签显示
                    },
                    edgeLabel: {
                        show: false
                    },
                    edgeSymbol: ['none', 'none'],
                    symbolSize: function(value, params) {
                        // 如果节点已经有预定义的symbolSize，使用预定义的值
                        if (params.data && params.data.symbolSize !== undefined) {
                            return params.data.symbolSize * 0.5; // 缩小到原来的一半
                        }
                        
                        // 根据相对丰度计算大小，使用非线性映射使差异更加明显
                        const baseSize = 30;   // 基础大小翻倍 (11.2 * 2)
                        const maxSize = 40;    // 最大尺寸相应调整 (16.8 * 2)
                        const sizeValue = value > 1 ? value : value * 100;
                        return baseSize + Math.min(maxSize - baseSize, Math.sqrt(sizeValue) * 3.36); // 调整系数
                    },
                    itemStyle: {
                        borderWidth: 1,
                        borderColor: '#fff',
                        shadowColor: 'rgba(0, 0, 0, 0.15)',
                        shadowBlur: 3
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 3
                        },
                        label: {
                            show: false,  // 禁用高亮时的标签显示
                            fontSize: 12,
                            fontWeight: 'bold'
                        },
                        itemStyle: {
                            shadowColor: 'rgba(0, 0, 0, 0.5)',
                            shadowBlur: 5
                        }
                    }
                }],
                graphic: [
                    {
                        type: 'text',
                        right: '5%',
                        bottom: '10%',
                        style: {
                            text: [
                                'Node: genus',
                                'Node Number: relative abundance (%)',
                                'Edge: genus correlation',
                                'Spearman correlation coefficent >= 0.5',
                                '',  // 空行作为分隔
                                'Tips:',
                                'Blue edges: Positive correlation',
                                'Orange edges: Negative correlation'
                            ].join('\n'),
                            fontSize: 14,  // 减小整体字体大小
                            fontWeight: '600',  // 稍微减小字重
                            fill: '#002060',
                            lineHeight: 20,  // 适当减小行高
                            textBorderColor: 'white',
                            textBorderWidth: 2,
                            textShadowColor: 'rgba(255, 255, 255, 0.8)',
                            textShadowBlur: 2,
                            align: 'right',
                            rich: {
                                Tips: {
                                    color: '#ff6b6b',  // 使用淡红色
                                    fontSize: 15,  // Tips稍大一点但比原来小
                                    fontWeight: 'bold',
                                    padding: [0, 0, 3, 0]
                                },
                                BlueEdges: {
                                    color: '#3498db',
                                    fontWeight: 'bold'
                                },
                                OrangeEdges: {
                                    color: '#e67e22',
                                    fontWeight: 'bold'
                                }
                            }
                        },
                        z: 100
                    }
                ]
            };
            
            myChart.hideLoading();
            myChart.setOption(option, true);
            
            // 在图表渲染完成后创建自定义的径向标签
            renderRadialLabels(connectedNodeIds);
            
            // 添加点击事件监听器
            myChart.on('click', function(params) {
                if (params.dataType === 'node') {
                    highlightNode(params.data.id);
                    // 等待高亮效果完成后重新渲染标签
                    setTimeout(function() {
                        renderRadialLabels(connectedNodeIds);
                    }, 200);
                }
            });
            
            // 添加拖动和缩放事件监听
            myChart.on('dataZoom', function() {
                renderRadialLabels(connectedNodeIds);
            });
            
            myChart.on('graphRoam', function() {
                // 在拖动时使用节点的初始角度重新渲染标签
                networkData.nodes.forEach(node => {
                    // 确保使用初始角度
                    node.angle = node.initialAngle;
                });
                renderRadialLabels(connectedNodeIds);
            });
            
            // 监听鼠标拖拽开始、拖拽过程和拖拽结束
            myChart.getZr().on('mousemove', debounce(function() {
                // 在拖动时使用节点的初始角度重新渲染标签
                networkData.nodes.forEach(node => {
                    node.angle = node.initialAngle;
                });
                renderRadialLabels(connectedNodeIds);
            }, 50));
            
            myChart.getZr().on('mouseup', function() {
                networkData.nodes.forEach(node => {
                    node.angle = node.initialAngle;
                });
                renderRadialLabels(connectedNodeIds);
            });
            
            myChart.getZr().on('mousedown', function() {
                networkData.nodes.forEach(node => {
                    node.angle = node.initialAngle;
                });
                renderRadialLabels(connectedNodeIds);
            });
        }
        
        // 函数：创建自定义径向标签
        function renderRadialLabels(connectedNodeIds) {
            if (!networkData || !networkData.nodes) return;
            
            // 获取ECharts容器
            const container = document.getElementById('network-container');
            const echartContainer = container.querySelector('.echarts-container') || container.firstElementChild;
            if (!echartContainer) return;
            
            // 计算中心点
            const width = echartContainer.clientWidth;
            const height = echartContainer.clientHeight;
            const centerX = width / 2;
            const centerY = height / 2;
            
            // 移除旧的标签层
            let labelLayer = document.getElementById('radial-labels-layer');
            if (labelLayer) {
                labelLayer.remove();
            }
            
            // 创建新的标签层
            labelLayer = document.createElement('div');
            labelLayer.id = 'radial-labels-layer';
            labelLayer.style.position = 'absolute';
            labelLayer.style.top = '0';
            labelLayer.style.left = '0';
            labelLayer.style.width = '100%';
            labelLayer.style.height = '100%';
            labelLayer.style.pointerEvents = 'none';
            labelLayer.style.zIndex = '100';
            echartContainer.appendChild(labelLayer);
            
            // 获取图表坐标转换
            const convertToPixel = myChart.convertToPixel.bind(myChart, {seriesIndex: 0});
            
            // 计算和创建每个节点的径向标签
            networkData.nodes.filter(node => connectedNodeIds.has(node.id)).forEach(node => {
                // 获取节点像素位置
                const nodePos = convertToPixel([node.x, node.y]);
                if (!nodePos) return;
                
                const nodeX = nodePos[0];
                const nodeY = nodePos[1];
                
                // 使用节点原始的角度信息，而不是根据当前位置计算
                const angle = node.angle || Math.atan2(nodeY - centerY, nodeX - centerX);
                
                // 标签文本 - 处理文本去掉后缀
                const text = node.name.replace(/_unclassified$/, '').replace(/_uncultured$/, '');
                
                // 计算节点大小和标签位置
                const nodeSize = node.symbolSize || 12;
                
                // 计算标签位置
                const labelDistance = nodeSize * 0.5; // 距离节点更近
                
                // 创建标签元素
                const label = document.createElement('div');
                label.style.position = 'absolute';
                label.style.whiteSpace = 'nowrap';
                label.style.color = '#002060';
                label.style.fontSize = '12px';
                label.style.fontWeight = 'normal';
                label.style.textShadow = '0 0 2px white, 0 0 2px white, 0 0 2px white, 0 0 2px white';
                label.style.pointerEvents = 'none';
                label.textContent = text;
                
                // 使用节点的原始角度计算旋转角度
                let rotateDeg = (node.angle || angle) * 180 / Math.PI;
                
                // 根据角度调整文字位置和对齐方式
                if (rotateDeg > 90 && rotateDeg < 270) {
                    // 左半边的文字
                    rotateDeg += 180; // 旋转180度确保文字不会倒置
                    label.style.transformOrigin = 'right center';
                    label.style.textAlign = 'right';
                    // 将文字放在节点左侧，并右对齐
                    label.style.right = (width - nodeX + labelDistance) + 'px';
                    label.style.left = 'auto';
                } else {
                    // 右半边的文字
                    label.style.transformOrigin = 'left center';
                    label.style.textAlign = 'left';
                    // 将文字放在节点右侧，并左对齐
                    label.style.left = (nodeX + labelDistance) + 'px';
                    label.style.right = 'auto';
                }
                
                // 垂直居中并保持原始旋转角度
                label.style.top = nodeY + 'px';
                label.style.transform = `translate(0, -50%) rotate(${rotateDeg}deg)`;
                
                // 添加到标签层
                labelLayer.appendChild(label);
            });
        }
        
        // 重设图表大小时重新渲染标签
        window.addEventListener('resize', function() {
            if (myChart) {
                myChart.resize();
                // 等待图表重绘完成后渲染标签
                setTimeout(function() {
                    // 重新生成connectedNodeIds
                    const connectedNodeIds = new Set();
                    if (networkData && networkData.links) {
                        networkData.links.forEach(link => {
                            connectedNodeIds.add(link.source);
                            connectedNodeIds.add(link.target);
                        });
                    }
                    renderRadialLabels(connectedNodeIds);
                }, 200);
            }
        });
        
        // 高亮显示节点及其连接
        function highlightNode(nodeId) {
            // 找到与该节点相关的所有连接
            const relatedLinks = networkData.links.filter(link => 
                link.source === nodeId || link.target === nodeId
            );
            
            // 找到与该节点直接连接的所有节点ID
            const relatedNodeIds = new Set();
            relatedNodeIds.add(nodeId); // 添加自身
            relatedLinks.forEach(link => {
                relatedNodeIds.add(link.source);
                relatedNodeIds.add(link.target);
            });
            
            // 设置非相关节点为暗色
            const grayStyle = {
                opacity: 0.15,
                borderColor: '#ccc'
            };
            const normalStyle = {
                opacity: 1,
                borderColor: '#fff'
            };
            const highlightStyle = {
                opacity: 1,
                borderColor: '#fff',
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 15
            };
            
            // 更新节点样式
            myChart.setOption({
                series: [{
                    name: 'Microbial Network',
                    data: networkData.nodes.map(node => {
                        if (node.id === nodeId) {
                            // 突出显示选中的节点
                            return {
                                ...node,
                                symbolSize: node.symbolSize ? node.symbolSize * 1.3 : undefined,
                                itemStyle: highlightStyle,
                                z: 10 // 确保在最上层显示
                            };
                        } else if (relatedNodeIds.has(node.id)) {
                            // 相关节点保持正常显示
                            return {
                                ...node,
                                itemStyle: normalStyle
                            };
                        } else {
                            // 其他节点变暗
                            return {
                                ...node,
                                itemStyle: grayStyle
                            };
                        }
                    })
                }]
            });
            
            // 更新连接样式
            myChart.setOption({
                series: [{
                    name: 'Microbial Network',
                    links: networkData.links.map(link => {
                        const isRelated = link.source === nodeId || link.target === nodeId;
                        
                        if (isRelated) {
                            // 高亮相关连接
                            return {
                                ...link,
                                lineStyle: {
                                    ...link.lineStyle,
                                    opacity: 1,
                                    width: (link.lineStyle?.width || 2) * 1.5,
                                    shadowBlur: 5,
                                    shadowColor: 'rgba(0, 0, 0, 0.2)'
                                }
                            };
                        } else {
                            // 淡化其他连接
                            return {
                                ...link,
                                lineStyle: {
                                    ...link.lineStyle,
                                    opacity: 0.1,
                                    shadowBlur: 0
                                }
                            };
                        }
                    })
                }]
            });
            
            // 更新自定义标签显示
            const labelLayer = document.getElementById('radial-labels-layer');
            if (labelLayer) {
                // 获取所有标签元素
                const labels = labelLayer.querySelectorAll('div');
                
                // 遍历标签
                let nodeIndex = 0;
                for (let i = 0; i < labels.length; i++) {
                    // 获取对应节点
                    const node = networkData.nodes[nodeIndex++];
                    if (!node) continue;
                    
                    if (node.id === nodeId) {
                        // 高亮选中节点的标签
                        labels[i].style.fontWeight = 'bold';
                        labels[i].style.fontSize = '14px';
                        labels[i].style.color = '#000';
                        labels[i].style.opacity = '1';
                    } else if (relatedNodeIds.has(node.id)) {
                        // 相关节点的标签
                        labels[i].style.fontWeight = 'normal';
                        labels[i].style.fontSize = '12px';
                        labels[i].style.color = '#000';
                        labels[i].style.opacity = '1';
                    } else {
                        // 淡化其他标签
                        labels[i].style.opacity = '0.2';
                    }
                }
            } else {
                // 如果标签层不存在，重新渲染标签
                setTimeout(renderRadialLabels, 200);
            }
        }
        
        // 事件监听
        document.getElementById('networkType').addEventListener('change', function() {
            const type = this.value;
            const projectSelect = document.getElementById('projectID');
            projectSelect.innerHTML = '';
            
            // 获取项目并确保无重复
            const projects = type === 'bacteria' ? [...new Set(bacteriaProjects)] : [...new Set(fungiProjects)];
            console.log(`选择了${type}类型，加载${projects.length}个项目`);
            
            projects.forEach(project => {
                const option = document.createElement('option');
                option.value = project;
                option.textContent = project;
                projectSelect.appendChild(option);
            });
            
            // 自动加载第一个项目
            if (projects.length > 0) {
                initChart();
                loadNetworkData(type, 'genus', projects[0]);
            }
        });

        // Project Selection的change事件监听
        document.getElementById('projectID').addEventListener('change', function() {
            const type = document.getElementById('networkType').value;
            const projectId = this.value;
            if (projectId) {
                initChart();
                loadNetworkData(type, 'genus', projectId);
            }
        });
        
        // 缩放按钮点击事件
        document.getElementById('zoomInBtn').addEventListener('click', function() {
            if (myChart) {
                const option = myChart.getOption();
                const zoom = option.series[0].zoom || 1;
                myChart.setOption({
                    series: [{
                        zoom: zoom * 1.2
                    }]
                });
                
                // 重新生成connectedNodeIds
                const connectedNodeIds = new Set();
                if (networkData && networkData.links) {
                    networkData.links.forEach(link => {
                        connectedNodeIds.add(link.source);
                        connectedNodeIds.add(link.target);
                    });
                }
                setTimeout(function() {
                    renderRadialLabels(connectedNodeIds);
                }, 100);
            }
        });
        
        document.getElementById('zoomOutBtn').addEventListener('click', function() {
            if (myChart) {
                const option = myChart.getOption();
                const zoom = option.series[0].zoom || 1;
                myChart.setOption({
                    series: [{
                        zoom: zoom * 0.8
                    }]
                });
                
                // 重新生成connectedNodeIds
                const connectedNodeIds = new Set();
                if (networkData && networkData.links) {
                    networkData.links.forEach(link => {
                        connectedNodeIds.add(link.source);
                        connectedNodeIds.add(link.target);
                    });
                }
                setTimeout(function() {
                    renderRadialLabels(connectedNodeIds);
                }, 100);
            }
        });
        
        document.getElementById('downloadBtn').addEventListener('click', function() {
            if (myChart) {
                // 获取当前项目和类型信息用于文件名
                const type = document.getElementById('networkType').value;
                const projectId = document.getElementById('projectID').value;
                const timestamp = new Date().toISOString().split('T')[0];
                const filename = `microbe_network_${type}_${projectId}_${timestamp}.png`;
                
                // 通过ECharts API下载高质量PNG
                const url = myChart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,
                    backgroundColor: '#fff'
                });
                
                // 创建下载链接并触发
                const link = document.createElement('a');
                link.download = filename;
                link.href = url;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        });
        
        // Reset按钮点击事件
        document.getElementById('resetBtn').addEventListener('click', function() {
            if (myChart && networkData) {
                // 重置图表到初始状态
                myChart.dispatchAction({
                    type: 'restore'
                });
                
                // 重新设置选项以确保布局正确恢复
                networkData.nodes.forEach(node => {
                    // 恢复节点到原始角度位置
                    if (node.initialAngle !== undefined) {
                        node.angle = node.initialAngle;
                    }
                    // 恢复节点样式
                    node.itemStyle = {
                        opacity: 1,
                        borderColor: '#fff'
                    };
                });
                
                // 重置连接样式
                networkData.links.forEach(link => {
                    // 确保使用正确的originalCorr值
                    // 如果originalCorr为undefined，尝试从lineStyle中推断
                    if (link.originalCorr === undefined && link.lineStyle) {
                        if (link.lineStyle.color && link.lineStyle.color.includes('230, 126, 34')) {
                            link.originalCorr = -Math.abs(link.value || 0.3);
                        } else {
                            link.originalCorr = Math.abs(link.value || 0.3);
                        }
                    }
                    
                    const isPositive = link.originalCorr >= 0;
                    link.lineStyle = {
                        width: 0.5 + Math.abs(link.originalCorr) * 3.5,
                        color: isPositive ? 'rgba(52, 152, 219, 0.8)' : 'rgba(230, 126, 34, 0.8)'
                    };
                });
                
                // 更新图表
                renderNetwork(document.getElementById('projectID').value);
                
                // 通知用户
                const toast = document.createElement('div');
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.left = '50%';
                toast.style.transform = 'translateX(-50%)';
                toast.style.padding = '10px 20px';
                toast.style.backgroundColor = 'rgba(0, 32, 96, 0.8)';
                toast.style.color = 'white';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '9999';
                toast.textContent = 'The view has been reset';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transition = 'opacity 0.5s';
                    setTimeout(() => document.body.removeChild(toast), 500);
                }, 2000);
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置下拉菜单的样式
            const styleSelects = function() {
                const selects = document.querySelectorAll('.form-select');
                selects.forEach(select => {
                    select.style.borderColor = '#002060';
                    select.style.color = '#002060';
                    select.style.fontWeight = '600';
                });
                
                // 设置标签样式
                const labels = document.querySelectorAll('.form-label');
                labels.forEach(label => {
                    label.style.fontWeight = '600';
                    label.style.color = '#002060';
                });

                // 调整控制面板布局
                setTimeout(() => {
                    const controlPanel = document.querySelector('.control-panel');
                    if (controlPanel) {
                        const row = controlPanel.querySelector('.row');
                        if (row) {
                            // 调整列宽和顺序
                            const cols = row.querySelectorAll('.col-md-4, .col-md-3, .col-md-1');
                            cols.forEach((col, index) => {
                                if (index === 0 || index === 1) {
                                    col.className = 'col-md-3';
                                } else if (index === 2) {
                                    col.className = 'col-md-2';
                                    const compareBtn = col.querySelector('#compareBtn');
                                    if (compareBtn) {
                                        compareBtn.style.fontWeight = '900';
                                    }
                                } else if (index === 3) {
                                    col.className = 'col-md-4 text-end';
                                    const btnGroup = col.querySelector('.d-flex');
                                    if (btnGroup) {
                                        btnGroup.style.justifyContent = 'flex-end';
                                    }
                                }
                            });
                        }
                    }
                }, 0);
                
                // 设置下拉菜单的选项样式
                const styleOptions = document.createElement('style');
                styleOptions.textContent = `
                    .form-select option { 
                        font-weight:600;
                    }
                    .form-select option:checked { 
                        background-color: #002060 !important;
                        color: white !important;
                    }
                    .form-select:focus {
                        border-color: #002060;
                        box-shadow: 0 0 0 0.2rem rgba(0, 32, 96, 0.25);
                    }
                    .btn-outline-primary {
                        color: #002060;
                        border-color: #002060;
                    }
                    .btn-outline-primary:hover {
                        background-color: #002060;
                        color: white;
                    }
                    .btn-primary {
                        background-color: #002060;
                        border-color: #002060;
                    }
                `;
                document.head.appendChild(styleOptions);
            };
            
            // 调用设置下拉菜单样式的函数
            styleSelects();
            
            initChart();
            
            // 初始化页面
            console.log('开始初始化页面...');
            const initType = document.getElementById('networkType').value;
            
            // 确保项目列表无重复
            const initProjects = initType === 'bacteria' ? 
                [...new Set(bacteriaProjects)] : 
                [...new Set(fungiProjects)];
            
            console.log(`初始化${initType}项目，数量: ${initProjects.length}`);
            
            // 填充项目下拉框
            const projectSelect = document.getElementById('projectID');
            projectSelect.innerHTML = ''; // 清空已有选项
            
            initProjects.forEach(project => {
                const option = document.createElement('option');
                option.value = project;
                option.textContent = project;
                projectSelect.appendChild(option);
            });
            
            console.log('项目下拉菜单选项数量:', projectSelect.options.length);
            
            // 检查是否有可用项目，如果没有则尝试通过API获取
            if (initProjects.length > 0) {
                console.log('加载第一个项目:', initProjects[0]);
                initChart();
                loadNetworkData(initType, 'genus', initProjects[0]);
            } else {
                console.warn('没有可用的项目数据，使用测试数据');
                // 使用测试数据
                if (initType === 'bacteria') {
                    bacteriaProjects = ['SRID-1', 'SRID-2', 'SRID-3'];
                    projectSelect.innerHTML = '';
                    bacteriaProjects.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project;
                        option.textContent = project;
                        projectSelect.appendChild(option);
                    });
                    console.log('添加测试数据后重新加载:', bacteriaProjects[0]);
                    initChart();
                    loadNetworkData(initType, 'genus', bacteriaProjects[0]);
                } else {
                    fungiProjects = ['SRID-4', 'SRID-5'];
                    projectSelect.innerHTML = '';
                    fungiProjects.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project;
                        option.textContent = project;
                        projectSelect.appendChild(option);
                    });
                    console.log('添加测试数据后重新加载:', fungiProjects[0]);
                    initChart();
                    loadNetworkData(initType, 'genus', fungiProjects[0]);
                }
            }
            
            // 响应窗口大小变化
            window.addEventListener('resize', debounce(function() {
                if (myChart) {
                    myChart.resize();
                }
            }, 250));
        });
        
        // 防抖函数，避免频繁渲染造成性能问题
        function debounce(fn, delay) {
            let timer = null;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timer);
                timer = setTimeout(function() {
                    fn.apply(context, args);
                }, delay);
            };
        }
        
        // 对比模式相关变量
        let myChart1 = null;
        let myChart2 = null;
        let networkData1 = null;
        let networkData2 = null;
        
        // 对比按钮点击事件
        document.getElementById('compareBtn').addEventListener('click', function() {
            // 获取当前网络类型和项目列表
            const type = document.getElementById('networkType').value;
            const projects = type === 'bacteria' ? bacteriaProjects : fungiProjects;
            
            // 填充下拉菜单选项
            const firstSelect = document.getElementById('firstProject');
            const secondSelect = document.getElementById('secondProject');
            
            firstSelect.innerHTML = '';
            secondSelect.innerHTML = '';
            
            projects.forEach(project => {
                // 第一个下拉框
                const option1 = document.createElement('option');
                option1.value = project;
                option1.textContent = project;
                firstSelect.appendChild(option1);
                
                // 第二个下拉框
                const option2 = document.createElement('option');
                option2.value = project;
                option2.textContent = project;
                secondSelect.appendChild(option2);
            });
            
            // 如果有两个或更多项目，默认选择第一个和第二个项目
            if (projects.length >= 2) {
                firstSelect.selectedIndex = 0;
                secondSelect.selectedIndex = 1;
            }
            
            // 显示模态框
            const compareModal = new bootstrap.Modal(document.getElementById('compareModal'));
            compareModal.show();
        });
        
        // 开始对比按钮点击事件
        document.getElementById('startCompareBtn').addEventListener('click', function() {
            const type = document.getElementById('networkType').value;
            const firstProject = document.getElementById('firstProject').value;
            const secondProject = document.getElementById('secondProject').value;
            
            // 获取模态框实例
            const compareModal = bootstrap.Modal.getInstance(document.getElementById('compareModal'));
            
            // 在隐藏模态框之前，将焦点移动到一个安全的元素
            document.getElementById('compareBtn').focus();
            
            // 隐藏模态框
            compareModal.hide();
            
            // 隐藏主网络容器
            document.getElementById('network-container').style.display = 'none';
            
            // 显示对比容器
            document.getElementById('compare-container').style.display = 'block';
            
            // 设置标题
            document.getElementById('network-title-1').textContent = `Project: ${firstProject}`;
            document.getElementById('network-title-2').textContent = `Project: ${secondProject}`;
            
            // 初始化对比图表
            initCompareCharts();
            
            // 加载两个项目的数据
            loadCompareData(type, 'genus', firstProject, secondProject);
        });
        
        // 退出对比模式
        document.getElementById('exitCompareBtn').addEventListener('click', function() {
            // 隐藏对比容器
            document.getElementById('compare-container').style.display = 'none';
            
            // 显示主网络容器
            document.getElementById('network-container').style.display = 'block';
            
            // 清理对比图表
            if (myChart1) {
                myChart1.dispose();
                myChart1 = null;
            }
            if (myChart2) {
                myChart2.dispose();
                myChart2 = null;
            }
            
            networkData1 = null;
            networkData2 = null;
        });
        
        // 初始化对比图表
        function initCompareCharts() {
            if (myChart1) {
                myChart1.dispose();
            }
            if (myChart2) {
                myChart2.dispose();
            }
            
            myChart1 = echarts.init(document.getElementById('network-container-1'));
            myChart2 = echarts.init(document.getElementById('network-container-2'));
            
            myChart1.showLoading({
                text: 'Loading network data...',
                color: '#002060',
                textColor: '#333',
                maskColor: 'rgba(255, 255, 255, 0.8)'
            });
            
            myChart2.showLoading({
                text: 'Loading network data...',
                color: '#002060',
                textColor: '#333',
                maskColor: 'rgba(255, 255, 255, 0.8)'
            });
        }
        
        // 加载对比数据
        function loadCompareData(type, level, project1, project2) {
            // 从项目ID中提取数字部分
            const project1Number = project1.replace(/^SRID-/, '');
            const project2Number = project2.replace(/^SRID-/, '');
            
            // 使用数字部分构建URL
            const url1 = `../../api/get_network_data.php?type=${type}&level=${level}&project=SRID-${project1Number}`;
            const url2 = `../../api/get_network_data.php?type=${type}&level=${level}&project=SRID-${project2Number}`;
            
            console.log('对比视图API请求URLs:', { url1, url2 });
            
            // 加载第一个项目数据
            fetch(url1)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data || !data.nodes || !data.links) {
                        throw new Error('API返回的数据格式不正确或不完整');
                    }
                    networkData1 = data;
                    renderCompareNetwork(myChart1, networkData1, project1, 1);
                })
                .catch(error => {
                    console.error('加载第一个项目数据失败:', error);
                    myChart1.hideLoading();
                    myChart1.setOption({
                        title: {
                            text: '加载失败',
                            subtext: `错误: ${error.message}`,
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#ff4444' }
                        }
                    });
                });
            
            // 加载第二个项目数据
            fetch(url2)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data || !data.nodes || !data.links) {
                        throw new Error('API返回的数据格式不正确或不完整');
                    }
                    networkData2 = data;
                    renderCompareNetwork(myChart2, networkData2, project2, 2);
                })
                .catch(error => {
                    console.error('加载第二个项目数据失败:', error);
                    myChart2.hideLoading();
                    myChart2.setOption({
                        title: {
                            text: '加载失败',
                            subtext: `错误: ${error.message}`,
                            left: 'center',
                            top: 'center',
                            textStyle: { color: '#ff4444' }
                        }
                    });
                });
        }
        
        // 渲染对比网络图
        function renderCompareNetwork(chart, data, projectId, chartIndex) {
            if (!data) return;
            
            // 为每个类别分配唯一颜色
            const categories = data.categories;
            categories.forEach(category => {
                category.itemStyle = {
                    color: getRandomColor(category.name)
                };
            });
            
            // 过滤节点，移除没有连接的孤立节点
            const connectedNodeIds = new Set();
            
            // 找出所有有连接的节点ID
            data.links.forEach(link => {
                connectedNodeIds.add(link.source);
                connectedNodeIds.add(link.target);
            });
            
            // 过滤掉没有连接的节点
            const filteredNodes = data.nodes.filter(node => connectedNodeIds.has(node.id));
            
            console.log(`对比图${chartIndex} - 过滤前节点数量: ${data.nodes.length}, 过滤后: ${filteredNodes.length}`);
            
            // 更新原始数据，确保后续操作使用正确的数据
            if (chartIndex === 1) {
                networkData1.nodes = filteredNodes;
            } else {
                networkData2.nodes = filteredNodes;
            }
            
            // 设置圆形环形布局
            const radius = Math.min(chart.getWidth(), chart.getHeight()) * 0.125;
            const centerX = chart.getWidth() / 2;
            const centerY = chart.getHeight() / 2;
            
            // 所有节点均匀分布在圆周上
            const allNodes = filteredNodes;
            const totalNodes = allNodes.length;
            const angleStep = (Math.PI * 2) / totalNodes;
            
            // 计算每个节点的位置
            allNodes.forEach((node, i) => {
                const angle = i * angleStep;
                
                node.x = centerX + radius * Math.cos(angle);
                node.y = centerY + radius * Math.sin(angle);
                node.fixed = true;
                
                node.angle = angle;
                node.initialAngle = angle;
                
                let rotate = angle * 180 / Math.PI;
                if (rotate > 90 && rotate < 270) {
                    rotate += 180;
                }
                rotate = rotate % 360;
                node.labelRotate = rotate;
            });
            
            // 获取当前项目信息
            const metadata = projectMetadata[projectId] || {};
            
            const option = {
                title: {
                    subtext: document.getElementById('networkType').options[document.getElementById('networkType').selectedIndex].text,
                    top: 'top',
                    left: 'center',
                    subtextStyle: {
                        color: '#002060',
                        fontSize: 15,
                        fontWeight: '900'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#ccc',
                    borderWidth: 1,
                    padding: 15,
                    textStyle: {
                        color: '#002060',
                        fontSize: 16,
                        fontWeight: '900'
                    },
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            const abundanceValue = params.data.value;
                            const formattedAbundance = abundanceValue.toFixed(4);
                            const name = params.name.replace(/_unclassified$/, '').replace(/_uncultured$/, '');
                            
                            return `
                                <div style="text-align:center; font-weight:bold; color:#002060;">
                                    ${name}: ${formattedAbundance}
                                </div>
                            `;
                        } else {
                            const source = params.data.source.replace(/_unclassified$/, '').replace(/_uncultured$/, '');
                            const target = params.data.target.replace(/_unclassified$/, '').replace(/_uncultured$/, '');
                            const correlation = params.data.originalCorr;
                            const correlationType = correlation >= 0 ? 'Positive' : 'Negative';
                            return `
                                <div style="text-align:center; color:#002060;">
                                    ${source} > ${target}<br>
                                    Correlation: ${correlation.toFixed(3)} (${correlationType})
                                </div>
                            `;
                        }
                    }
                },
                legend: {
                    data: categories.map(cat => cat.name),
                    orient: 'horizontal',
                    top: 30,
                    left: 'center',
                    itemWidth: 15,
                    itemHeight: 10,
                    textStyle: {
                        fontSize: 14,
                        fontWeight: 'bold',
                        color: '#002060'
                    },
                    selectedMode: 'multiple',
                    selector: ['all', 'inverse']
                },
                animationDuration: 1500,
                animationEasingUpdate: 'quinticInOut',
                series: [{
                    name: 'Microbial Network',
                    type: 'graph',
                    layout: 'none',
                    data: filteredNodes,
                    links: data.links.map(link => {
                        // 找到源节点和目标节点
                        const sourceNode = data.nodes.find(node => node.id === link.source);
                        const targetNode = data.nodes.find(node => node.id === link.target);
                        
                        if (!sourceNode || !targetNode) return link;
                        
                        // 确保相关系数值正确
                        let originalCorr = parseFloat(link.value);
                        
                        if (link.originalCorr !== undefined) {
                            originalCorr = parseFloat(link.originalCorr);
                        }
                        
                        // 确保相关系数的绝对值大于等于0.5
                        // 注意：后端API已经做了过滤，此处为前端二次确认
                        if (Math.abs(originalCorr) < 0.5) {
                            return null; // 返回null将在后续filter步骤中被过滤掉
                        }
                        
                        // 明确设置正负相关的颜色
                        const lineStyle = {
                            width: Math.max(1, 0.5 + Math.abs(originalCorr) * 3.5),
                            color: originalCorr >= 0 ? '#3498db' : '#e67e22',
                            opacity: 0.8
                        };
                        
                        return {
                            ...link,
                            value: Math.abs(originalCorr),
                            originalCorr: originalCorr,
                            lineStyle: lineStyle
                        };
                    }).filter(link => link !== null), // 过滤掉相关系数小于0.5的连接
                    categories: categories,
                    center: ['50%', '50%'],
                    zoom: 0.7,
                    roam: true,
                    focusNodeAdjacency: true,
                    draggable: true,
                    cursor: 'pointer',
                    label: {
                        show: false
                    },
                    edgeLabel: {
                        show: false
                    },
                    edgeSymbol: ['none', 'none'],
                    symbolSize: function(value, params) {
                        if (params.data && params.data.symbolSize !== undefined) {
                            return params.data.symbolSize * 0.4;
                        }
                        
                        const baseSize = 20;
                        const maxSize = 30;
                        const sizeValue = value > 1 ? value : value * 100;
                        return baseSize + Math.min(maxSize - baseSize, Math.sqrt(sizeValue) * 2.5);
                    },
                    itemStyle: {
                        borderWidth: 1,
                        borderColor: '#fff',
                        shadowColor: 'rgba(0, 0, 0, 0.15)',
                        shadowBlur: 3
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 3
                        },
                        label: {
                            show: false
                        },
                        itemStyle: {
                            shadowColor: 'rgba(0, 0, 0, 0.5)',
                            shadowBlur: 5
                        }
                    }
                }],
                graphic: [
                    {
                        type: 'text',
                        right: '5%',
                        bottom: '10%',
                        style: {
                            text: [
                                'Node: genus',
                                'Node Number: relative abundance (%)',
                                'Edge: genus correlation',
                                'Spearman correlation coefficent >= 0.5',
                                '',  // 空行作为分隔
                                'Tips:',
                                'Blue edges: Positive correlation',
                                'Orange edges: Negative correlation'
                            ].join('\n'),
                            fontSize: 14,  // 减小整体字体大小
                            fontWeight: '600',  // 稍微减小字重
                            fill: '#002060',
                            lineHeight: 20,  // 适当减小行高
                            textBorderColor: 'white',
                            textBorderWidth: 2,
                            textShadowColor: 'rgba(255, 255, 255, 0.8)',
                            textShadowBlur: 2,
                            align: 'right',
                            rich: {
                                Tips: {
                                    color: '#ff6b6b',  // 使用淡红色
                                    fontSize: 15,  // Tips稍大一点但比原来小
                                    fontWeight: 'bold',
                                    padding: [0, 0, 3, 0]
                                },
                                BlueEdges: {
                                    color: '#3498db',
                                    fontWeight: 'bold'
                                },
                                OrangeEdges: {
                                    color: '#e67e22',
                                    fontWeight: 'bold'
                                }
                            }
                        },
                        z: 100
                    }
                ]
            };
            
            chart.hideLoading();
            chart.setOption(option, true);
            
            // 添加点击事件
            chart.on('click', function(params) {
                if (params.dataType === 'node') {
                    highlightCompareNode(chart, data, params.data.id, chartIndex);
                }
            });
            
            // 处理窗口调整大小
            window.addEventListener('resize', debounce(function() {
                if (chart) {
                    chart.resize();
                }
            }, 250));
        }
        
        // 高亮对比网络中的节点
        function highlightCompareNode(chart, data, nodeId, chartIndex) {
            // 找到与该节点相关的所有连接
            const relatedLinks = data.links.filter(link => 
                link.source === nodeId || link.target === nodeId
            );
            
            // 找到与该节点直接连接的所有节点ID
            const relatedNodeIds = new Set();
            relatedNodeIds.add(nodeId);
            relatedLinks.forEach(link => {
                relatedNodeIds.add(link.source);
                relatedNodeIds.add(link.target);
            });
            
            // 设置样式
            const grayStyle = {
                opacity: 0.15,
                borderColor: '#ccc'
            };
            const normalStyle = {
                opacity: 1,
                borderColor: '#fff'
            };
            const highlightStyle = {
                opacity: 1,
                borderColor: '#fff',
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 15
            };
            
            // 更新节点样式
            chart.setOption({
                series: [{
                    name: 'Microbial Network',
                    data: data.nodes.map(node => {
                        if (node.id === nodeId) {
                            return {
                                ...node,
                                symbolSize: node.symbolSize ? node.symbolSize * 1.3 : undefined,
                                itemStyle: highlightStyle,
                                z: 10
                            };
                        } else if (relatedNodeIds.has(node.id)) {
                            return {
                                ...node,
                                itemStyle: normalStyle
                            };
                        } else {
                            return {
                                ...node,
                                itemStyle: grayStyle
                            };
                        }
                    })
                }]
            });
            
            // 更新连接样式
            chart.setOption({
                series: [{
                    name: 'Microbial Network',
                    links: data.links.map(link => {
                        const isRelated = link.source === nodeId || link.target === nodeId;
                        
                        if (isRelated) {
                            return {
                                ...link,
                                lineStyle: {
                                    ...link.lineStyle,
                                    opacity: 1,
                                    width: (link.lineStyle?.width || 2) * 1.5,
                                    shadowBlur: 5,
                                    shadowColor: 'rgba(0, 0, 0, 0.2)'
                                }
                            };
                        } else {
                            return {
                                ...link,
                                lineStyle: {
                                    ...link.lineStyle,
                                    opacity: 0.1,
                                    shadowBlur: 0
                                }
                            };
                        }
                    })
                }]
            });
            
            // 高亮同名节点在另一个图中的位置
            const otherChart = chartIndex === 1 ? myChart2 : myChart1;
            const otherData = chartIndex === 1 ? networkData2 : networkData1;
            
            if (otherChart && otherData) {
                // 找到对应节点
                const matchingNode = otherData.nodes.find(node => node.id === nodeId);
                if (matchingNode) {
                    // 在另一个图中高亮对应节点
                    highlightCompareNode(otherChart, otherData, nodeId, chartIndex === 1 ? 2 : 1);
                }
            }
        }

        // 添加对比视图按钮的事件处理
        document.addEventListener('DOMContentLoaded', function() {
            // 第一个图表的控制按钮
            document.getElementById('zoomInBtn1').addEventListener('click', function() {
                if (myChart1) {
                    const option = myChart1.getOption();
                    const zoom = option.series[0].zoom || 1;
                    myChart1.setOption({
                        series: [{
                            zoom: zoom * 1.2
                        }]
                    });
                }
            });

            document.getElementById('zoomOutBtn1').addEventListener('click', function() {
                if (myChart1) {
                    const option = myChart1.getOption();
                    const zoom = option.series[0].zoom || 1;
                    myChart1.setOption({
                        series: [{
                            zoom: zoom * 0.8
                        }]
                    });
                }
            });

            document.getElementById('downloadBtn1').addEventListener('click', function() {
                if (myChart1) {
                    const type = document.getElementById('networkType').value;
                    const projectId = document.getElementById('firstProject').value;
                    const timestamp = new Date().toISOString().split('T')[0];
                    const filename = `microbe_network_${type}_${projectId}_${timestamp}.png`;
                    
                    const url = myChart1.getDataURL({
                        type: 'png',
                        pixelRatio: 2,
                        backgroundColor: '#fff'
                    });
                    
                    const link = document.createElement('a');
                    link.download = filename;
                    link.href = url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 显示下载成功提示
                    const toast = document.createElement('div');
                    toast.style.position = 'fixed';
                    toast.style.bottom = '20px';
                    toast.style.left = '50%';
                    toast.style.transform = 'translateX(-50%)';
                    toast.style.padding = '10px 20px';
                    toast.style.backgroundColor = 'rgba(0, 32, 96, 0.8)';
                    toast.style.color = 'white';
                    toast.style.borderRadius = '4px';
                    toast.style.zIndex = '9999';
                    toast.textContent = 'Network image downloaded successfully';
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transition = 'opacity 0.5s';
                        setTimeout(() => document.body.removeChild(toast), 500);
                    }, 2000);
                }
            });

            document.getElementById('resetBtn1').addEventListener('click', function() {
                if (myChart1 && networkData1) {
                    renderCompareNetwork(myChart1, networkData1, document.getElementById('firstProject').value, 1);
                    
                    // 显示重置成功提示
                    const toast = document.createElement('div');
                    toast.style.position = 'fixed';
                    toast.style.bottom = '20px';
                    toast.style.left = '50%';
                    toast.style.transform = 'translateX(-50%)';
                    toast.style.padding = '10px 20px';
                    toast.style.backgroundColor = 'rgba(0, 32, 96, 0.8)';
                    toast.style.color = 'white';
                    toast.style.borderRadius = '4px';
                    toast.style.zIndex = '9999';
                    toast.textContent = 'Network view has been reset';
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transition = 'opacity 0.5s';
                        setTimeout(() => document.body.removeChild(toast), 500);
                    }, 2000);
                }
            });

            // 第二个图表的控制按钮
            document.getElementById('zoomInBtn2').addEventListener('click', function() {
                if (myChart2) {
                    const option = myChart2.getOption();
                    const zoom = option.series[0].zoom || 1;
                    myChart2.setOption({
                        series: [{
                            zoom: zoom * 1.2
                        }]
                    });
                }
            });

            document.getElementById('zoomOutBtn2').addEventListener('click', function() {
                if (myChart2) {
                    const option = myChart2.getOption();
                    const zoom = option.series[0].zoom || 1;
                    myChart2.setOption({
                        series: [{
                            zoom: zoom * 0.8
                        }]
                    });
                }
            });

            document.getElementById('downloadBtn2').addEventListener('click', function() {
                if (myChart2) {
                    const type = document.getElementById('networkType').value;
                    const projectId = document.getElementById('secondProject').value;
                    const timestamp = new Date().toISOString().split('T')[0];
                    const filename = `microbe_network_${type}_${projectId}_${timestamp}.png`;
                    
                    const url = myChart2.getDataURL({
                        type: 'png',
                        pixelRatio: 2,
                        backgroundColor: '#fff'
                    });
                    
                    const link = document.createElement('a');
                    link.download = filename;
                    link.href = url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 显示下载成功提示
                    const toast = document.createElement('div');
                    toast.style.position = 'fixed';
                    toast.style.bottom = '20px';
                    toast.style.left = '50%';
                    toast.style.transform = 'translateX(-50%)';
                    toast.style.padding = '10px 20px';
                    toast.style.backgroundColor = 'rgba(0, 32, 96, 0.8)';
                    toast.style.color = 'white';
                    toast.style.borderRadius = '4px';
                    toast.style.zIndex = '9999';
                    toast.textContent = 'Network image downloaded successfully';
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transition = 'opacity 0.5s';
                        setTimeout(() => document.body.removeChild(toast), 500);
                    }, 2000);
                }
            });

            document.getElementById('resetBtn2').addEventListener('click', function() {
                if (myChart2 && networkData2) {
                    renderCompareNetwork(myChart2, networkData2, document.getElementById('secondProject').value, 2);
                    
                    // 显示重置成功提示
                    const toast = document.createElement('div');
                    toast.style.position = 'fixed';
                    toast.style.bottom = '20px';
                    toast.style.left = '50%';
                    toast.style.transform = 'translateX(-50%)';
                    toast.style.padding = '10px 20px';
                    toast.style.backgroundColor = 'rgba(0, 32, 96, 0.8)';
                    toast.style.color = 'white';
                    toast.style.borderRadius = '4px';
                    toast.style.zIndex = '9999';
                    toast.textContent = 'Network view has been reset';
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transition = 'opacity 0.5s';
                        setTimeout(() => document.body.removeChild(toast), 500);
                    }, 2000);
                }
            });

            // 添加窗口大小变化监听
            window.addEventListener('resize', function() {
                if (myChart1) {
                    myChart1.resize();
                }
                if (myChart2) {
                    myChart2.resize();
                }
            });
        });

        // 添加模态框事件监听器
        document.getElementById('compareModal').addEventListener('hidden.bs.modal', function () {
            // 模态框隐藏后，将焦点移动到触发按钮
            document.getElementById('compareBtn').focus();
        });

        document.getElementById('compareModal').addEventListener('shown.bs.modal', function () {
            // 模态框显示后，将焦点移动到第一个可聚焦元素
            document.getElementById('firstProject').focus();
        });
    </script>
</body>
</html>
<?php
// 关闭数据库连接
$conn->close();
?> 