@media (min-width: 768px){
    .news-input{
        width:70%;
    }
}

body::before{
    display: block;
    content: '';
    height: 50px;
}

/* 内容容器样式 */
.content-container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Study.php 页面样式 */
.text-start {
    margin: 100px 0;  /* 上下边距设置为100px */
    padding-top: 25px;  /* 增加上方内边距25px */
    width: 100%;
}

/* 第一个 text-start 不需要上边距 */
.text-start:first-child {
    margin-top: 30px;  /* 保持原有的上边距 */
    padding-top: 0;  /* 第一个模块不需要额外的上边距 */
}

/* 最后一个 text-start 不需要下边距 */
.text-start:last-child {
    margin-bottom: 30px;
}

/* 标题样式统一 */
.text-start h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 1rem;
    padding: 0;
}

/* 分割线样式统一 */
.text-start hr {
    margin: 1rem 0;
    opacity: 0.8;
    width: 100%;
}

/* 确保内容区域宽度一致 */
.text-dark {
    width: 100%;
    padding: 0;
}

/* 表格部分样式调整 */
.table-container, 
.table-responsive {
    width: 100%;
    margin: 0;
    padding: 0;
}

.table-container {
    overflow-x: auto;
}

/* 增加表格之间的间距 */
.table-responsive {
    margin-bottom: 80px !important;  /* 使用 !important 覆盖可能的插件样式 */
}

/* 最后一个表格不需要底部间距 */
.table-responsive:last-child {
    margin-bottom: 0 !important;
}

/* 图表相关样式 */
.chart-section {
    margin-bottom: 60px;
}

.pie-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 400px;
}

#stackChart {
    width: 100%;
    height: 400px;
    margin-top: 60px;
    min-height: 300px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .chart-section {
        margin-bottom: 40px;
    }
    
    #pieChart, #stackChart {
        height: 350px;
    }
}

@media (max-width: 768px) {
    #pieChart, #stackChart {
        height: 300px;
    }
    
    .sample-id-main-title {
        margin: -35px 0 20px;
    }

    .table-responsive {
        margin-bottom: 60px !important;
    }
}

/* 为 bootstrap-table 之后的标题添加上边距 */
div[class*="table"] + .text-start,
.fixed-table-pagination + .text-start,
[data-toggle="table"] + .text-start {
    margin-top: 40px;
}

/* 为 Soil Physicochemical properties 标题添加强制上边距 */
#title-3,
.text-start h4#title-3 {
    margin-top: 50px !important;  /* 使用 !important 确保样式生效 */
}

/* 隐藏说明文字 */
.hidden-text {
    color: white !important;
}


