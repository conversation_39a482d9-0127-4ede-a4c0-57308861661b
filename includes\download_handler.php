<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入数据库连接
require_once(__DIR__ . '/config/db_connection.php');

// 获取参数
$study_id = isset($_GET['study_id']) ? $_GET['study_id'] : '';
$data_type = isset($_GET['data_type']) ? strtoupper($_GET['data_type']) : '16S';
$table_type = isset($_GET['table_type']) ? $_GET['table_type'] : 'otu';

// 确保data_type是有效值
if (!in_array($data_type, ['16S', 'ITS'])) {
    $data_type = '16S';
}

// 转换study_id格式 (从HRMA00001到SRID-1)
if (preg_match('/HRMA0*(\d+)/', $study_id, $matches)) {
    $number = intval($matches[1]);
    $study_id = sprintf("SRID-%d", $number);
}

// 记录转换后的参数
$log_file = __DIR__ . '/download_log.txt';
$log_message = date('Y-m-d H:i:s') . " - 下载请求开始\n";
$log_message .= "原始参数: study_id={$_GET['study_id']}, data_type={$_GET['data_type']}, table_type={$table_type}\n";
$log_message .= "转换后的参数: study_id={$study_id}, data_type={$data_type}\n";
file_put_contents($log_file, $log_message, FILE_APPEND);

// 验证参数
if (empty($study_id)) {
    die("错误：缺少Study ID参数");
}

// 验证数据库连接
if (!isset($conn) || !$conn) {
    die("错误：数据库连接失败");
}

try {
    if ($table_type === 'otu') {
        // 获取OTU数据
        $sql = "SELECT id, study_id, data_type, otu_id, sample_id, abundance 
               FROM otu 
               WHERE study_id = ? AND data_type = ?
               ORDER BY id";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("准备OTU查询失败: " . $conn->error);
        }
        
        // 记录查询参数
        file_put_contents($log_file, "执行OTU查询，参数：study_id={$study_id}, data_type={$data_type}\n", FILE_APPEND);
        
        $stmt->bind_param("ss", $study_id, $data_type);
        $stmt->execute();
        $result = $stmt->get_result();

        // 记录查询结果
        file_put_contents($log_file, "查询结果行数：" . $result->num_rows . "\n", FILE_APPEND);

        if ($result->num_rows === 0) {
            // 查询示例数据
            $sql_check = "SELECT DISTINCT study_id, data_type FROM otu ORDER BY study_id LIMIT 5";
            $result_check = $conn->query($sql_check);
            $examples = [];
            while ($row = $result_check->fetch_assoc()) {
                $examples[] = "study_id: " . $row['study_id'] . ", data_type: " . $row['data_type'];
            }
            throw new Exception("未找到相关OTU数据。数据库中的示例记录：\n" . implode("\n", $examples));
        }

        // 构建TSV文件内容
        $output = "id\tstudy_id\tdata_type\totu_id\tsample_id\tabundance\n";
        
        while ($row = $result->fetch_assoc()) {
            $output .= implode("\t", [
                $row['id'],
                $row['study_id'],
                $row['data_type'],
                $row['otu_id'],
                $row['sample_id'],
                $row['abundance']
            ]) . "\n";
        }

    } else { // taxonomy表
        // 获取分类数据
        $sql = "SELECT id, study_id, data_type, otu_id, taxa_name, confidence 
               FROM taxonomy 
               WHERE study_id = ? AND data_type = ?
               ORDER BY id";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("准备分类查询失败: " . $conn->error);
        }
        
        // 记录查询参数
        file_put_contents($log_file, "执行Taxonomy查询，参数：study_id={$study_id}, data_type={$data_type}\n", FILE_APPEND);
        
        $stmt->bind_param("ss", $study_id, $data_type);
        if (!$stmt->execute()) {
            throw new Exception("执行分类查询失败: " . $stmt->error);
        }
        $result = $stmt->get_result();

        // 记录查询结果
        file_put_contents($log_file, "查询结果行数：" . $result->num_rows . "\n", FILE_APPEND);

        if ($result->num_rows === 0) {
            // 查询示例数据
            $sql_check = "SELECT DISTINCT study_id, data_type FROM taxonomy ORDER BY study_id LIMIT 5";
            $result_check = $conn->query($sql_check);
            $examples = [];
            while ($row = $result_check->fetch_assoc()) {
                $examples[] = "study_id: " . $row['study_id'] . ", data_type: " . $row['data_type'];
            }
            throw new Exception("未找到相关分类数据。数据库中的示例记录：\n" . implode("\n", $examples));
        }

        // 构建TSV文件内容
        $output = "id\tstudy_id\tdata_type\totu_id\ttaxa_name\tconfidence\n";
        
        while ($row = $result->fetch_assoc()) {
            $output .= implode("\t", [
                $row['id'],
                $row['study_id'],
                $row['data_type'],
                $row['otu_id'],
                $row['taxa_name'],
                $row['confidence']
            ]) . "\n";
        }
    }

    // 设置下载头
    $filename = sprintf("%s_%s_%s.tsv", $study_id, $data_type, $table_type);
    header('Content-Type: text/tab-separated-values; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');

    // 输出文件内容
    echo $output;

    // 记录下载成功
    file_put_contents($log_file, "下载成功完成\n", FILE_APPEND);

} catch (Exception $e) {
    // 记录错误
    file_put_contents($log_file, "下载失败: " . $e->getMessage() . "\n", FILE_APPEND);
    die("处理数据时发生错误：" . $e->getMessage());
}

// 关闭数据库连接
$conn->close();
?>