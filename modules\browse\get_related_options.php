<?php
// Include database connection file
$conn = require_once(__DIR__ . '/../../includes/config/db_connection.php');

header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get and validate parameters
$selected_fields = array_filter(explode(',', $_POST['selected_field'] ?? ''));
$selected_values = array_filter(explode(',', $_POST['selected_value'] ?? ''));
$target_field = $_POST['target_field'] ?? '';

// Input validation
if (empty($target_field)) {
    echo json_encode([
        'error' => 'Missing target field',
        'debug' => ['post_data' => $_POST]
    ]);
    exit;
}

try {
    // 构建查询条件
    $conditions = [];
    $params = [];
    $types = '';
    
    // 确保字段和值的数量匹配
    if (count($selected_fields) === count($selected_values)) {
        for ($i = 0; $i < count($selected_fields); $i++) {
            $field = trim($selected_fields[$i]);
            $value = trim($selected_values[$i]);
            
            if (!empty($field) && !empty($value)) {
                // 使用FIND_IN_SET或LIKE模式更高效地匹配
                if (strpos($value, ';') !== false) {
                    $valueArray = array_map('trim', explode(';', $value));
                    $orConditions = [];
                    foreach ($valueArray as $val) {
                        if (!empty($val)) {
                            // 使用 REGEXP 或 FIND_IN_SET 更高效
                            $orConditions[] = "`{$field}` = ? OR `{$field}` LIKE ?";
                            $params[] = $val;
                            $params[] = "%;{$val};%";
                            $types .= 'ss';
                        }
                    }
                    if (!empty($orConditions)) {
                        $conditions[] = "(" . implode(" OR ", $orConditions) . ")";
                    }
                } else {
                    // 使用参数绑定避免SQL注入并提高性能
                    $conditions[] = "(`{$field}` = ? OR `{$field}` LIKE ?)";
                    $params[] = $value;
                    $params[] = "%;{$value};%";
                    $types .= 'ss';
                }
            }
        }
    }

    // 构建优化的查询 - 使用索引提示和预处理语句
    $sql = "SELECT DISTINCT `{$target_field}` FROM `advanced search` USE INDEX (idx_{$target_field})";
    
    // 添加条件（如果有）
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    } else {
        $sql .= " WHERE 1=1";
    }
    
    // 添加非空条件和排序
    $sql .= " AND `{$target_field}` IS NOT NULL AND `{$target_field}` != '' ORDER BY `{$target_field}` LIMIT 1000";

    // 准备并执行查询
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        throw new Exception("Failed to prepare statement: " . $conn->error);
    }

    // 绑定参数（如果有）
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    // 执行查询
    if (!$stmt->execute()) {
        throw new Exception("Failed to execute statement: " . $stmt->error);
    }

    $result = $stmt->get_result();
    if ($result === false) {
        throw new Exception("Failed to get result: " . $stmt->error);
    }

    // 获取结果并缓存处理
    $options = [];
    while ($row = $result->fetch_assoc()) {
        $value = $row[$target_field];
        if (!empty($value)) {
            // 处理可能包含多个值的字段
            if (strpos($value, ';') !== false) {
                $values = array_map('trim', explode(';', $value));
                $options = array_merge($options, array_filter($values));
            } else {
                $options[] = trim($value);
            }
        }
    }

    // 高效去重和排序
    $options = array_values(array_unique(array_filter($options)));
    natcasesort($options); // 使用自然排序，对于中文和混合字符更友好
    $options = array_values($options); // 重新索引

    // 返回结果
    echo json_encode([
        'success' => true,
        'options' => $options,
        'count' => count($options)
    ]);

} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage(),
        'debug' => [
            'selected_fields' => $selected_fields,
            'selected_values' => $selected_values,
            'target_field' => $target_field
        ]
    ]);
}

$conn->close();
?> 