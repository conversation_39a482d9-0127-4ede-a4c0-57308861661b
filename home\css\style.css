/**
 * style.css - HRMA网站的主要样式文件
 * 定义了网站的所有自定义样式，包括布局、颜色、间距等
 * 作者: HRMA开发团队
 * 版本: 1.0.0
 * 最后更新: 2023/05/10
 */

/* 覆盖Bootstrap的img-fluid样式 */
.img-fluid {
    height: auto;
}

/* ===== 1. 基础样式 ===== */
body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif; /* 设置字体优先级 */
    line-height: 1.6; /* 设置行高，提高可读性 */
    color: #333333; /* 设置默认文字颜色 */
}

/* 标题样式统一设置 */
h2, h3, h4, h5, h6 {
    font-weight: 600; /* 设置字体粗细 */
    color: #002060; /* 设置标题颜色为深蓝色 */
}

/* 内容区域通用样式 */
section {
    padding: 3rem 0; /* 设置上下内边距 */
    position: relative; /* 设置定位方式 */
}

/* 背景矩形样式 */
.bg-section {
    background-color: #F9FAFB;
    border-radius: 12px;
    padding: 4rem 2rem;
    margin: 4rem auto;
    width: 50%;
    min-width: 50%;
    max-width: 2200px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
}

/* 内容居中容器 */
.content-container {
    max-width: 1600px; /* 进一步增加最大宽度 */
    margin: 0 auto; /* 居中显示 */
    padding: 0 20px; /* 增加内边距 */
}

/* ===== 2. 搜索框样式 ===== */
/* 搜索框布局 */
.input-group {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
    border-radius: 25px; /* 增加圆角 */
    overflow: hidden; /* 隐藏溢出内容 */
    border: 3px solid #002060; /* 添加边框 */
    max-width: 900px; /* 增加最大宽度 */
    margin: 0 auto; /* 居中显示 */
    padding: 0.2rem; /* 增加内边距 */
    background-color: #F9FAFB; /* 设置背景色 */
    display: flex; /* 使用flexbox布局 */
    align-items: center; /* 垂直居中对齐 */
    height: 55px; /* 增加高度 */
}

/* 搜索框输入区域样式 */
.input-group .form-control {
    border: none; /* 移除边框 */
    background-color: #F9FAFB; /* 设置背景色 */
    padding: 0.2rem 1.5rem; /* 增加内边距 */
    box-shadow: none; /* 移除阴影 */
    height: 100%; /* 设置高度为100% */
    font-size: 1.1rem; /* 增大字体 */
}

/* 搜索按钮样式 */
.input-group .btn-primary {
    border: none; /* 移除边框 */
    background-color: #F9FAFB; /* 设置背景色 */
    padding: 0.2rem 1.5rem; /* 增加内边距 */
    display: flex; /* 使用flexbox布局 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    height: 100%; /* 设置高度为100% */
}

/* 搜索按钮悬停样式 */
.input-group .btn-primary:hover {
    background-color: #F9FAFB; /* 保持背景色不变 */
}

/* 搜索字体样式 */
section h2 {
    margin-bottom: 2rem; /* 增加下外边距 */
    color: #002060; /* 设置标题颜色 */
    font-size: 2rem; /* 增大字体 */
}

/* 搜索图片样式 */
.input-group .btn-primary img {
    width: 25px; /* 增大宽度 */
    height: 25px; /* 增大高度 */
    object-fit: contain; /* 保持图片比例 */
    display: block; /* 设置为块级元素 */
    margin: auto; /* 自动边距实现居中 */
}

/* ===== 4. About HRMA部分样式 ===== */
/* About HRMA布局 */
.about-content {
    display: flex; /* 使用flexbox布局 */
    flex-wrap: wrap; /* 允许换行 */
    gap: 2rem; /* 设置元素间距 */
    align-items: stretch; /* 拉伸对齐以确保等高 */
    justify-content: center; /* 居中对齐 */
}

/* 词云图容器样式 */
.wordcloud-container {
    flex: 1 1 300px; /* 弹性伸缩，基础宽度300px */
    min-width: 300px; /* 最小宽度 */
    max-width: 500px; /* 最大宽度 */
    display: flex; /* 使用flexbox布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
}

/* 文本内容容器样式 */
.text-content {
    flex: 1 1 300px; /* 弹性伸缩，基础宽度300px */
    min-width: 300px; /* 最小宽度 */
    max-width: 500px; /* 最大宽度 */
    display: flex; /* 使用flexbox布局 */
    flex-direction: column; /* 垂直排列 */
    justify-content: center; /* 垂直居中 */
    padding: 1rem; /* 内边距 */
}

/* About HRMA字体样式 */
.text-content p {
    margin: 0; /* 移除外边距 */
    line-height: 1.8; /* 设置行高 */
    font-size: 1rem; /* 固定字体大小 */
}

/* About HRMA图片样式 */
.wordcloud-container img {
    width: 100%; /* 宽度填充容器 */
    height: auto; /* 自动计算高度 */
    object-fit: contain; /* 保持图片比例 */
    border-radius: 8px; /* 添加圆角 */
}

/* ===== 5. Introduction部分样式 ===== */
.introduction-section {
    display: flex;
    flex-direction: column;
    min-height: 600px;
}

.introduction-section .row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: stretch;
    margin: 0;
    padding: 0;
    height: 80%;
}

/* 内容容器样式 */
.content-box {
    border-radius: 10px;
    padding: 0.6rem;
    display: flex;
    flex-direction: column;
}

/* 右侧面板样式 */
.right-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
}

.right-panel .content-box {
    height: calc(50% - 0.3rem);
    margin-bottom: 0.6rem;
    padding: 1.2rem;
    min-height: 0;
}

.right-panel .content-box:last-child {
    margin-bottom: 0;
}

/* 左侧面板样式 */
.introduction-section .col-md-5 {
    height: 100%;
}

.introduction-section .col-md-5 .content-box {
    height: 100%;
}

/* 标题样式 */
.introduction-section h4 {
    color: #002060;
    font-weight: 600;
    font-size: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 1.5rem;
    text-align: center;
    width: 100%;
}

/* 图标容器通用样式 */
.introduction-section .col-3,
.introduction-section .col-2dot4 {
    padding: 0.3rem !important;
    margin-bottom: 0.3rem !important;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.introduction-section .col-3 {
    width: 25%;
    flex: 0 0 25%;
    max-width: 25%;
}

.introduction-section .col-2dot4 {
    width: 20%;
    flex: 0 0 20%;
    max-width: 20%;
}

/* 分类图标样式 */
.introduction-section .text-center img {
    width: 60px !important;
    height: 60px !important;
    margin-bottom: 0.2rem !important;
    object-fit: contain !important;
}

/* 图标文字样式 */
.introduction-section .text-center p {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
    padding: 0.1rem;
    text-align: center;
    min-height: 2.2em;
    display: flex;
    align-items: center;
    justify-content: center;
    word-break: normal;
    word-wrap: break-word;
    font-weight: 600;
    color: #333;
}

/* 图标容器样式调整 */
.right-panel .row {
    height: auto;
    align-items: flex-start;
}

.right-panel .col-2dot4 {
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .introduction-section {
        min-height: auto;
    }

    .right-panel .content-box {
        height: auto;
        margin-bottom: 1rem;
    }

    .introduction-section .col-md-5 .content-box {
        height: auto;
        margin-bottom: 1rem;
    }

    .introduction-section h4 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }
    
    .introduction-section .text-center img {
        width: 60px !important;
        height: 60px !important;
    }
    
    .introduction-section .text-center p {
        font-size: 0.8rem;
        min-height: 2.2em;
    }
}

/* ===== 6. Data Statistics部分样式 ===== */
/* Data Statistics布局 */
.data-statistics {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    padding: 2rem 5rem;
    margin: 2rem auto;
    max-width: 1600px;
    width: 100%;
}

/* 统计项容器样式 */
.statistics-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    text-align: center;
    min-width: 0;
}

/* 统计图片容器 */
.statistics-item .img-container {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

/* 统计图片样式 */
.statistics-item img {
    width: 70px;
    height: 70px;
    object-fit: contain;
}

/* 统计文字说明样式 */
.statistics-item .text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #002060;
    text-align: center;
    margin: 0.5rem 0;
    line-height: 1.4;
    width: 100%;
}

/* Data Statistics响应式调整 */
@media (min-resolution: 1.1dppx) {
    .data-statistics {
        flex-wrap: wrap;
        justify-content: center;
        gap: 2rem;
    }
    
    .statistics-item {
        flex: 0 0 calc(50% - 1rem);
        padding: 1rem;
    }
}

@media (min-resolution: 1.5dppx) {
    .data-statistics {
        gap: 1.5rem;
    }
    
    .statistics-item {
        flex: 0 0 calc(50% - 0.75rem);
    }
}

@media (max-width: 768px) {
    .data-statistics {
        padding: 1rem 2rem;
    }
    
    .statistics-item img {
        width: 60px;
        height: 60px;
    }
    
    .statistics-item .text {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .data-statistics {
        padding: 1rem;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .statistics-item {
        width: 100%;
    }
}

/* ===== 7. 标签部分样式 ===== */
/* 标签字体样式 */
.d-flex h3 {
    color: #002060; /* 设置标题颜色 */
}

/* 标签图片样式 */
.d-flex img {
    margin-right: 0.5rem; /* 设置右侧外边距 */
}

/* ===== 8. 响应式样式 ===== */
/* 缩放大于100%时的调整 */
@media (min-resolution: 1.1dppx) {
    .bg-section {
        width: 60%;
        min-width: 50%;
    }
    
    /* About HRMA响应式 */
    .about-content {
        flex-direction: column; /* 垂直布局 */
        align-items: center; /* 居中对齐 */
        gap: 2rem; /* 保持间距 */
    }
    
    .wordcloud-container,
    .text-content {
        min-height: auto; /* 自动高度 */
        width: 100%; /* 占满宽度 */
        max-width: 500px; /* 最大宽度限制 */
    }
    
    .wordcloud-container img {
        max-height: 400px; /* 控制图片最大高度 */
        max-width: 100%; /* 限制最大宽度 */
        width: auto; /* 宽度自适应 */
        margin: 0 auto; /* 居中显示 */
        display: block; /* 确保图片能够居中 */
    }
    
    .text-content {
        text-align: center; /* 文本居中 */
    }
    
    /* Introduction响应式 */
    .introduction-section .text-center img {
        width: 100px !important; /* 与非响应式部分保持一致 */
        height: 100px !important; /* 与非响应式部分保持一致 */
    }
}

/* 缩放大于150%时的调整 */
@media (min-resolution: 1.5dppx) {
    .bg-section {
        width: 70%;
        min-width: 50%;
    }
    
    /* About HRMA响应式 */
    .wordcloud-container {
        max-width: 450px; /* 减小最大宽度 */
    }
    
    .wordcloud-container img {
        max-height: 350px; /* 进一步控制图片高度 */
    }
    
    /* Introduction响应式 */
    .introduction-section .text-center img {
        width: 90px !important; /* 稍微减小 */
        height: 90px !important; /* 稍微减小 */
    }
}

/* 平板设备的样式调整 */
@media (max-width: 768px) {
    /* About HRMA响应式 */
    .about-content {
        flex-direction: column; /* 垂直布局 */
        align-items: center; /* 居中对齐 */
    }

    .wordcloud-container,
    .text-content {
        min-height: auto; /* 自动高度 */
        width: 100%; /* 占满宽度 */
        max-width: 500px; /* 最大宽度限制 */
    }
    
    /* Introduction响应式 */
    .introduction-section .text-center img {
        width: 50px !important; /* 调整图片尺寸 */
        height: 50px !important; /* 调整图片尺寸 */
    }
}

/* 手机设备的样式调整 */
@media (max-width: 576px) {
    /* About HRMA响应式 */
    .about-content {
        gap: 1rem; /* 减小间距 */
    }
    
    .text-content p {
        font-size: 0.9rem; /* 减小字体大小 */
    }
    
    /* Introduction响应式 */
    .introduction-section .text-center img {
        width: 37px !important; /* 进一步减小图片尺寸 */
        height: 37px !important; /* 进一步减小图片尺寸 */
    }
    
    /* 在手机设备上调整右侧面板布局为两列显示 */
    .introduction-section .col-2dot4 {
        width: 50%; /* 2列布局 */
        flex: 0 0 50%; /* 固定宽度，不伸缩 */
        max-width: 50%; /* 最大宽度限制 */
    }
    
    /* 在手机设备上调整左侧面板布局为两列显示 */
    .introduction-section .col-3 {
        width: 50%; /* 2列布局 */
        flex: 0 0 50%; /* 固定宽度，不伸缩 */
        max-width: 50%; /* 最大宽度限制 */
    }
}

/* ===== 9. 页脚样式 ===== */
footer {
    background-color: #002060; /* 设置背景色 */
    color: white; /* 设置文字颜色 */
    padding: 2rem 0; /* 设置内边距 */
    text-align: center; /* 文本居中 */
}

/* 覆盖Introduction部分的mb-3类 */
.introduction-section .mb-3 {
    margin-bottom: 0 !important;
}

/* 调整中间药材总览图的边距 */
.introduction-section .herb-total-container {
    margin-top: 0; /* 消除负边距 */
}

/* 调整图标高度和大小 */
.introduction-section .text-center img {
    width: 100px !important; /* 进一步增大图片尺寸 */
    height: 100px !important; /* 进一步增大图片尺寸 */
    margin-bottom: 1.2rem !important; /* 增加与文字的间距 */
    object-fit: contain !important; /* 确保图片保持比例 */
}

/* 调整图标文字大小 */
.introduction-section .text-center p {
    font-size: 1.2rem; /* 适当增大字体 */
    line-height: 1.3; /* 调整行高 */
    letter-spacing: 0.01rem; /* 减少字间距，让文字更紧凑 */
    margin: 0;
    padding: 0.2rem 0.5rem; /* 增加左右内边距 */
    text-align: center;
    max-width: 100%;
    min-height: 3.2em; /* 设置最小高度约两行 */
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: none; /* 移除高度限制 */
    overflow: visible;
    word-break: normal; /* 使用正常断词 */
    word-wrap: break-word; /* 允许长词换行 */
}

/* 调整图标容器样式 */
.introduction-section .col-3, .introduction-section .col-2dot4 {
    padding: 1rem !important; /* 增加内边距 */
    margin-bottom: 1.5rem !important; /* 增加底部间距 */
    height: 200px; /* 增大高度 */
}

/* 调整中间药材总览图 */
.herb-total-container {
    height: 320px; /* 设置固定高度 */
    display: flex;
    justify-content: center;
    align-items: center;
}


/* 减少标题的底部边距 */
.introduction-section h4 {
    font-size: 1.35rem; /* 增大50% (0.9rem*1.5) */
    margin-bottom: 0.5rem; /* 增加底部间距 */
}

@media (max-width: 768px) {
    .bg-section {
        width: 95%;
        min-width: 95%;
        padding: 2rem 1rem;
        margin: 2rem auto;
    }

    /* Introduction部分的响应式调整 */
    .introduction-section .row {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        margin: 0 -0.3rem;
    }

    .introduction-section .col-2dot4 {
        flex: 0 0 33.333%;
        max-width: 33.333%;
        padding: 0.3rem !important;
    }

    .introduction-section .text-center {
        margin-bottom: 0.3rem;
    }

    .introduction-section .text-center img {
        width: 40px !important;
        height: 40px !important;
        margin-bottom: 0.2rem !important;
    }

    .introduction-section .text-center p {
        font-size: 0.75rem !important;
        line-height: 1.2;
        min-height: 2.4em;
        margin: 0;
        padding: 0.1rem;
        word-break: break-word;
    }

    .introduction-section h4 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
        padding-bottom: 0.3rem;
    }

    .content-box {
        padding: 1rem 0.5rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .introduction-section .col-2dot4 {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0.25rem !important;
    }

    .introduction-section .text-center img {
        width: 35px !important;
        height: 35px !important;
    }

    .introduction-section .text-center p {
        font-size: 0.7rem !important;
        min-height: 2.8em;
    }

    .content-box {
        padding: 0.8rem 0.3rem;
    }
}



