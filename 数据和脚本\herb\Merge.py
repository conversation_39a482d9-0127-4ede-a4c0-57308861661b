# 导入pandas库，这是处理表格数据的核心工具
import pandas as pd

# ------------------- 文件和列名定义 -------------------
# 源文件：包含详细药草信息的、我们之前生成的CSV文件
source_file_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\HERB数据库数据\HERB_filtered_herbs_by_cn_name.csv"
source_key_column = 'Herb_cn_name'  # 源文件中的索引列（中文名）

# 目标文件：您需要被填充数据的Excel表格
destination_file_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\HERB数据库数据\herb details.xlsx"
destination_key_column = 'Chinese Name' # 目标文件中的索引列（中文名）

# 输出文件：最终生成的、合并了所有信息的新Excel文件
output_file_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\HERB数据库数据\herb_details_filled.xlsx"
# ----------------------------------------------------

print("脚本开始运行...")

try:
    # 1. 首先读取您的目标表格
    print(f"正在读取您的目标表格: {destination_file_path}")
    destination_df = pd.read_excel(destination_file_path)

    # --- 新增功能：清理目标文件中的'Chinese Name'列 (增强版) ---
    print(f"正在预处理目标文件，清理 '{destination_key_column}' 列...")
    # 确保该列是字符串类型，以防有数字等其他类型导致.str访问器报错
    destination_df[destination_key_column] = destination_df[destination_key_column].astype(str)
    
    # 第1步清理：使用正则表达式(regex)移除所有空白字符(包括空格、制表符Tab等)
    # 例如： "  金 银 花	"  会变成 "金银花"
    destination_df[destination_key_column] = destination_df[destination_key_column].str.replace(r'\s+', '', regex=True)
    
    # 第2步清理：移除可能存在的引号
    destination_df[destination_key_column] = destination_df[destination_key_column].str.replace('"', '', regex=False)
    
    print("格式清理完成。")
    # --------------------------------------------------

    # 2. 读取源文件
    print(f"正在读取源文件: {source_file_path}")
    source_df = pd.read_csv(source_file_path)
    print("两个文件均已成功读取。")

    # 3. 执行核心操作：数据合并 (Merge)
    print(f"正在根据清理后的 '{destination_key_column}' 和 '{source_key_column}' 两列进行数据匹配与合并...")
    
    merged_df = pd.merge(
        left=destination_df,      # 左侧表格（使用已清理的版本）
        right=source_df,          # 右侧表格（包含详细信息的源文件）
        left_on=destination_key_column,  # 左侧表格的“钥匙”
        right_on=source_key_column, # 右侧表格的“钥匙”
        how='left'                # 使用“左合并”策略
    )
    print("数据合并完成。")

    # 4. 清理数据：将合并后产生的空白单元格填充为 "NA"
    print("正在将空白单元格填充为 'NA'...")
    merged_df.fillna('NA', inplace=True)

    # 5. 保存最终结果
    merged_df.to_excel(output_file_path, index=False)
    
    print("\n" + "="*50)
    print("🎉 全部任务完成！")
    print(f"✅ 最终结果已成功保存至新文件: {output_file_path}")
    print("="*50)

except FileNotFoundError as e:
    print(f"❌ 错误：找不到文件。请检查下面的文件路径是否正确：\n{e}")
except KeyError as e:
    print(f"❌ 错误：找不到指定的列名 {e}。")
    print(f"请检查 '{source_key_column}' 是否在CSV文件中，以及 '{destination_key_column}' 是否在Excel文件中。")
except Exception as e:
    print(f"❌ 发生未知错误: {e}")