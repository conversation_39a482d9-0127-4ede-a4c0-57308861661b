/* ===== 1. 基础样式 ===== */
body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif; /* 设置字体优先级 */
    line-height: 1.6; /* 设置行高，提高可读性 */
    color: #333333; /* 设置默认文字颜色 */
}
h2, h3, h4, h5, h6 {
    font-weight: 600; /* 设置字体粗细 */
    color: #002060; /* 设置标题颜色为深蓝色 */
}
/* ===== 导航栏样式 ===== */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: #F9FAFB;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

/* 左侧Logo和标题区域 */
.left-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Logo容器样式 */
.logo-container {
    width: 50px;
    height: 50px;
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 标题容器样式 */
.title-container {
    display: flex;
    flex-direction: column;
}

/* HRMA标题样式 */
.title-container h1 {
    font-weight: 700; /* 更粗的字体 */
    color: #002060; /* 深蓝色 */
    margin: 0;
}

/* HRMA副标题样式 */
.title-container p {
    color: #002060; /* 深蓝色 */
    font-weight: 600; /* 中等粗细 */
    font-size: 0.9rem; /* 调整字体大小 */
}

/* 汉堡包按钮样式 */
.hamburger {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 10000; /* 提高汉堡包按钮的层级 */
}

.hamburger span {
    width: 100%;
    height: 2px;
    background: #002060;
    /* transition: all 0.3s linear; */ /* 移除过渡动画 */
}

/* 导航链接区域 */
nav .nav {
    display: flex;
    gap: 1rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

nav .nav-item .nav-link {
    color: #002060;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    /* transition: background-color 0.3s; */ /* 移除背景色过渡动画 */
    font-weight: 600; /* 增加字体粗细 */
    font-size: 1.05rem; /* 稍微增加字体大小 */
    letter-spacing: 0.02em; /* 增加字母间距 */
}

nav .nav-item .nav-link:hover {
    background-color: rgba(0, 32, 96, 0.1);
}

nav .nav-item .nav-link.active {
    background-color: #002060;
    color: white;
    font-weight: 700; /* 激活状态更粗 */
}

/* ===== 页脚样式 ===== */
footer {
    background-color: #002060;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

/* 响应式调整 */
@media (max-width: 1500px) { /* 修改为1500px以适应150%缩放 */
    header {
        padding: 1rem;
    }
    
    .hamburger {
        display: flex;
    }

    nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #F9FAFB;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 9999;
    }

    nav.show {
        display: block;
    }

    nav .nav {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    nav .nav-item {
        width: 100%;
    }

    nav .nav-item .nav-link {
        display: block;
        text-align: center;
        padding: 0.8rem;
        width: 100%;
    }

    /* 汉堡包按钮动画 */
    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}