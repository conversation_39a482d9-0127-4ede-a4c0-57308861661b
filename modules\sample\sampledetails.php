<?php
// 根据您的数据库连接信息进行修改
$servername = "localhost";
$username = "root";
$password = "123456";
$dbname = "soildb";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
mysqli_set_charset($conn, 'utf8');
// 从 URL 参数获取 Sample ID
$sampleID = $_GET['sample_id'];

// 查询 sample 表
$sql_sample = "SELECT * FROM sample WHERE `Sample ID` = '$sampleID'";
$result_sample = $conn->query($sql_sample);

// 检查查询结果是否非空
if ($result_sample !== false && $result_sample->num_rows > 0) {
    $row_study = $result_sample->fetch_assoc();
} else {
    $row_study = null; // 初始化变量
    echo "没有找到样本信息。";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Details</title>
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/style.css">
    <link rel="stylesheet" href="../../public/css/bootstrap-table.min.css">
    <link rel="stylesheet" href="../../public/css/search.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .info-card {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .microbe-section {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }
        .microbe-list {
            flex: 1;
            margin: 0 10px;
        }
        .microbe-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #007bff;
            border-radius: 5px;
            background-color: #e9f7ff;
            margin-bottom: 10px;
            transition: transform 0.2s;
        }
        .microbe-item:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        .microbe-icon {
            margin-right: 10px;
            color: #007bff;
        }
        table th {
            text-align: left;
            width: 180px; /* 控制标签列的宽度 */
            font-size: 18px; /* 增大内容字体 */
        }

        table td {
            text-align: left;
            font-size: 18px; /* 增大内容字体 */
        }

        .info-card {
            width: 100%;
        }

        .row {
            display: flex;
        }

        .col-md-6 {
            width: 50%;
        }

        .table {
            width: 100%;
        }

    </style>
</head>
<body>
    <!--导航栏-->
    <nav class="navbar navbar-expand-lg bg-dark bg-gradient navbar-dark fixed-top">
        <div class="container">
            <a href="../../public/index.php" class="navbar-brand">
                <img src="../../public/img/logo.png" alt="1" width="30" height="24">
                Soil Multimodal Database
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navmenu">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navmenu">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="../../public/index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="../browse/Project.php">Project</a></li>
                    <li class="nav-item"><a class="nav-link" href="Sample.php">Sample</a></li>
                    <li class="nav-item"><a class="nav-link" href="#">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#">Help</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-3 pt-3"> <!-- 减少顶部留白 -->
        <h2 class="mt-4">Sample Information</h2>
        <div class="info-card">
            <div class="row">
            <!-- 左边 -->
            <div class="col-md-6 col-sm-12">
                <table class="table table-borderless">
                    <tr>
                        <th>Sample ID:</th>
                        <td><?php echo htmlspecialchars($row_study['Sample ID']); ?></td>
                    </tr>
                    <tr>
                        <th>Sample Name:</th>
                        <td><?php echo htmlspecialchars($row_study['Sample Name']); ?></td>
                    </tr>
                    <tr>
                        <th>Collection Sites:</th>
                        <td><?php echo htmlspecialchars($row_study['Collection Sites']); ?></td>
                    </tr>
                    <tr>
                        <th>Province:</th>
                        <td><?php echo htmlspecialchars($row_study['Province']); ?></td>
                    </tr>
                    <tr>
                        <th>Country:</th>
                        <td><?php echo htmlspecialchars($row_study['Country']); ?></td>
                    </tr>
                    <tr>
                        <th>Organism:</th>
                        <td><?php echo htmlspecialchars($row_study['Organism']); ?></td>
                    </tr>
                    <tr>
                        <th>Study Name:</th>
                        <td><?php echo htmlspecialchars($row_study['Study Name']); ?></td>
                    </tr>
                    <tr>
                        <th>Latitude:</th>
                        <td><?php echo htmlspecialchars($row_study['Latitude']); ?></td>
                    </tr>
                    <tr>
                        <th>Longitude:</th>
                        <td><?php echo htmlspecialchars($row_study['Longitude']); ?></td>
                    </tr>
                    <tr>
                        <th>Elevation:</th>
                        <td><?php echo htmlspecialchars($row_study['Elevation']); ?></td>
                    </tr>
                </table>
            </div>

            <!-- 右边 -->
            <div class="col-md-6 col-sm-12">
                <table class="table table-borderless">
                    <tr>
                        <th>Age:</th>
                        <td><?php echo htmlspecialchars($row_study['Age']); ?></td>
                    </tr>
                    <tr>
                        <th>PH:</th>
                        <td><?php echo htmlspecialchars($row_study['PH']); ?></td>
                    </tr>
                    <tr>
                        <th>WC(%):</th>
                        <td><?php echo htmlspecialchars($row_study['WC(%)']); ?></td>
                    </tr>
                    <tr>
                        <th>OM(g/kg):</th>
                        <td><?php echo htmlspecialchars($row_study['OM(g/kg)']); ?></td>
                    </tr>
                    <tr>
                        <th>TN(g/kg):</th>
                        <td><?php echo htmlspecialchars($row_study['TN(g/kg)']); ?></td>
                    </tr>
                    <tr>
                        <th>AN(mg/kg):</th>
                        <td><?php echo htmlspecialchars($row_study['AN(mg/kg)']); ?></td>
                    </tr>
                    <tr>
                        <th>TP(g/kg):</th>
                        <td><?php echo htmlspecialchars($row_study['TP(g/kg)']); ?></td>
                    </tr>
                    <tr>
                        <th>AP(mg/kg):</th>
                        <td><?php echo htmlspecialchars($row_study['AP(mg/kg)']); ?></td>
                    </tr>
                    <tr>
                        <th>TK(g/kg):</th>
                        <td><?php echo htmlspecialchars($row_study['TK(g/kg)']); ?></td>
                    </tr>
                    <tr>
                        <th>AK(mg/kg):</th>
                        <td><?php echo htmlspecialchars($row_study['AK(mg/kg)']); ?></td>
                    </tr>
                </table>
            </div>
            </div>
        </div>
    
    
        <h2 class="mt-4">Microbial Information</h2>
        <div class="microbe-section">
            <div class="microbe-list">
                <h3>Probiotic</h3>
                <div class="info-card">
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 1</span>
                    </div>
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 2</span>
                    </div>
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 3</span>
                    </div>
                </div>
            </div>
            <div class="microbe-list">
                <h3>Other Microbes</h3>
                <div class="info-card">
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 4</span>
                    </div>
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 5</span>
                    </div>
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 6</span>
                    </div>
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 7</span>
                    </div>
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 8</span>
                    </div>
                    <div class="microbe-item">
                        <i class="microbe-icon fas fa-bacteria"></i>
                        <span>Microbe 9</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    

    <script src="../../public/js/jquery-3.7.1.min.js"></script>
    <script src="../../public/js/bootstrap.bundle.min.js"></script>
    <script src="../../public/js/bootstrap-table.min.js"></script>
</body>
</html>

