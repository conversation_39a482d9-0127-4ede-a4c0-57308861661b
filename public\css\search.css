body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    margin: 0;
    padding: 0;
}

h1 {
    margin-bottom: 20px;
}

#search-card {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border: none;
}

#search-form {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 5px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
}

button[type="reset"] {
    background-color: #6c757d;
}

button:hover {
    background-color: #0056b3;
}

button[type="reset"]:hover {
    background-color: #5a6268;
}

#year-start, #year-end {
    width: 48%;
}