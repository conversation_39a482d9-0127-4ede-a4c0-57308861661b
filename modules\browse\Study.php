<?php
// 开启页面缓存
$cache_time = 3600; // 1小时
header('Cache-Control: public, max-age='.$cache_time);
header('Expires: '.gmdate('D, d M Y H:i:s', time() + $cache_time).' GMT');

// 开启Gzip压缩
if (extension_loaded('zlib') && !ini_get('zlib.output_compression')) {
    ini_set('zlib.output_compression', 'On');
    ini_set('zlib.output_compression_level', '7');
}

// 设置执行时间和内存限制
ini_set('max_execution_time', 300);
ini_set('memory_limit', '512M');

// 设置会话缓存
session_start();
$session_cache_key = 'study_data_' . (isset($_GET['study_id']) ? $_GET['study_id'] : '');

// 只引入一次数据库连接
require_once(__DIR__ . '/../../includes/config/db_connection.php');
// 确保$conn存在
if (!isset($conn)) {
    die("数据库连接变量不存在，请检查db_connection.php文件");
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 从 URL 参数获取 Study ID
$studyId = isset($_GET['study_id']) ? $_GET['study_id'] : '';

if (empty($studyId)) {
    die("Study ID is required");
}

// 检查数据库连接
if (!$conn) {
    die("Database connection failed");
}

try {
    // 查询 project details 表
    $sql_study = "SELECT * FROM `project details` WHERE `Study ID` = ?";
    $stmt = $conn->prepare($sql_study);
    
    if ($stmt === false) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("s", $studyId);
    $success = $stmt->execute();
    
    if (!$success) {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $result_study = $stmt->get_result();
    $row_study = $result_study->fetch_assoc();
    
    if (!$row_study) {
        throw new Exception("No data found for Study ID: " . htmlspecialchars($studyId));
    }
    
    // 查询 herb 表
    $sql_herb = "SELECT * FROM herb WHERE `Study ID` = ?";
    $stmt_herb = $conn->prepare($sql_herb);
    if ($stmt_herb === false) {
        throw new Exception("Prepare failed for herb query: " . $conn->error);
    }
    
    $stmt_herb->bind_param("s", $studyId);
    $stmt_herb->execute();
    $result_herb = $stmt_herb->get_result();
    $row_herb = $result_herb->fetch_assoc();
    
    // 查询 geo 表
    $sql_geo = "SELECT `Study ID`, `Sample ID`, `Sample Name`, `Collection Sites`, `Province`, `Country`, `Longitude`, `Latitude`, 
    `Elevation (m)`, `Age (year)` FROM geo WHERE `Study ID` = ? ORDER BY `Sample Name`";
    $stmt_geo = $conn->prepare($sql_geo);
    if ($stmt_geo === false) {
        throw new Exception("Prepare failed for geo query: " . $conn->error);
    }
    $stmt_geo->bind_param("s", $studyId);
    $stmt_geo->execute();
    $result_geo = $stmt_geo->get_result();
    
    // 查询 physicochemical 表
    $sql_physicochemical = "SELECT * FROM physicochemical WHERE `Study ID` = ? ORDER BY `Sample Name`";
    $stmt_phys = $conn->prepare($sql_physicochemical);
    if ($stmt_phys === false) {
        throw new Exception("Prepare failed for physicochemical query: " . $conn->error);
    }
    $stmt_phys->bind_param("s", $studyId);
    $stmt_phys->execute();
    $result_physicochemical = $stmt_phys->get_result();
    
    // 查询 primer details 表
    $sql_primer = "SELECT * FROM `primer details` WHERE `Study ID` = ?";
    $stmt_primer = $conn->prepare($sql_primer);
    if ($stmt_primer === false) {
        throw new Exception("Prepare failed for primer details query: " . $conn->error);
    }
    $stmt_primer->bind_param("s", $studyId);
    $stmt_primer->execute();
    $result_primer = $stmt_primer->get_result();
    $row_primer = $result_primer->fetch_assoc();
    
    // 查询 plant details 表
    $sql_plant = "SELECT * FROM `plant details` WHERE `Study ID` = ?";
    $stmt_plant = $conn->prepare($sql_plant);
    if ($stmt_plant === false) {
        throw new Exception("Prepare failed for plant details query: " . $conn->error);
    }
    $stmt_plant->bind_param("s", $studyId);
    $stmt_plant->execute();
    $result_plant = $stmt_plant->get_result();
    $row_plant = $result_plant->fetch_assoc();
    
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

// 检查 16S 和 ITS 数据文件是否存在
$jsonPath16S = "../../public/results/{$studyId}/{$studyId}_16S_taxa.json";
$jsonPathITS = "../../public/results/{$studyId}/{$studyId}_ITS_taxa.json";

$has16S = file_exists($jsonPath16S);
$hasITS = file_exists($jsonPathITS);

if ($has16S) {
    error_log("16S JSON file exists: " . $jsonPath16S);
} else {
    error_log("16S JSON file not found: " . $jsonPath16S);
}

if ($hasITS) {
    error_log("ITS JSON file exists: " . $jsonPathITS);
} else {
    error_log("ITS JSON file not found: " . $jsonPathITS);
}

// 将数据可用性传递给 JavaScript
echo "<script>
    const dataAvailability = {
        has16S: " . ($has16S ? 'true' : 'false') . ",
        hasITS: " . ($hasITS ? 'true' : 'false') . "
    };
</script>";

?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Study Details - MPRAM</title>
    <meta name="description" content="MPRAM - Detailed study information and data visualization for herbal rhizosphere microbiome research">
    <meta name="keywords" content="MPRAM,study details,herbal medicine,rhizosphere,microbiome,research data">
    <meta name="author" content="MPRAM Development Team">
    
    <!-- DNS prefetch -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="../../public/css/bootstrap.min.css" as="style">
    <link rel="preload" href="../../public/js/jquery-3.7.1.min.js" as="script">
    <link rel="preload" href="../../public/js/bootstrap-table.min.js" as="script">
    <link rel="preload" href="../../home/<USER>/logo.png" as="image">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">

    <!-- 添加favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/common.css">
    <link rel="stylesheet" href="../../public/css/bootstrap-table.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 添加 ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <!-- 添加下载辅助脚本 -->
    <script src="../../public/js/downloader.js"></script>

    <style>
        /* 基础布局样式 */
        body {
            padding-top: 70px;
        }

        /* 内容包装器样式 */
        .content-wrapper {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }

        /* 内容区块样式 */
        .content-section {
            margin-bottom: 20px;
            padding: 0;
        }

        .content-section:last-child {
            margin-bottom: 0;
        }

        /* 标题样式 */
        .content-section h4 {
            color: #002060;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        /* 表格容器样式 */
        .table-responsive {
            margin: 0;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #dee2e6;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        /* 表格样式 */
        .table {
            margin-bottom: 0;
            width: 100%;
        }

        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            color: #495057;
            font-weight: 600;
            padding: 12px;
            white-space: nowrap;
        }

        .table td {
            padding: 12px;
            vertical-align: middle;
        }

        /* Taxa Composition 部分样式 */
        .charts-container {
            margin-top: 20px;
        }

        .level-selector {
            margin-bottom: 25px;
        }

        .data-type-switch {
            margin-bottom: 20px;
        }

        /* 按钮组样式 */
        .btn-group {
            margin-bottom: 15px;
        }

        .btn-group .btn {
            padding: 8px 16px;
        }

        /* 图表容器样式 */
        #pieChart, #stackChart {
            border-radius: 8px;
            background-color: transparent;
        }

        /* 饼图容器样式 */
        .pie-chart-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: transparent;
        }

        #pieChart {
            width: 100%;
            box-shadow: none;
            background-color: transparent;
        }

        /* 表格相关样式优化 */
        .taxa-table-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        #taxaTable thead.sticky-top {
            z-index: 990;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        /* 滑块容器样式 */
        .sample-range {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            margin-top: 20px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .content-wrapper {
                padding: 15px;
            }

            .content-section {
                margin-bottom: 30px;
            }

            .table-responsive {
                margin-bottom: 30px !important;
            }

            .col-md-6.d-flex {
                margin-bottom: 30px;
            }
            
            #pieChart {
                height: 400px !important;
            }
            
            .table-responsive.flex-grow-1 {
                max-height: 350px !important;
            }
        }

        @media (max-width: 576px) {
            #pieChart {
                height: 350px !important;
            }
            
            .table-responsive.flex-grow-1 {
                max-height: 300px !important;
            }
        }

        /* 调整整体布局 */
        .container-fluid {
            padding: 0 30px;
        }

        .content-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* 侧边栏样式 */
        #sidebar {
            position: sticky;
            top: 85px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 15px 0;
        }

        /* 表格样式优化 */
        .table-responsive {
            margin-bottom: 30px !important;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            color: #495057;
            font-weight: 600;
            padding: 12px;
        }

        .table td {
            padding: 12px;
            vertical-align: middle;
        }

        /* 分页样式优化 */
        .fixed-table-pagination {
            padding: 5px;
            background-color: #fff;
            border-top: 1px solid #dee2e6;
            margin-bottom: 0 !important;
        }

        /* 响应式布局优化 */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 0 15px;
            }

            #sidebar {
                position: static;
                margin-bottom: 20px;
            }

            .content-container {
                padding: 15px;
            }
        }

        /* 在head部分添加Google字体（如果需要的话）可以在此添加 */
        @import url('https://fonts.googleapis.com/css2?family=Patrick+Hand&display=swap');

        /* 移除旧的导航栏样式，使用common.css */

        /* 去除顶部空白区域 */
        body::before {
            display: none !important;
            content: none !important;
            height: 0 !important;
        }


        /* 调整内容区域的上边距 */
        .content-section {
            margin-top: 0;
            padding-top: 1rem;
        }

        /* 确保导航栏固定在顶部 */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
        }

        /* 1.折叠title，abstract */
        .text-truncate-container {
            position: relative;
            max-width: 100%;
        }

        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .expanded {
            white-space: normal;
            overflow: visible;
            text-overflow: clip;
        }

        .text-truncate-container {
            display: flex;
            align-items: center;
        }

        .text-truncate {
            flex: 1;
            overflow: hidden;
        }

        .btn-link {
            text-decoration: none; /* 去除按钮的下划线 */
            color: #002060; /* 按钮的颜色 */
            font-weight: bold;
            margin-left: 10px;
        }

        .btn-link:hover {
            text-decoration: underline;
            color: #002060; /* 悬停时显示下划线 */
        }

        /* 2.修改表格样式 */
        table.table-borderless {
            border-collapse: collapse;
            width: 100%;
        }

        table.table-borderless th, 
        table.table-borderless td {
            border: none; /* 去掉单元格之间的边框 */
            padding: 8px; /* 适当增加单元格的填充 */
        }

        /* 设置灰白相间的行背景色 */
        table.table-borderless tr:nth-child(odd) {
            background-color: #f9f9f9; /* 浅灰色 */
        }

        table.table-borderless tr:nth-child(even) {
            background-color: #ffffff; /* 白色 */
        }

        /* 表头固定为白色背景色 */
        table.table-borderless thead th {
            background-color: #ffffff; /* 白色背景 */
            position: sticky;
            top: 0;
            z-index: 1; /* 确保表头在滚动时保持在最前 */
        }
        
        /* 3.固定导航栏并设置美化样式 */
        #sidebar .list-group-item {
            color: #666;
            background-color: transparent;
            border: none;
            padding: 10px 15px;
            margin-bottom: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
            position: relative;
        }

        /* 悬停效果 */
        #sidebar .list-group-item:hover {
            background-color: #e9ecef;
            color: #333;
            text-decoration: none;
        }

        /* 当前激活的导航项样式 */
        #sidebar .list-group-item.active {
            background-color: #e3f2fd;
            color: #002060;
            font-weight: bold;
            border-left: 3px solid #002060;
        }

        #sidebar .list-group-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background-color: #002060;
            border-radius: 2px;
        }

        /* 调整滚动条样式 */
        #sidebar::-webkit-scrollbar {
            width: 5px;
        }

        #sidebar::-webkit-scrollbar-thumb {
            background-color: #002060;
            border-radius: 10px;
        }

        #sidebar::-webkit-scrollbar-track {
            background-color: #343a40;
        }

        /* 侧边栏回顶部按钮卡片样式 */
        .back-to-top-card {
            margin: 20px 10px 10px 10px;
            padding: 20px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .back-to-top-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #002060, #003080, #0040a0);
        }

        .sidebar-back-to-top-btn {
            background: linear-gradient(145deg, #002060, #003080);
            color: white;
            border: none;
            border-radius: 30px;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 700;
            box-shadow: 0 6px 20px rgba(0, 32, 96, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .sidebar-back-to-top-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .sidebar-back-to-top-btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 32, 96, 0.4);
            background: linear-gradient(145deg, #003080, #0040a0);
        }

        .sidebar-back-to-top-btn:hover::before {
            left: 100%;
        }

        .sidebar-back-to-top-btn:active {
            transform: translateY(-1px) scale(0.98);
            box-shadow: 0 4px 15px rgba(0, 32, 96, 0.3);
        }

        .sidebar-back-to-top-btn i {
            font-size: 16px;
            animation: bounce 2s infinite;
        }

        .sidebar-back-to-top-btn span {
            font-size: 12px;
            letter-spacing: 0.8px;
            text-transform: uppercase;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-3px);
            }
            60% {
                transform: translateY(-2px);
            }
        }

        /* 修改按钮样式 */
        .btn-group .btn-outline-primary {
            border-color: #ddd;
            color: #666;
            background-color: #fff;
            margin-right: 2px;
        }

        .btn-group .btn-outline-primary:hover {
            background-color: #f8f9fa;
            border-color: #666;
            color: #333;
        }

        .btn-group .btn-outline-primary.active {
            background-color: #002060 !important;
            border-color: #002060 !important;
            color: #fff !important;
            font-weight: bold;
        }

        /* 确保按钮组样式正确 */
        .level-selector .btn-group {
            display: inline-flex;
            gap: 5px;
        }

        .level-selector .btn {
            padding: 6px 12px;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        /* 滑块容器样式 */
        .range-slider {
            width: 100%;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 20px;
        }

        /* 滑块轨道样式 */
        .range-container {
            position: relative;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            margin: 10px 0 8px;  /* 进一步减小上下间距 */
        }

        /* 滑块样式 */
        .form-range {
            -webkit-appearance: none;
            position: absolute;
            top: -8px;
            width: 100%;
            height: 20px;
            background: transparent;
            pointer-events: none;
            z-index: 5;  /* 提高滑块整体的层级 */
        }

        .form-range::-webkit-slider-thumb {
            -webkit-appearance: none;
            pointer-events: auto;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #fff;
            border: 2px solid #007bff;
            cursor: pointer;
            margin-top: -6px;
            z-index: 10;  /* 确保滑块圆点始终在最上层 */
            position: relative;
        }

        /* 滑块值显示 */
        .range-values {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }

        .range-label {
            color: #333;
            font-weight: 500;
            margin-bottom: 10px;
        }

        /* 表格样式 */
        #taxaTable {
            border-collapse: collapse;
            width: 100%;
        }

        #taxaTable th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #333;
        }

        #taxaTable td {
            padding: 10px 12px;
            border-bottom: 1px solid #dee2e6;
        }

        #taxaTable tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 分页样式 */
        .pagination {
            margin: 0;
        }

        .pagination .page-link {
            padding: 6px 12px;
            color: #666;
            border: 1px solid #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: #002060;
            border-color: #002060;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            color: #ccc;
        }

        /* 修改图表和表格的间距 */
        .chart-section {
            margin-bottom: 40px;  /* 增加与表格的距离 */
        }

        /* 调整表格上方的间距 */
        .table-section {
            margin-top: 40px;
        }

        /* 样本ID主标题样式 */
        .sample-id-main-title {
            text-align: center;
            color: #333;
            font-size: 16px;
            font-weight: 500;
            margin: -45px 0 25px;  /* 进一步上移标题 */
        }

        /* 范围选择标题样式 */
        .range-title {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;  /* 减小标题下方间距 */
        }

        /* 样本范围控制样式 */
        .sample-range {
            width: 100%;
            padding: 5px 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 10px;
            position: relative;
        }

        .range-container {
            position: relative;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            margin: 10px 0 8px;
            z-index: 1;  /* 确保范围条在滑块下方 */
        }

        .form-range.sample-slider {
            position: absolute;
            top: -8px;
            width: 100%;
            height: 20px;
            background: transparent;
            pointer-events: none;
            z-index: 5;
        }

        .form-range.sample-slider::-webkit-slider-thumb {
            pointer-events: auto;
            margin-top: -6px;
            position: relative;
            z-index: 10;
        }

        .range-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            color: #666;
            font-size: 12px;
            margin-bottom: 2px;  /* 添加底部间距 */
        }

        /* 修改样式 */
        #stackChart {
            width: 100%;
            height: 400px;
            margin-top: 60px;
            min-height: 300px;
        }

        /* 确保滑块轨道不会干扰滑块的拖动 */
        .range-track {
            pointer-events: none;
        }

        .selected-range {
            pointer-events: none;
        }

        /* 响应式布局优化 */
        @media (max-width: 1200px) {
            .chart-section {
                margin-bottom: 40px;
            }
            
            #pieChart, #stackChart {
                height: 350px;
            }
            
            .grid {
                left: '5%';
                right: '5%';
                bottom: '15%';
            }
        }
        
        @media (max-width: 768px) {
            #pieChart, #stackChart {
                height: 300px;
            }
            
            .sample-id-main-title {
                margin: -35px 0 20px;
            }
            
            .range-container {
                margin: 8px 0 6px;
            }
        }

        /* 如果需要在特定情况下覆盖间距，可以添加更具体的选择器 */
        @media (max-width: 768px) {
            body .content-container .text-start:nth-child(3),
            body .content-container .text-start:nth-child(4) {
                margin-top: 60px !important;  /* 移动端稍微减少间距 */
            }
        }

        /* 从 style.css 迁移的样式 */
        @media (min-width: 768px){
            .news-input{
                width:70%;
            }
        }

        /* 内容容器样式 */
        .content-container {
            width: 100%;
            padding-right: 15px;
            padding-left: 15px;
        }

        /* Study.php 页面样式 */
        .text-start {
            margin: 40px 0;
            padding-top: 15px;
            width: 100%;
        }

        /* 第一个 text-start 不需要上边距 */
        .text-start:first-child {
            margin-top: 30px;
            padding-top: 0;
        }

        /* 最后一个 text-start 不需要下边距 */
        .text-start:last-child {
            margin-bottom: 30px;
        }

        /* 标题样式统一 */
        .text-start h4 {
            font-size: 1.2rem;  /* 增大字体从 1.2rem 到 1.5rem */
            color: #002060;
            margin-bottom: 1rem;
            padding: 0;
            font-weight: 700;  /* 加粗字体 */
        }

        /* 分割线样式统一 */
        .text-start hr {
            margin: 1rem 0;
            opacity: 0.8;
            width: 100%;
        }

        /* 确保内容区域宽度一致 */
        .text-dark {
            width: 100%;
            padding: 0;
        }

        /* 表格部分样式调整 */
        .table-container, 
        .table-responsive {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        .table-container {
            overflow-x: auto;
        }

        /* 删除表格底部过大的间距 */
        .table-responsive {
            margin-bottom: 30px !important;
        }

        /* 最后一个表格不需要底部间距 */
        .table-responsive:last-child {
            margin-bottom: 0 !important;
        }

        /* 图表相关样式 */
        .chart-section {
            margin-bottom: 60px;
        }

        .pie-chart-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 400px;
        }

        #stackChart {
            width: 100%;
            height: 400px;
            margin-top: 60px;
            min-height: 300px;
        }

        /* 响应式布局 */
        @media (max-width: 1200px) {
            .chart-section {
                margin-bottom: 40px;
            }
            
            #pieChart, #stackChart {
                height: 350px;
            }
        }

        @media (max-width: 768px) {
            #pieChart, #stackChart {
                height: 300px;
            }
            
            .sample-id-main-title {
                margin: -35px 0 20px;
            }

            .table-responsive {
                margin-bottom: 30px !important;
            }
        }

        /* 为 bootstrap-table 之后的标题添加上边距 */
        div[class*="table"] + .text-start,
        .fixed-table-pagination + .text-start,
        [data-toggle="table"] + .text-start {
            margin-top: 40px;
        }

        /* 为 Soil Physicochemical properties 标题添加强制上边距 */
        #title-3,
        .text-start h4#title-3 {
            margin-top: 50px !important;
        }

        /* 隐藏说明文字 */
        .hidden-text {
            color: white !important;
        }

        /* 固定表格高度，避免过多空白 */
        .bootstrap-table .fixed-table-container {
            height: auto !important;
        }

        /* 覆盖bootstrap-table的内部样式 */
        .bootstrap-table .fixed-table-container .fixed-table-body {
            height: auto !important;
            overflow: visible !important;
        }

        /* 表格响应式布局样式 */
        .taxa-table-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .table-responsive {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        #taxaTable thead.sticky-top {
            z-index: 990;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .col-md-6.d-flex {
                margin-bottom: 30px;
            }
            
            #pieChart {
                height: 400px !important;
            }
            
            .table-responsive {
                max-height: 350px !important;
            }
        }

        @media (max-width: 576px) {
            #pieChart {
                height: 350px !important;
            }
            
            .table-responsive {
                max-height: 300px !important;
            }
        }
    </style>
</head>
<body>
    <!--导航栏-->
    <header>
        <!-- 左侧Logo和标题区域 -->
        <div class="left-content">
            <!-- Logo容器 -->
            <div class="logo-container">
                <img src="../../home/<USER>/logo.png" alt="MPRAM Logo" class="logo-image">
            </div>
            <!-- 标题和副标题容器 -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">MPRAM</h1>
                <p class="mb-0 small">Medicinal Plant Rhizosphere Associated Microbiome Database </p>
            </div>
        </div>
        <!-- 汉堡包按钮 -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- 导航链接区域 -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="browse.php" class="nav-link">Search</a></li>
                <li class="nav-item"><a href="Project.php" class="nav-link active">Browse</a></li>
                <li class="nav-item"><a href="../microbes/Microbes.php" class="nav-link">Microbes</a></li>
                <li class="nav-item"><a href="../microbes/Network.php" class="nav-link">Network</a></li>
                <li class="nav-item"><a href="../../map/map.html" class="nav-link">Map</a></li>
                <li class="nav-item"><a href="../help/help.php" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

    <section class="p-5 text-sm-start">
        <div class="container-fluid px-4">
            <h3 class="text-center mb-4" style="color: #002060; font-weight: 600; font-size: 1.5rem;">
                Study Details: <?php echo htmlspecialchars($studyId); ?>
            </h3>
            
            <div class="row">
                <!-- 侧边栏 -->
                <div class="col-md-2">
                    <div id="sidebar" class="list-group text-center">
                        <a href="#title-1" class="list-group-item list-group-item-action">Sample Details</a>
                        <a href="#title-2.5" class="list-group-item list-group-item-action">Primer Details</a>
                        <a href="#title-2.6" class="list-group-item list-group-item-action">Plant Details</a>
                        <a href="#title-2" class="list-group-item list-group-item-action">Herb Details</a>
                        <a href="#title-herb-related" class="list-group-item list-group-item-action">Herb Related Information</a>
                        <a href="#title-3" class="list-group-item list-group-item-action">Geo. information</a>
                        <a href="#title-4" class="list-group-item list-group-item-action">Soil Physicochemical properties</a>
                        <a href="#title-5" class="list-group-item list-group-item-action">Taxa Composition</a>

                        <!-- 一键回顶部按钮卡片 -->
                        <div class="back-to-top-card">
                            <button id="sidebarBackToTop" class="sidebar-back-to-top-btn" title="Back to Top">
                                <i class="fas fa-rocket"></i>
                                <span>Back to Top</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="col-md-10">
                    <div class="content-wrapper bg-white p-4 rounded shadow-sm">
                        <!-- Sample Details Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-1">Sample Details</h4>
                            <hr class="border-2 opacity-75">
                            <div class="section-content">
                                <?php
                                echo   '<dl class="row">
                                            <dt class="col-sm-3 p-2">Study ID:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_study['Study ID'] . '</dd>

                                            <dt class="col-sm-3 p-2">Latin Name:</dt>
                                            <dd class="col-sm-9 p-2">
                                                <em>' . $row_study['Study Name'] . '</em>
                                            </dd>
                                            
                                            <dt class="col-sm-3 p-2">Chinese Name:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_study['Chinese Name'] . '</dd>

                                            <dt class="col-sm-3 p-2">English Name:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_study['English Name'] . '</dd>

                                            <dt class="col-sm-3 p-2">Pub Date:</dt>
                                            <dd class="col-sm-9 p-2">' . date('Y-m-d', strtotime($row_study['Pub Date'])) . '</dd>

                                            <dt class="col-sm-3 p-2">Title:</dt>
                                            <dd class="col-sm-9 p-2">
                                                <div class="text-truncate-container">
                                                    <div class="text-truncate" id="titleContent">' . $row_study['Title'] . '</div>
                                                    <button class="btn btn-link p-0" type="button" onclick="toggleText(\'titleContent\', this)">Read more</button>
                                                </div>
                                            </dd>
                                        
                                            <dt class="col-sm-3 p-2">Abstract:</dt>
                                            <dd class="col-sm-9 p-2">
                                                <div class="text-truncate-container">
                                                    <div class="text-truncate" id="abstractContent">' . $row_study['Abstract'] . '</div>
                                                    <button class="btn btn-link p-0" type="button" onclick="toggleText(\'abstractContent\', this)">Read more</button>
                                                </div>
                                            </dd>

                                            <dt class="col-sm-3 p-2">NCBI-Accession:</dt>
                                            <dd class="col-sm-9 p-2 d-flex align-items-center">
                                                ' . $row_study['NCBI_Accession'] . '
                                                <div class="dropdown ms-3 me-2">
                                                    <button class="btn btn-sm btn-outline-info dropdown-toggle" type="button" id="ncbiDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-database"></i> NCBI SRA
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="ncbiDropdown">';
                                                    
                                                    // 处理NCBI SRA链接
                                                    $accessions = explode(';', $row_study['NCBI_Accession']);
                                                    foreach($accessions as $accession) {
                                                        $trimmedAccession = trim($accession);
                                                        if(!empty($trimmedAccession)) {
                                                            echo '<li><a class="dropdown-item" href="https://www.ncbi.nlm.nih.gov/sra/?term=' . urlencode($trimmedAccession) . '" target="_blank">' . $trimmedAccession . '</a></li>';
                                                        }
                                                    }
                                                    
                                                    echo '</ul>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="enaDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-database"></i> ENA Browser
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="enaDropdown">';
                                                    
                                                    // 处理ENA Browser链接
                                                    foreach($accessions as $accession) {
                                                        $trimmedAccession = trim($accession);
                                                        if(!empty($trimmedAccession)) {
                                                            echo '<li><a class="dropdown-item" href="https://www.ebi.ac.uk/ena/browser/view/' . urlencode($trimmedAccession) . '" target="_blank">' . $trimmedAccession . '</a></li>';
                                                        }
                                                    }
                                                    
                                                    echo '</ul>
                                                </div>
                                            </dd>

                                            <dt class="col-sm-3 p-2">DOI-Link:</dt>
                                            <dd class="col-sm-9 p-2"><a href="' . $row_study['DOI-LINK'] . '" target="_blank">' . $row_study['DOI-LINK'] . '</a></dd>

                                            <dt class="col-sm-3 p-2">Pedological Classification:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_study['Pedological Classification'] . '</dd>

                                            <dt class="col-sm-3 p-2">Sampling Stratification:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_study['Sampling Stratification'] . '</dd>

                                            <dt class="col-sm-3 p-2">Sample Grouping Information:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_study['Sample Grouping Information'] . '</dd>
                                        </dl>';
                                ?>
                            </div>
                        </div>

                        <!-- Primer Details Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-2.5">Primer Details</h4>
                            <hr class="border-2 opacity-75">
                            <div class="section-content">
                                <?php
                                if ($row_primer) {
                                    echo '<dl class="row">
                                            <dt class="col-sm-3 p-2">16S Primer Region:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_primer['16S Primer Region'] . '</dd>

                                            <dt class="col-sm-3 p-2">16S Primer Information:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_primer['16S Primer Information'] . '</dd>

                                            <dt class="col-sm-3 p-2">ITS Primer Region:</dt>
                                            <dd class="col-sm-9 p-2">' . (array_key_exists('ITS Primer Region', $row_primer) ? $row_primer['ITS Primer Region'] : 
                                                (array_key_exists('​ITS Primer Region', $row_primer) ? $row_primer['​ITS Primer Region'] : 'N/A')) . '</dd>

                                            <dt class="col-sm-3 p-2">ITS Primer Information:</dt>
                                            <dd class="col-sm-9 p-2">' . (array_key_exists('ITS Primer Region.1', $row_primer) ? $row_primer['ITS Primer Region.1'] : 
                                                (array_key_exists('​ITS Primer Region.1', $row_primer) ? $row_primer['​ITS Primer Region.1'] : 'N/A')) . '</dd>
                                        </dl>';
                                } else {
                                    echo '<p class="alert alert-info">No primer details available for this study.</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Plant Details Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-2.6">Plant Details</h4>
                            <hr class="border-2 opacity-75">
                            <div class="section-content">
                                <?php
                                if ($row_plant) {
                                    echo '<dl class="row">
                                            <dt class="col-sm-3 p-2">Latin Name:</dt>
                                            <dd class="col-sm-9 p-2 d-flex align-items-center">
                                                <em>' . $row_plant['Latin Name'] . '</em>
                                                <a href="https://powo.science.kew.org/results?q=' . urlencode($row_plant['Latin Name']) . '" target="_blank" class="btn btn-sm btn-outline-primary ms-3 me-2">
                                                    <i class="fas fa-external-link-alt"></i> POWO
                                                </a>
                                                <a href="http://www.worldfloraonline.org/search?query=' . urlencode($row_plant['Latin Name']) . '" target="_blank" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-external-link-alt"></i> WFO
                                                </a>
                                            </dd>

                                            <dt class="col-sm-3 p-2">Chinese Name:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_plant['Chinese Name'] . '</dd>

                                            <dt class="col-sm-3 p-2">Family:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_plant['Family'] . '</dd>

                                            <dt class="col-sm-3 p-2">Order:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_plant['Order'] . '</dd>

                                            <dt class="col-sm-3 p-2">Class:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_plant['Class'] . '</dd>

                                            <dt class="col-sm-3 p-2">Native Distribution:</dt>
                                            <dd class="col-sm-9 p-2">' . $row_plant['Native Distribution (POWO)'] . '</dd>
                                        </dl>';
                                } else {
                                    echo '<p class="alert alert-info">No plant details available for this study.</p>';
                                }
                                ?>
                            </div>
                        </div>
                        
                        <!-- Herb Details Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-2">Herb Details</h4>
                            <hr class="border-2 opacity-75">
                            <div class="section-content">
                                <?php
                                if ($row_herb) {
                                    echo '<dl class="row">
                                            <dt class="col-sm-3 p-2">Latin Name:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Latin name']) ? $row_herb['Latin name'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Chinese Name:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Chinese name']) ? $row_herb['Chinese name'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Herb Alias Name:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Herb alias name']) ? $row_herb['Herb alias name'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Herb English Name:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Herb en name']) ? $row_herb['Herb en name'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Herb Latin Name:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Herb latin name']) ? $row_herb['Herb latin name'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Properties:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Properties']) ? $row_herb['Properties'] : (isset($row_herb['Property Flavor']) ? $row_herb['Property Flavor'] : 'N/A')) . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Meridians:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Meridians']) ? $row_herb['Meridians'] : (isset($row_herb['Channel Tropism']) ? $row_herb['Channel Tropism'] : 'N/A')) . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Use Part:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['UsePart']) ? $row_herb['UsePart'] : (isset($row_herb['Use Part']) ? $row_herb['Use Part'] : 'N/A')) . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Function:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Function']) ? $row_herb['Function'] : (isset($row_herb['Effect']) ? $row_herb['Effect'] : 'N/A')) . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Indication:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Indication']) ? $row_herb['Indication'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Toxicity:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Toxicity']) ? $row_herb['Toxicity'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Clinical Manifestations:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Clinical manifestations']) ? $row_herb['Clinical manifestations'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Therapeutic English Class:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Therapeutic en class']) ? $row_herb['Therapeutic en class'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Therapeutic Chinese Class:</dt>
                                            <dd class="col-sm-9 p-2">' . (isset($row_herb['Therapeutic cn class']) ? $row_herb['Therapeutic cn class'] : 'N/A') . '</dd>
                                            
                                            <dt class="col-sm-3 p-2">Database IDs:</dt>
                                            <dd class="col-sm-9 p-2">
                                                <span class="me-3"><strong>SymMap ID:</strong> ' . (isset($row_herb['SymMap id']) ? '<a href="http://www.symmap.org/detail/' . $row_herb['SymMap id'] . '" target="_blank">' . $row_herb['SymMap id'] . '</a>' : 'N/A') . '</span>
                                                <span class="me-3"><strong>TCMID ID:</strong> ' . (isset($row_herb['TCMID id']) ? '<a href="https://bidd.group/TCMID/herb.php?herb=TCMH' . $row_herb['TCMID id'] . '" target="_blank">' . $row_herb['TCMID id'] . '</a>' : 'N/A') . '</span>
                                                <span class="me-3"><strong>TCMSP ID:</strong> ' . (isset($row_herb['TCMSP id']) ? '<a href="http://tcmspw.com/molecule.php?qn=' . $row_herb['TCMSP id'] . '" target="_blank">' . $row_herb['TCMSP id'] . '</a>' : 'N/A') . '</span>
                                                <span class="me-3"><strong>TCM ID:</strong> ' . (isset($row_herb['TCM ID id']) ? '<a href="http://tcm.cmu.edu.tw/zh-tw/herb-detail.php?herb_id=' . $row_herb['TCM ID id'] . '" target="_blank">' . $row_herb['TCM ID id'] . '</a>' : 'N/A') . '</span>
                                                <span><strong>Herb ID:</strong> ' . (isset($row_herb['Herb id']) ? '<a href="http://47.92.70.12/Detail/?v=' . (strpos($row_herb['Herb id'], 'HERB') === 0 ? $row_herb['Herb id'] : 'HERB' . $row_herb['Herb id']) . '&label=Herb" target="_blank">' . $row_herb['Herb id'] . '</a>' : 'N/A') . '</span>
                                            </dd>
                                        </dl>';
                                } else {
                                    echo '<p class="alert alert-info">No herb details available for this study.</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Herb Related Information Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-herb-related">Herb Related Information</h4>
                            <hr class="border-2 opacity-75">
                            <div class="section-content">
                                <?php
                                // 获取中文名称（优先从herb表获取，如果没有则从plant表获取）
                                $chinese_name = '';
                                if ($row_herb && isset($row_herb['Chinese name'])) {
                                    $chinese_name = $row_herb['Chinese name'];
                                } elseif ($row_herb && isset($row_herb['Chinese Name'])) {
                                    $chinese_name = $row_herb['Chinese Name'];
                                } elseif ($row_plant && isset($row_plant['Chinese Name'])) {
                                    $chinese_name = $row_plant['Chinese Name'];
                                } elseif ($row_study && isset($row_study['Chinese Name'])) {
                                    $chinese_name = $row_study['Chinese Name'];
                                }
                                
                                if (!empty($chinese_name)) {
                                    // 检查是否存在对应的文件夹
                                    $herb_folder = $_SERVER['DOCUMENT_ROOT'] . '/MPRAM/HRRB/' . $chinese_name;
                                    
                                    if (is_dir($herb_folder)) {
                                        // 检查成分文件
                                        $ingredients_file = $herb_folder . '/ingredients.csv';
                                        $has_ingredients = file_exists($ingredients_file);
                                        
                                        // 检查靶点文件
                                        $targets_file = $herb_folder . '/targets.csv';
                                        $has_targets = file_exists($targets_file);
                                        
                                        // 检查疾病文件
                                        $diseases_file = $herb_folder . '/diseases.csv';
                                        $has_diseases = file_exists($diseases_file);
                                        
                                        // 创建折叠面板
                                        echo '<div class="accordion" id="herbRelatedAccordion">';
                                        
                                        // 成分信息面板
                                        if ($has_ingredients) {
                                            echo '<div class="accordion-item">';
                                            echo '<h2 class="accordion-header" id="headingIngredients">';
                                            echo '<button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseIngredients" aria-expanded="true" aria-controls="collapseIngredients">';
                                            echo 'Related Ingredients';
                                            echo '</button>';
                                            echo '</h2>';
                                            echo '<div id="collapseIngredients" class="accordion-collapse collapse show" aria-labelledby="headingIngredients" data-bs-parent="#herbRelatedAccordion">';
                                            echo '<div class="accordion-body">';
                                            echo '<div class="table-responsive">';
                                            echo '<table class="table table-striped" data-toggle="table" data-pagination="true" data-page-size="10" data-page-list="[10, 25, 50, All]">';
                                            echo '<thead><tr>';
                                            
                                            // 读取CSV文件头
                                            $handle = fopen($ingredients_file, 'r');
                                            $header = fgetcsv($handle);
                                            
                                            // 需要显示的字段
                                            $display_fields = ['Ingredient_name', 'Ingredient_alias_name', 'Molecular_formula'];
                                            $field_indices = [];
                                            
                                            // 查找需要显示的字段的索引
                                            foreach ($display_fields as $field) {
                                                $index = array_search($field, $header);
                                                if ($index !== false) {
                                                    $field_indices[$field] = $index;
                                                    echo '<th>' . htmlspecialchars($field) . '</th>';
                                                }
                                            }
                                            echo '</tr></thead>';
                                            
                                            echo '<tbody>';
                                            // 读取CSV文件内容
                                            while (($data = fgetcsv($handle)) !== FALSE) {
                                                echo '<tr>';
                                                foreach ($field_indices as $field => $index) {
                                                    if (isset($data[$index])) {
                                                        if ($field === 'Ingredient_name') {
                                                            // 为成分名称添加链接，在新页面打开，并传递返回URL
                                                            $current_url = urlencode($_SERVER['REQUEST_URI']);
                                                            echo '<td><a href="herb_detail.php?type=ingredient&name=' . urlencode($data[$index]) . '&herb=' . urlencode($chinese_name) . '&return_url=' . $current_url . '" class="text-primary herb-detail-link" target="_blank" rel="noopener noreferrer">' . htmlspecialchars($data[$index]) . ' <i class="fas fa-external-link-alt ms-1" style="font-size: 10px;"></i></a></td>';
                                                        } else {
                                                            echo '<td>' . htmlspecialchars($data[$index]) . '</td>';
                                                        }
                                                    } else {
                                                        echo '<td>N/A</td>';
                                                    }
                                                }
                                                echo '</tr>';
                                            }
                                            fclose($handle);
                                            echo '</tbody>';
                                            echo '</table>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                        }
                                        
                                        // 靶点信息面板
                                        if ($has_targets) {
                                            echo '<div class="accordion-item">';
                                            echo '<h2 class="accordion-header" id="headingTargets">';
                                            echo '<button class="accordion-button' . ($has_ingredients ? ' collapsed' : '') . '" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTargets" aria-expanded="' . ($has_ingredients ? 'false' : 'true') . '" aria-controls="collapseTargets">';
                                            echo 'Related Targets';
                                            echo '</button>';
                                            echo '</h2>';
                                            echo '<div id="collapseTargets" class="accordion-collapse collapse' . ($has_ingredients ? '' : ' show') . '" aria-labelledby="headingTargets" data-bs-parent="#herbRelatedAccordion">';
                                            echo '<div class="accordion-body">';
                                            echo '<div class="table-responsive">';
                                            echo '<table class="table table-striped" data-toggle="table" data-pagination="true" data-page-size="10" data-page-list="[10, 25, 50, All]">';
                                            echo '<thead><tr>';
                                            
                                            // 读取CSV文件头
                                            $handle = fopen($targets_file, 'r');
                                            $header = fgetcsv($handle);
                                            
                                            // 需要显示的字段
                                            $display_fields = ['Gene_symbol', 'Gene_alias_name', 'Protein_name', 'Type_of_gene'];
                                            $field_indices = [];
                                            
                                            // 查找需要显示的字段的索引
                                            foreach ($display_fields as $field) {
                                                $index = array_search($field, $header);
                                                if ($index !== false) {
                                                    $field_indices[$field] = $index;
                                                    echo '<th>' . htmlspecialchars($field) . '</th>';
                                                }
                                            }
                                            echo '</tr></thead>';
                                            
                                            echo '<tbody>';
                                            // 读取CSV文件内容
                                            while (($data = fgetcsv($handle)) !== FALSE) {
                                                echo '<tr>';
                                                foreach ($field_indices as $field => $index) {
                                                    if (isset($data[$index])) {
                                                        if ($field === 'Gene_symbol') {
                                                            // 为靶点名称添加链接，在新页面打开，并传递返回URL
                                                            $current_url = urlencode($_SERVER['REQUEST_URI']);
                                                            echo '<td><a href="herb_detail.php?type=target&name=' . urlencode($data[$index]) . '&herb=' . urlencode($chinese_name) . '&return_url=' . $current_url . '" class="text-primary herb-detail-link" target="_blank" rel="noopener noreferrer">' . htmlspecialchars($data[$index]) . ' <i class="fas fa-external-link-alt ms-1" style="font-size: 10px;"></i></a></td>';
                                                        } else {
                                                            echo '<td>' . htmlspecialchars($data[$index]) . '</td>';
                                                        }
                                                    } else {
                                                        echo '<td>N/A</td>';
                                                    }
                                                }
                                                echo '</tr>';
                                            }
                                            fclose($handle);
                                            echo '</tbody>';
                                            echo '</table>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                        }
                                        
                                        // 疾病信息面板
                                        if ($has_diseases) {
                                            echo '<div class="accordion-item">';
                                            echo '<h2 class="accordion-header" id="headingDiseases">';
                                            echo '<button class="accordion-button' . ($has_ingredients || $has_targets ? ' collapsed' : '') . '" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDiseases" aria-expanded="' . ($has_ingredients || $has_targets ? 'false' : 'true') . '" aria-controls="collapseDiseases">';
                                            echo 'Related Diseases';
                                            echo '</button>';
                                            echo '</h2>';
                                            echo '<div id="collapseDiseases" class="accordion-collapse collapse' . ($has_ingredients || $has_targets ? '' : ' show') . '" aria-labelledby="headingDiseases" data-bs-parent="#herbRelatedAccordion">';
                                            echo '<div class="accordion-body">';
                                            echo '<div class="table-responsive">';
                                            echo '<table class="table table-striped" data-toggle="table" data-pagination="true" data-page-size="10" data-page-list="[10, 25, 50, All]">';
                                            echo '<thead><tr>';
                                            
                                            // 读取CSV文件头
                                            $handle = fopen($diseases_file, 'r');
                                            $header = fgetcsv($handle);
                                            
                                            // 需要显示的字段
                                            $display_fields = ['Disease_name', 'Disease_alias_name', 'DisGeNET_disease_type'];
                                            $field_indices = [];
                                            
                                            // 查找需要显示的字段的索引
                                            foreach ($display_fields as $field) {
                                                $index = array_search($field, $header);
                                                if ($index !== false) {
                                                    $field_indices[$field] = $index;
                                                    echo '<th>' . htmlspecialchars($field) . '</th>';
                                                }
                                            }
                                            echo '</tr></thead>';
                                            
                                            echo '<tbody>';
                                            // 读取CSV文件内容
                                            while (($data = fgetcsv($handle)) !== FALSE) {
                                                echo '<tr>';
                                                foreach ($field_indices as $field => $index) {
                                                    if (isset($data[$index])) {
                                                        if ($field === 'Disease_name') {
                                                            // 为疾病名称添加链接，在新页面打开，并传递返回URL
                                                            $current_url = urlencode($_SERVER['REQUEST_URI']);
                                                            echo '<td><a href="herb_detail.php?type=disease&name=' . urlencode($data[$index]) . '&herb=' . urlencode($chinese_name) . '&return_url=' . $current_url . '" class="text-primary herb-detail-link" target="_blank" rel="noopener noreferrer">' . htmlspecialchars($data[$index]) . ' <i class="fas fa-external-link-alt ms-1" style="font-size: 10px;"></i></a></td>';
                                                        } else {
                                                            echo '<td>' . htmlspecialchars($data[$index]) . '</td>';
                                                        }
                                                    } else {
                                                        echo '<td>N/A</td>';
                                                    }
                                                }
                                                echo '</tr>';
                                            }
                                            fclose($handle);
                                            echo '</tbody>';
                                            echo '</table>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                            echo '</div>';
                                        }
                                        
                                        echo '</div>'; // 结束折叠面板
                                    } else {
                                        echo '<div class="alert alert-info">No herb related information available for ' . htmlspecialchars($chinese_name) . '.</div>';
                                    }
                                } else {
                                    echo '<div class="alert alert-info">No Chinese name available for this herb.</div>';
                                }
                                ?>
                            </div>
                        </div>

                        <!-- Geo Information Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-3">Geo. information</h4>
                            <hr class="border-2 opacity-75">
                            <div class="table-responsive">
                                <table class="table table-borderless text-center"
                                       data-toggle="table"
                                       data-pagination="true"
                                       data-page-size="5"
                                       data-page-list="[5, 10, 20, All]">
                                    <thead>
                                        <tr>
                                            <th scope="col">Study Id</th>
                                            <th scope="col">Sample Id</th>
                                            <th scope="col">Sample Name</th>
                                            <th scope="col">Collection Sites</th>
                                            <th scope="col">Province</th>
                                            <th scope="col">Country</th>
                                            <th scope="col">Longitude</th>
                                            <th scope="col">Latitude</th>
                                            <th scope="col">Elevation (m)</th>
                                            <th scope="col">Age (year)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // 输出 geo 表的数据
                                        while ($row_geo = $result_geo->fetch_assoc()) {
                                            echo '<tr>';
                                            echo '<td>' . $row_geo['Study ID'] . '</td>';
                                            echo '<td>' . $row_geo['Sample ID'] . '</td>';
                                            echo '<td>' . $row_geo['Sample Name'] . '</td>';
                                            echo '<td>' . (isset($row_geo['Collection Sites']) ? $row_geo['Collection Sites'] : 'N/A') . '</td>';
                                            echo '<td>' . (isset($row_geo['Province']) ? $row_geo['Province'] : 'N/A') . '</td>';
                                            echo '<td>' . (isset($row_geo['Country']) ? $row_geo['Country'] : 'N/A') . '</td>';
                                            
                                            // 处理经度，添加 E/W
                                            $longitude = $row_geo['Longitude'];
                                            if (is_numeric($longitude)) {
                                                $longitude_val = floatval($longitude);
                                                $longitude_direction = $longitude_val >= 0 ? 'E' : 'W';
                                                echo '<td>' . number_format(abs($longitude_val), 2) . '°' . $longitude_direction . '</td>';
                                            } else {
                                                echo '<td>' . $longitude . '</td>';
                                            }
                                            
                                            // 处理纬度，添加 N/S
                                            $latitude = $row_geo['Latitude'];
                                            if (is_numeric($latitude)) {
                                                $latitude_val = floatval($latitude);
                                                $latitude_direction = $latitude_val >= 0 ? 'N' : 'S';
                                                echo '<td>' . number_format(abs($latitude_val), 2) . '°' . $latitude_direction . '</td>';
                                            } else {
                                                echo '<td>' . $latitude . '</td>';
                                            }
                                            
                                            // 处理海拔
                                            $elevation = isset($row_geo['Elevation (m)']) ? $row_geo['Elevation (m)'] : 'N/A';
                                            echo '<td>' . (is_numeric($elevation) ? number_format(floatval($elevation), 2) : $elevation) . '</td>';
                                            
                                            // 处理年龄
                                            $age = isset($row_geo['Age (year)']) ? $row_geo['Age (year)'] : 'N/A';
                                            echo '<td>' . (is_numeric($age) ? number_format(floatval($age), 2) : $age) . '</td>';
                                            
                                            echo '</tr>';
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Physicochemical Properties Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-4">Soil Physicochemical properties</h4>
                            <hr class="border-2 opacity-75">
                            <div class="table-responsive">
                                <table class="table table-borderless text-center"
                                       data-toggle="table"
                                       data-pagination="true"
                                       data-page-size="5"
                                       data-page-list="[5, 10, 20, All]">
                                    <thead>
                                        <tr>
                                            <th scope="col">Study Id</th>
                                            <th scope="col">Sample Name</th>
                                            <th scope="col">pH</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Water Content">WC (%)</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Organic Matter">OM (g/kg)</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Total Nitrogen">TN (g/kg)</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Available Nitrogen">AN (mg/kg)</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Total Phosphorus">TP (g/kg)</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Available Phosphorus">AP (mg/kg)</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Total Potassium">TK (g/kg)</th>
                                            <th scope="col" data-toggle="tooltip" data-placement="top" title="Available Potassium">AK (mg/kg)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // 输出 physicochemical 表的数据
                                        while ($row_physicochemical = $result_physicochemical->fetch_assoc()) {
                                            echo '<tr>';
                                            echo '<td>' . $row_physicochemical['Study ID'] . '</td>';
                                            echo '<td>' . $row_physicochemical['Sample Name'] . '</td>';
                                            
                                            // 处理 pH 值
                                            $ph = $row_physicochemical['PH'];
                                            echo '<td>' . (is_numeric($ph) ? number_format(floatval($ph), 2) : $ph) . '</td>';
                                            
                                            // 处理 WC(%)
                                            $wc = $row_physicochemical['WC(%)'];
                                            echo '<td>' . (is_numeric($wc) ? number_format(floatval($wc), 2) : $wc) . '</td>';
                                            
                                            // 处理 OM(g/kg)
                                            $om = $row_physicochemical['OM(g/kg)'];
                                            echo '<td>' . (is_numeric($om) ? number_format(floatval($om), 2) : $om) . '</td>';
                                            
                                            // 处理 TN(g/kg)
                                            $tn = $row_physicochemical['TN(g/kg)'];
                                            echo '<td>' . (is_numeric($tn) ? number_format(floatval($tn), 2) : $tn) . '</td>';
                                            
                                            // 处理 AN(mg/kg)
                                            $an = $row_physicochemical['AN(mg/kg)'];
                                            echo '<td>' . (is_numeric($an) ? number_format(floatval($an), 2) : $an) . '</td>';
                                            
                                            // 处理 TP(g/kg)
                                            $tp = $row_physicochemical['TP(g/kg)'];
                                            echo '<td>' . (is_numeric($tp) ? number_format(floatval($tp), 2) : $tp) . '</td>';
                                            
                                            // 处理 AP(mg/kg)
                                            $ap = $row_physicochemical['AP(mg/kg)'];
                                            echo '<td>' . (is_numeric($ap) ? number_format(floatval($ap), 2) : $ap) . '</td>';
                                            
                                            // 处理 TK(g/kg)
                                            $tk = $row_physicochemical['TK(g/kg)'];
                                            echo '<td>' . (is_numeric($tk) ? number_format(floatval($tk), 2) : $tk) . '</td>';
                                            
                                            // 处理 AK(mg/kg)
                                            $ak = $row_physicochemical['AK(mg/kg)'];
                                            echo '<td>' . (is_numeric($ak) ? number_format(floatval($ak), 2) : $ak) . '</td>';
                                            
                                            echo '</tr>';
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Taxa Composition Section -->
                        <div class="content-section mb-5">
                            <h4 id="title-5">Taxa Composition Analysis</h4>
                            <hr class="border-2 opacity-75">
                            
                            <!-- 数据类型切换按钮 -->
                            <div class="data-type-switch mb-3" style="display: none;">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary active" data-type="16S">Bacteria</button>
                                    <button type="button" class="btn btn-outline-primary" data-type="ITS">Fungi</button>
                                </div>
                            </div>

                            <!-- 分类层级选择器 -->
                            <div class="level-selector mb-4">
                                <div class="btn-group" role="group" aria-label="Taxa level selector">
                                    <button type="button" class="btn btn-outline-primary active" data-level="Genus">Genus</button>
                                    <button type="button" class="btn btn-outline-primary" data-level="Family">Family</button>
                                    <button type="button" class="btn btn-outline-primary" data-level="Order">Order</button>
                                    <button type="button" class="btn btn-outline-primary" data-level="Class">Class</button>
                                    <button type="button" class="btn btn-outline-primary" data-level="Phylum">Phylum</button>
                                </div>
                            </div>

                            <!-- 下载按钮 -->
                            <div class="d-flex justify-content-end mb-3">
                                <div class="btn-group" role="group" style="width: 600px;">
                                    <button type="button" class="btn btn-outline-success" id="downloadOtuBtn" style="width: 50%;">
                                        <i class="bi bi-download me-1"></i> Download OTU Table
                                    </button>
                                    <button type="button" class="btn btn-outline-info" id="downloadTaxonomyBtn" style="width: 50%;">
                                        <i class="bi bi-download me-1"></i> Download Taxonomy Table
                                    </button>
                                </div>
                            </div>

                            <!-- 图表和表格容器 -->
                            <div class="charts-container">
                                <div class="row">
                                    <div class="col-md-6 d-flex flex-column">
                                        <div id="pieChart" style="height: 500px; flex-grow: 1;"></div>
                                    </div>
                                    <div class="col-md-6 d-flex flex-column">
                                        <div class="taxa-table-container d-flex flex-column h-100">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2">Show</span>
                                                    <select class="form-select form-select-sm" id="itemsPerPage" style="width: auto;">
                                                        <option value="5">5</option>
                                                        <option value="10" selected>10</option>
                                                        <option value="20">20</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="ms-2">entries</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="me-2">Total:</span>
                                                    <span id="totalItems">0</span>
                                                </div>
                                            </div>
                                            <div class="table-responsive flex-grow-1" style="max-height: 430px; overflow-y: auto;">
                                                <table class="table table-hover" id="taxaTable">
                                                    <thead class="sticky-top bg-white">
                                                        <tr>
                                                            <th><span id="taxaLevelHeader">Taxa</span></th>
                                                            <th>Average relative abundance (%)</th>
                                                            <th>Median relative abundance (%)</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                <div id="tableInfo" class="text-muted">
                                                    Showing <span id="showingStart">0</span> to <span id="showingEnd">0</span> of <span id="totalEntries">0</span> entries
                                                </div>
                                                <nav>
                                                    <ul class="pagination justify-content-end mb-0" id="tablePagination">
                                                    </ul>
                                                </nav>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 添加Tips信息区域 - 铺满整个容器宽度 -->
                                <div class="alert mt-4 mb-4" style="font-size: 14px; background-color: #002060; color: white; padding: 8px 15px; width: 100%;">
                                    <strong style="font-size: 16px;">Tips：</strong> <?php echo $row_study['Sample Grouping Information']; ?>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div id="stackChart" style="height: 400px;"></div>
                                        <div class="sample-range mt-3">
                                            <div class="range-title">Sample range</div>
                                            <div class="range-container">
                                                <input type="range" class="form-range sample-slider" id="startSlider" min="0" max="100" value="0">
                                                <input type="range" class="form-range sample-slider" id="endSlider" min="0" max="100" value="100">
                                            </div>
                                            <div class="range-labels">
                                                <span id="startLabel">0%</span>
                                                <span id="endLabel">100%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚区域 -->
    <footer class="container-fluid py-3 text-center">
        <p>Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
    </footer>

    <!-- 引入JavaScript文件 -->
    <script src="../../public/js/jquery-3.7.1.min.js"></script>
    <script src="../../public/js/bootstrap.bundle.min.js"></script>
    <script src="../../public/js/bootstrap-table.min.js"></script>
    <script src="../../public/js/common.js"></script>
    <script>
        // POWO数据库链接处理函数
        function openPOWOLink(latinName) {
            // 构建POWO数据库的URL
            const baseUrl = 'https://powo.science.kew.org/results';
            const encodedName = encodeURIComponent(latinName.trim());
            const powoUrl = `${baseUrl}?q=${encodedName}`;
            
            // 在新窗口中打开链接
            window.open(powoUrl, '_blank');
        }

        function toggleNav() {
            const nav = document.querySelector('nav');
            const hamburger = document.querySelector('.hamburger');
            nav.classList.toggle('show');
            hamburger.classList.toggle('active');
        }

        $(function () {
            $('[data-toggle="tooltip"]').tooltip({
                delay: { show: 50, hide: 100 },
                template: '<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner" style="font-size: 16px; font-weight: 500;"></div></div>'
            })
        })

        function toggleText(id, button) {
            var content = document.getElementById(id);
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                button.textContent = 'Read more';
            } else {
                content.classList.add('expanded');
                button.textContent = 'Read less';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('h4');  // 只选择主要标题
            const navLinks = document.querySelectorAll('#sidebar .list-group-item');

            function activateLink(link) {
                navLinks.forEach(nav => nav.classList.remove('active'));
                link.classList.add('active');
            }

            // 计算每个部分的位置
            function getSectionPositions() {
                return Array.from(sections).map(section => ({
                    id: section.getAttribute('id'),
                    top: section.offsetTop - 100  // 考虑固定导航栏的高度
                }));
            }

            window.addEventListener('scroll', () => {
                let currentSection = '';
                const sectionPositions = getSectionPositions();
                const scrollPosition = window.pageYOffset;

                // 找到当前滚动位置对应的部分
                for (let i = sectionPositions.length - 1; i >= 0; i--) {
                    if (scrollPosition >= sectionPositions[i].top) {
                        currentSection = sectionPositions[i].id;
                        break;
                    }
                }

                navLinks.forEach(link => {
                    const linkTarget = link.getAttribute('href').substring(1);
                    if (linkTarget === currentSection) {
                        activateLink(link);
                    }
                });
            });

            // 添加平滑滚动
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);
                    if (targetSection) {
                        window.scrollTo({
                            top: targetSection.offsetTop - 90,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });

        // 修改颜色数组，使用 mymind 配色
        const COLORS = [
            '#167C80',  // Mr. Business - 清新深青
            '#E54B4B',  // Corrupted Monk - 温暖红色
            '#32B67A',  // Plant Matter - 自然绿
            '#283470',  // Earnest Proposal - 深蓝
            '#B6CAC0',  // Chivalrous Fox - 柔和灰绿
            '#EF3E4A',  // Swift Kiss - 鲜艳红
            '#178E96',  // Late Homework - 湖蓝
            '#9357A9',  // Late Gift - 优雅紫
            '#00B28B',  // Dessert Island - 薄荷绿
            '#4E7CA1',  // 清爽蓝
            '#B32A48',  // Billiards Room - 深红
            '#17C37B',  // Drunk on Easter - 明亮绿
            '#61BFAD',  // Radio Silence - 柔和青
            '#776EA7',  // Twisted Ribbon - 淡紫
            '#3B755F',  // Over the Hill - 深绿
            '#CE7182',  // Skinny Dipped - 粉红
            '#0BBCD6',  // Favorite Bodega - 天蓝
            '#FF8B8B',  // Riviera - 淡红
            '#20AD65',  // Spring Awakening - 春绿
            '#8AA9C6'   // Robed Witness - 淡蓝
        ];

        // 修改全局变量的默认值
        let itemsPerPage = 10; // 改为默认显示10条
        let currentPage = 1;
        let currentData = null;
        let startPercent = 0;
        let endPercent = 100;

        // 修改下拉框的默认选中值
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('itemsPerPage').value = '10';  // 设置默认选中值为10
        });

        // 初始化图表实例
        let pieChart = null;
        let stackChart = null;

        // 初始化函数
        function initCharts() {
            pieChart = echarts.init(document.getElementById('pieChart'));
            stackChart = echarts.init(document.getElementById('stackChart'));
            
            // 只保留堆叠图内部的高亮联动
            stackChart.on('mouseover', {seriesIndex: 'all'}, function(params) {
                if (params.seriesName) {
                    stackChart.dispatchAction({
                        type: 'highlight',
                        seriesName: params.seriesName
                    });
                }
            });

            stackChart.on('mouseout', {seriesIndex: 'all'}, function(params) {
                if (params.seriesName) {
                    stackChart.dispatchAction({
                        type: 'downplay',
                        seriesName: params.seriesName
                    });
                }
            });
        }

        // 修改堆叠图配置
        function updateStackChart(samplesData) {
            if (!stackChart) {
                initCharts();
            }
            
            // 确保 Other 始终在最后
            const allTaxa = [...new Set(samplesData.flatMap(sample => sample.taxa))];
            const sortedTaxa = allTaxa.filter(t => t !== 'Other');
            if (allTaxa.includes('Other')) {
                sortedTaxa.push('Other');
            }
            
            // 根据滑块值计算显示范围
            const totalSamples = samplesData.length;
            const startIndex = Math.floor((startPercent / 100) * totalSamples);
            const endIndex = Math.ceil((endPercent / 100) * totalSamples);
            
            // 对数据按照sample_id排序
            let displayData = samplesData.slice(startIndex, endIndex);
            displayData.sort((a, b) => {
                const aId = Array.isArray(a.sample_id) ? a.sample_id[0] : a.sample_id;
                const bId = Array.isArray(b.sample_id) ? b.sample_id[0] : b.sample_id;
                return aId.localeCompare(bId, undefined, {numeric: true, sensitivity: 'base'});
            });
            
            // 提取并处理样本ID，确保顺序正确
            const sampleIds = displayData.map(item => {
                const id = Array.isArray(item.sample_id) ? item.sample_id[0] : item.sample_id;
                return id.toString();
            });
            
            const option = {
                title: {
                    text: 'Composition of Taxa across All Samples',
                    left: 'center',
                    top: -5,
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 500,
                        color: '#666'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return `${params.seriesName}<br/>Abundance: ${params.value.toFixed(3)}%`;
                    },
                    axisPointer: {
                        type: 'none'
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 25,
                    left: 'center',
                    width: '90%',
                    textStyle: {
                        fontSize: 11,
                        color: '#666'
                    },
                    pageButtonPosition: 'end',
                    pageIconSize: 12,
                    pageTextStyle: {
                        color: '#666'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    top: '12%',
                    bottom: '18%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: sampleIds,
                    axisLabel: {
                        show: true,
                        interval: 0,
                        rotate: 45,
                        fontSize: function() {
                            // 根据屏幕宽度动态调整字体大小
                            return window.innerWidth < 768 ? 9 : 11;
                        }(),
                        formatter: function (value) {
                            // 根据屏幕宽度调整显示长度
                            const maxLength = window.innerWidth < 768 ? 8 : 15;
                            if (value.length > maxLength) {
                                return value.substring(0, maxLength - 3) + '...';
                            }
                            return value;
                        },
                        margin: window.innerWidth < 768 ? 5 : 8
                    },
                    axisTick: {
                        alignWithLabel: true
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#333'
                        }
                    },
                    name: '',  // 移除x轴标题
                    nameLocation: 'middle',
                    nameGap: 35
                },
                yAxis: {
                    type: 'value',
                    name: 'Relative abundance (%)',
                    nameLocation: 'middle',
                    nameGap: 40,
                    min: 0,
                    max: 100,
                    nameTextStyle: {
                        fontSize: 16,
                        fontWeight: 500,
                        color: '#666',
                        padding: [0, 0, 5, 0]
                    },
                    axisLabel: {
                        formatter: function(value) {
                            return value.toFixed(0);
                        }
                    }
                },
                series: sortedTaxa.map((taxa, index) => ({
                    name: taxa,
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    itemStyle: {
                        color: taxa === 'Other' ? '#C7C6C4' : COLORS[index % COLORS.length],
                        borderColor: '#fff',
                        borderWidth: 0.5
                    },
                    data: displayData.map(sample => {
                        const index = sample.taxa.indexOf(taxa);
                        return index !== -1 ? Number(sample.values[index]) || 0 : 0;
                    })
                }))
            };
            
            stackChart.setOption(option);
        }

        function updateSliders() {
            const startSlider = document.getElementById('startSlider');
            const endSlider = document.getElementById('endSlider');
            
            // 获取滑块值
            startPercent = parseInt(startSlider.value);
            endPercent = parseInt(endSlider.value);
            
            // 确保值在有效范围内
            startPercent = Math.max(0, Math.min(startPercent, 100));
            endPercent = Math.max(0, Math.min(endPercent, 100));
            
            // 更新标签
            const startLabel = document.getElementById('startLabel');
            const endLabel = document.getElementById('endLabel');
            if (startLabel && endLabel) {
                startLabel.textContent = `${startPercent}%`;
                endLabel.textContent = `${endPercent}%`;
            }
            
            // 如果有当前数据，更新堆叠图
            if (currentData) {
                updateStackChart(currentData);  // 传递完整数据，让 updateStackChart 处理切片
            }
        }

        function initSliders() {
            const startSlider = document.getElementById('startSlider');
            const endSlider = document.getElementById('endSlider');
            const selectedRange = document.getElementById('selectedRange');
            
            // 确保滑块的 z-index 正确
            startSlider.style.zIndex = "2";
            endSlider.style.zIndex = "2";
            
            // 设置初始值
            startSlider.value = 0;
            endSlider.value = 100;
            
            function handleSliderInput(e) {
                e.stopPropagation(); // 阻止事件冒泡
                const startValue = parseInt(startSlider.value);
                const endValue = parseInt(endSlider.value);
                
                // 根据滑块类型设置限制
                if (e.target === startSlider) {
                    if (startValue >= endValue) {
                        startSlider.value = endValue - 1;
                    }
                } else {
                    if (endValue <= startValue) {
                        endSlider.value = startValue + 1;
                    }
                }
                
                updateSliders();
            }
            
            // 添加事件监听器
            startSlider.addEventListener('input', handleSliderInput, true);
            endSlider.addEventListener('input', handleSliderInput, true);
            startSlider.addEventListener('change', handleSliderInput, true);
            endSlider.addEventListener('change', handleSliderInput, true);
            
            // 添加鼠标事件以改善拖动体验
            [startSlider, endSlider].forEach(slider => {
                slider.addEventListener('mousedown', () => {
                    document.body.style.cursor = 'grabbing';
                    slider.style.zIndex = "3"; // 提升当前拖动滑块的层级
                });
                
                slider.addEventListener('mouseup', () => {
                    document.body.style.cursor = 'default';
                    slider.style.zIndex = "2"; // 恢复原来的层级
                });
            });
            
            // 初始化选中区域
            updateSliders();
        }

        // 修改表格更新函数
        function updateTable(levelData, page = 1) {
            const tbody = document.querySelector('#taxaTable tbody');
            tbody.innerHTML = '';
            
            // 确保 all_taxa 数据存在
            if (!levelData.all_taxa) {
                console.error('No all_taxa data found:', levelData);
                return;
            }
            
            const allTaxaData = levelData.all_taxa;
            const totalItems = allTaxaData.length;
            
            // 更新总条目数显示
            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalEntries').textContent = totalItems;
            
            // 计算分页
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
            
            // 更新显示范围信息
            document.getElementById('showingStart').textContent = totalItems ? startIndex + 1 : 0;
            document.getElementById('showingEnd').textContent = endIndex;
            
            // 显示数据
            for(let i = startIndex; i < endIndex; i++) {
                const item = allTaxaData[i];
                const row = document.createElement('tr');
                const value = Number(item.value);
                const median = Number(item.median);
                row.innerHTML = `
                    <td>${item.taxa || 'Unknown'}</td>
                    <td>${!isNaN(value) ? value.toFixed(3) : 'N/A'}</td>
                    <td>${!isNaN(median) ? median.toFixed(3) : 'N/A'}</td>
                `;
                tbody.appendChild(row);
            }
            
            // 更新分页控件
            updatePagination(page, Math.ceil(totalItems / itemsPerPage), levelData);
        }

        // 改进分页控件函数
        function updatePagination(currentPage, totalPages, levelData) {
            const pagination = document.getElementById('tablePagination');
            pagination.innerHTML = '';
            
            // 添加上一页按钮
            pagination.innerHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
                </li>
            `;
            
            // 添加页码按钮
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            if (startPage > 1) {
                pagination.innerHTML += `
                    <li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>
                    ${startPage > 2 ? '<li class="page-item disabled"><span class="page-link">...</span></li>' : ''}
                `;
            }
            
            for(let i = startPage; i <= endPage; i++) {
                pagination.innerHTML += `
                    <li class="page-item ${currentPage === i ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            }
            
            if (endPage < totalPages) {
                pagination.innerHTML += `
                    ${endPage < totalPages - 1 ? '<li class="page-item disabled"><span class="page-link">...</span></li>' : ''}
                    <li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>
                `;
            }
            
            // 添加下一页按钮
            pagination.innerHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
                </li>
            `;

            // 移除旧的事件监听器
            const oldPagination = document.getElementById('tablePagination');
            const newPagination = oldPagination.cloneNode(true);
            oldPagination.parentNode.replaceChild(newPagination, oldPagination);

            // 添加新的事件监听器
            newPagination.addEventListener('click', function(e) {
                e.preventDefault();
                if (e.target.tagName === 'A' && !e.target.parentElement.classList.contains('disabled')) {
                    const newPage = parseInt(e.target.getAttribute('data-page'));
                    if (!isNaN(newPage) && newPage > 0 && newPage <= totalPages) {
                        currentPage = newPage;
                        updateTable(levelData, currentPage);
                    }
                }
            });
        }

        // 修改数据加载函数
        async function loadAndUpdateCharts(studyId, level) {
            try {
                if (!level) {
                    console.error('No level specified');
                    return;
                }

                // 显示加载状态
                document.getElementById('pieChart').style.opacity = '0.5';
                document.getElementById('stackChart').style.opacity = '0.5';
                
                // 获取当前选中的数据类型
                const activeButton = document.querySelector('.data-type-switch .btn.active');
                const dataType = activeButton ? activeButton.getAttribute('data-type') : 
                                (dataAvailability.has16S ? '16S' : 'ITS');
                
                console.log(`Loading ${dataType} data for study ${studyId}, level ${level}`);
                const response = await fetch(`../../public/results/${studyId}/${studyId}_${dataType}_taxa.json`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (!data.levels || !data.levels[level]) {
                    console.error(`No data found for level: ${level}`);
                    return;
                }

                const levelData = data.levels[level];
                
                if (!levelData.top20 || !levelData.samples || !levelData.all_taxa) {
                    console.error('Invalid data structure:', levelData);
                    return;
                }

                // 更新分类层级标题
                document.getElementById('taxaLevelHeader').textContent = level;
                
                // 保存当前数据到全局变量
                currentData = levelData.samples;
                
                // 更新图表和表格
                updatePieChart(levelData.top20);
                updateStackChart(currentData);
                updateTable(levelData, currentPage);
                
                // 恢复图表透明度
                document.getElementById('pieChart').style.opacity = '1';
                document.getElementById('stackChart').style.opacity = '1';
                
                // 更新按钮状态
                document.querySelectorAll('.btn-group button[data-level]').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.getAttribute('data-level') === level) {
                        btn.classList.add('active');
                    }
                });
            } catch (error) {
                console.error('Error loading data:', error);
                // 恢复图表透明度
                document.getElementById('pieChart').style.opacity = '1';
                document.getElementById('stackChart').style.opacity = '1';
            }
        }

        // 初始化数据类型切换
        function initDataTypeSwitch() {
            const switchContainer = document.querySelector('.data-type-switch');
            const buttonGroup = switchContainer.querySelector('.btn-group');
            buttonGroup.innerHTML = '';  // 清空现有按钮
            buttonGroup.style.width = '300px';  // 设置按钮组固定宽度
            
            // 显示容器
            switchContainer.style.display = 'block';
            
            // 根据数据可用性添加按钮
            if (dataAvailability.has16S) {
                const btn16S = document.createElement('button');
                btn16S.type = 'button';
                btn16S.className = 'btn btn-outline-primary active';
                btn16S.setAttribute('data-type', '16S');
                btn16S.textContent = 'Bacteria';
                btn16S.style.width = '50%';  // 设置按钮宽度为50%
                buttonGroup.appendChild(btn16S);
            }
            
            if (dataAvailability.hasITS) {
                const btnITS = document.createElement('button');
                btnITS.type = 'button';
                btnITS.className = 'btn btn-outline-primary' + (!dataAvailability.has16S ? ' active' : '');
                btnITS.setAttribute('data-type', 'ITS');
                btnITS.textContent = 'Fungi';
                btnITS.style.width = '50%';  // 设置按钮宽度为50%
                buttonGroup.appendChild(btnITS);
            }
            
            // 添加切换事件监听
            buttonGroup.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    // 更新按钮状态
                    buttonGroup.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 重新加载数据
                    const studyId = '<?php echo $studyId; ?>';
                    const level = document.querySelector('.level-selector .btn-group button.active').getAttribute('data-level');
                    loadAndUpdateCharts(studyId, level);
                });
            });
        }

        // 初始化分类层级切换
        function initLevelSelector() {
            const levelButtons = document.querySelectorAll('.level-selector .btn-group button');
            const studyId = '<?php echo $studyId; ?>';
            
            levelButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    // 移除所有按钮的激活状态
                    levelButtons.forEach(btn => btn.classList.remove('active'));
                    // 激活当前点击的按钮
                    this.classList.add('active');
                    // 加载对应层级的数据
                    const level = this.getAttribute('data-level');
                    console.log('Loading level:', level); // 调试用
                    loadAndUpdateCharts(studyId, level);
                });
            });
        }

        // 在页面加载时初始化
        window.addEventListener('load', function() {
            initCharts();
            initDataTypeSwitch();
            initLevelSelector();
            
            const studyId = '<?php echo $studyId; ?>';
            
            // 初始化滑块
            initSliders();
            
            // 加载初始数据
            const defaultLevel = 'Genus';
            document.querySelector(`[data-level="${defaultLevel}"]`).classList.add('active');
            loadAndUpdateCharts(studyId, defaultLevel);
        });

        // 监听窗口大小变化，调整图表大小
        window.addEventListener('resize', function() {
            if (pieChart) {
                pieChart.resize();
                // 重新渲染饼图以适应新的容器尺寸
                if (window.currentPieData) {
                    updatePieChart(window.currentPieData);
                }
            }
            if (stackChart) {
                stackChart.resize();
                // 重新加载数据以更新标签大小
                if (currentData) {
                    updateStackChart(currentData);
                }
            }
        });

        // 添加分页事件监听
        document.getElementById('tablePagination').addEventListener('click', function(e) {
            e.preventDefault();
            if (e.target.tagName === 'A') {
                const page = parseInt(e.target.getAttribute('data-page'));
                if (!isNaN(page) && page > 0) {
                    currentPage = page;
                    const studyId = '<?php echo $studyId; ?>';
                    const level = document.querySelector('.btn-group button.active').getAttribute('data-level');
                    loadAndUpdateCharts(studyId, level);
                }
            }
        });

        // 修改饼图更新函数，实现响应式图例显示
        function updatePieChart(top20Data) {
            if (!pieChart) {
                initCharts();
            }

            // 检测饼图容器宽度来决定图例显示方式
            const pieContainer = document.getElementById('pieChart');
            const containerWidth = pieContainer.offsetWidth;
            const isSmallContainer = containerWidth < 600; // 当容器宽度小于600px时隐藏图例

            const option = {
                title: {
                    text: 'TOP20 Taxa',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 500,
                        color: '#666'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        // 在图例隐藏时，tooltip显示更详细的信息
                        if (isSmallContainer) {
                            return `<div style="max-width: 200px;">
                                <strong>${params.name}</strong><br/>
                                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>
                                ${params.value}% (${params.percent}%)
                            </div>`;
                        } else {
                            return `${params.name}: ${params.value}%`;
                        }
                    },
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#ccc',
                    borderWidth: 1,
                    textStyle: {
                        color: '#333'
                    }
                },
                legend: isSmallContainer ? {
                    show: false  // 小容器时隐藏图例
                } : {
                    type: 'scroll',
                    orient: 'vertical',
                    right: '15%',
                    top: 50,
                    bottom: 20,
                    width: '30%',
                    textStyle: {
                        fontSize: 12
                    },
                    pageIconSize: 12,
                    pageTextStyle: {
                        color: '#666'
                    }
                },
                series: [{
                    name: 'Taxa Distribution',
                    type: 'pie',
                    radius: isSmallContainer ? '70%' : '65%',
                    center: isSmallContainer ? ['50%', '50%'] : ['30%', '50%'],  // 小容器时居中显示
                    data: top20Data.map((item, index) => ({
                        name: item.taxa,
                        value: Number(item.value) || 0,
                        itemStyle: {
                            color: item.taxa === 'Other' ? '#666666' : COLORS[index % COLORS.length]
                        }
                    })),
                    label: {
                        show: isSmallContainer,  // 小容器时显示标签
                        position: 'outside',
                        formatter: function(params) {
                            // 只显示百分比大于3%的标签，避免重叠
                            return params.percent > 3 ? `${params.name}\n${params.percent}%` : '';
                        },
                        fontSize: 10,
                        lineHeight: 12
                    },
                    labelLine: {
                        show: isSmallContainer,
                        length: 10,
                        length2: 5
                    },
                    emphasis: {
                        focus: 'self',
                        label: {
                            show: true,
                            formatter: '{b}: {d}%',
                            fontSize: 14
                        },
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };

            pieChart.setOption(option);

            // 保存当前饼图数据，用于窗口大小改变时重新渲染
            window.currentPieData = top20Data;
        }

        // 添加每页显示数量选择事件
        document.getElementById('itemsPerPage').addEventListener('change', function(e) {
            itemsPerPage = parseInt(e.target.value);
            currentPage = 1;  // 重置到第一页
            const studyId = '<?php echo $studyId; ?>';
            const level = document.querySelector('.btn-group button[data-level].active').getAttribute('data-level');
            loadAndUpdateCharts(studyId, level);
        });

        // 添加下载按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            // 下载 OTU 表
            document.getElementById('downloadOtuBtn').addEventListener('click', function() {
                const studyId = '<?php echo $studyId; ?>';
                const activeButton = document.querySelector('.data-type-switch .btn.active');
                const dataType = activeButton ? (activeButton.getAttribute('data-type') === '16S' ? '16S' : 'ITS') : '16S';
                downloadData(studyId, dataType, 'otu');
            });
            
            // 下载 Taxonomy 表
            document.getElementById('downloadTaxonomyBtn').addEventListener('click', function() {
                const studyId = '<?php echo $studyId; ?>';
                const activeButton = document.querySelector('.data-type-switch .btn.active');
                const dataType = activeButton ? (activeButton.getAttribute('data-type') === '16S' ? '16S' : 'ITS') : '16S';
                downloadData(studyId, dataType, 'taxonomy');
            });
        });

        // 修改下载数据函数
        function downloadData(studyId, dataType, tableType) {
            // 构建文件名
            const fileName = `${studyId}_${dataType}_${tableType === 'otu' ? 'otu_table' : 'taxonomy'}.tsv`;
            // 构建文件路径
            const filePath = `../../public/download/${fileName}`;
            
            // 创建一个隐藏的 a 标签并触发点击
            const link = document.createElement('a');
            link.href = filePath;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 侧边栏回顶部按钮功能
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarBackToTopBtn = document.getElementById('sidebarBackToTop');

            if (sidebarBackToTopBtn) {
                sidebarBackToTopBtn.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // 保存当前Study页面URL到localStorage，供herb_detail页面返回使用（跨标签页共享）
            localStorage.setItem('studyPageUrl', window.location.href);

            // 检查是否需要恢复滚动位置
            if (localStorage.getItem('shouldRestoreScroll') === 'true') {
                const savedScrollPosition = localStorage.getItem('studyPageScrollPosition');
                if (savedScrollPosition) {
                    // 延迟恢复滚动位置，确保页面完全加载
                    setTimeout(() => {
                        window.scrollTo(0, parseInt(savedScrollPosition));
                    }, 100);
                }
                // 清除恢复标记
                localStorage.removeItem('shouldRestoreScroll');
            }

            // 为herb detail链接添加点击事件，保存当前页面信息
            document.querySelectorAll('.herb-detail-link').forEach(link => {
                link.addEventListener('click', function() {
                    // 保存当前页面URL
                    localStorage.setItem('studyPageUrl', window.location.href);

                    // 保存当前滚动位置
                    localStorage.setItem('studyPageScrollPosition', window.pageYOffset);
                });
            });
        });
    </script>
<!--     <script src="./js/bootstrap-table-zh-CN.js"></script> --> <!-- 中文包 -->
</body>
</html>