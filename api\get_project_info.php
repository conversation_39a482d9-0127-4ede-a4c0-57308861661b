<?php
// 关闭PHP错误显示
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', '../logs/php_error.log');

// 设置响应头为JSON
header('Content-Type: application/json');

// 获取项目ID
$project_id = isset($_GET['project_id']) ? $_GET['project_id'] : '';
error_log("API 请求接收: project_id = $project_id");

if (empty($project_id)) {
    http_response_code(400);
    echo json_encode(['error' => 'Project ID is required']);
    exit;
}

// 格式化项目ID - 提取数字部分，然后重新格式化为标准形式
if (preg_match('/(\d+)/', $project_id, $matches)) {
    $project_number = $matches[1];
    $project_id = "MPRAMID01-" . $project_number;
} else if (strpos($project_id, 'MPRAMID01-') !== 0) {
    $project_id = 'MPRAMID01-' . preg_replace('/^MPRAMID01-/', '', $project_id);
}

// 记录调试信息
error_log("Project info API: 处理后的ID = $project_id");

// 从CSV文件获取数据
$csv_file = '../Network_info.csv';
error_log("尝试读取CSV文件: $csv_file");

// 如果找不到csv文件，尝试其他可能的位置
if (!file_exists($csv_file)) {
    $csv_file = dirname(__FILE__) . '/../Network_info.csv';
    error_log("尝试读取替代路径1: $csv_file");
    
    if (!file_exists($csv_file)) {
        $csv_file = dirname(__FILE__) . '/../../Network_info.csv';
        error_log("尝试读取替代路径2: $csv_file");
        
        if (!file_exists($csv_file)) {
            $csv_file = '../public/Network_info.csv';
            error_log("尝试读取替代路径3: $csv_file");
            
            if (!file_exists($csv_file)) {
                $csv_file = '../../../Network_info.csv';
                error_log("尝试读取替代路径4: $csv_file");
            }
        }
    }
}

$result = array(
    'latin_name' => '-',
    'english_name' => '-',
    'chinese_name' => '-',
    'soil_treatment_groups' => '-'
);

if (file_exists($csv_file)) {
    error_log("CSV文件存在: $csv_file");
    try {
        // 打开CSV文件
        $file = fopen($csv_file, 'r');
        if ($file) {
            error_log("CSV文件打开成功");
            // 读取标题行
            $header = fgetcsv($file);
            error_log("CSV标题行: " . implode(',', $header));
            
            // 检查CSV文件格式是否正确
            if ($header && count($header) >= 4) {
                // 确定每列的索引
                $study_id_idx = array_search('Study ID', $header);
                $latin_name_idx = array_search('Latin Name', $header);
                $chinese_name_idx = array_search('Chinese Name', $header);
                $soil_groups_idx = array_search('Soil Treatment Groups', $header);
                
                error_log("列索引: StudyID=$study_id_idx, Latin=$latin_name_idx, Chinese=$chinese_name_idx, Soil=$soil_groups_idx");
                
                // 如果找到了所有必要的列
                if ($study_id_idx !== false && $latin_name_idx !== false && 
                    $chinese_name_idx !== false && $soil_groups_idx !== false) {
                    
                    // 逐行读取CSV文件
                    error_log("开始搜索项目 $project_id");
                    $found = false;
                    
                    while (($row = fgetcsv($file)) !== false) {
                        // 检查这一行是否包含项目ID
                        if (isset($row[$study_id_idx])) {
                            $current_id = trim($row[$study_id_idx]);
                            
                            // 提取项目编号以进行比较
                            preg_match('/(\d+)/', $current_id, $current_matches);
                            preg_match('/(\d+)/', $project_id, $search_matches);
                            
                            $current_num = isset($current_matches[1]) ? $current_matches[1] : '';
                            $search_num = isset($search_matches[1]) ? $search_matches[1] : '';
                            
                            error_log("比较: 当前行=$current_id (num=$current_num) vs 搜索=$project_id (num=$search_num)");
                            
                            if ($current_id === $project_id || ($current_num && $search_num && $current_num === $search_num)) {
                                error_log("找到匹配项!");
                                // 找到匹配的项目，填充结果
                                $result['latin_name'] = isset($row[$latin_name_idx]) ? $row[$latin_name_idx] : '-';
                                $result['chinese_name'] = isset($row[$chinese_name_idx]) ? $row[$chinese_name_idx] : '-';
                                $result['soil_treatment_groups'] = isset($row[$soil_groups_idx]) ? $row[$soil_groups_idx] : '-';
                                // English Name不在CSV中，使用Latin Name代替
                                $result['english_name'] = $result['latin_name'];
                                $found = true;
                                break;
                            }
                        }
                    }
                    
                    if (!$found) {
                        error_log("未找到项目 $project_id");
                    }
                } else {
                    error_log("CSV文件格式不正确，缺少必要的列");
                }
            } else {
                error_log("CSV文件格式不正确，列数不足");
            }
            fclose($file);
        } else {
            error_log("无法打开CSV文件");
        }
    } catch (Exception $e) {
        error_log("CSV读取错误: " . $e->getMessage());
        echo json_encode(['error' => 'CSV读取错误', 'details' => $e->getMessage()]);
        exit;
    }
} else {
    error_log("CSV文件不存在: $csv_file");
}

error_log("返回结果: " . json_encode($result));
echo json_encode($result);
?> 