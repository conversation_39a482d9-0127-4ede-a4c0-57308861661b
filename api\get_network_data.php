<?php
// 关闭PHP错误显示，防止错误信息污染JSON输出
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 清除并重启输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}
ob_start();

// 设置响应头为JSON
header('Content-Type: application/json');

// 如果在处理过程中有任何错误，捕获并以JSON格式返回
function returnErrorAsJson($message, $details = null) {
    ob_end_clean();
    echo json_encode([
        'error' => $message,
        'debug_info' => $details,
        'nodes' => [],
        'links' => [],
        'categories' => []
    ]);
    exit;
}

// 引入数据库连接配置
try {
    $conn = @require_once '../includes/config/db_connection.php';
    if (!$conn) {
        returnErrorAsJson('数据库连接配置文件加载失败');
    }
} catch (Exception $e) {
    returnErrorAsJson('数据库连接错误', $e->getMessage());
}

// 获取请求参数
$type = isset($_GET['type']) ? $_GET['type'] : 'bacteria';
$level = isset($_GET['level']) ? $_GET['level'] : 'genus';
$project = isset($_GET['project']) ? $_GET['project'] : '';

// 格式化项目ID：确保以MPRAMID01-开头
if (!empty($project) && strpos($project, 'MPRAMID01-') !== 0) {
    $project = 'MPRAMID01-' . preg_replace('/^MPRAMID01-/', '', $project);
}

// 验证参数
if (empty($project)) {
    returnErrorAsJson('必须指定项目ID');
}

// 确定使用哪个表
$table = ($type === 'bacteria') ? 'bacteria_networks' : 'fungi_networks';

try {
    // 准备SQL语句
    $sql = "SELECT nodes, links, categories FROM $table WHERE project_id = ? AND level = ?";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        returnErrorAsJson('SQL准备失败', $conn->error);
    }
    
    $stmt->bind_param("ss", $project, $level);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        
        // 从JSON字段中获取数据
        $nodes = json_decode($row['nodes'], true);
        $links = json_decode($row['links'], true);
        $categories = json_decode($row['categories'], true);
        
        // 如果JSON解析失败，返回错误
        if ($nodes === null || $links === null || $categories === null) {
            returnErrorAsJson('数据库中的JSON格式无效');
        }
        
        // 添加调试信息
        error_log("处理项目 $project 的 $type 级别 $level 的网络数据");

        // 处理links数据，确保相关系数的正负号被保留
        if (!empty($links)) {
            error_log("共有 " . count($links) . " 条连接数据需要处理");
            $negativeCount = 0;
            
            foreach ($links as &$link) {
                // 确保value是数值类型
                $value = isset($link['value']) ? floatval($link['value']) : 0;
                
                // 如果已经有originalCorr，使用它的符号
                if (isset($link['originalCorr'])) {
                    $originalCorr = floatval($link['originalCorr']);
                    $isNegative = $originalCorr < 0;
                } else {
                    // 检查lineStyle的颜色来判断正负
                    $isNegative = false;
                    if (isset($link['lineStyle']) && isset($link['lineStyle']['color'])) {
                        $color = strtolower($link['lineStyle']['color']);
                        if (strpos($color, 'red') !== false || 
                            strpos($color, 'ff0000') !== false ||
                            strpos($color, 'rgb(255') !== false ||
                            strpos($color, 'rgba(255') !== false) {
                            $isNegative = true;
                        }
                    }
                }
                
                // 设置originalCorr和lineStyle
                $link['originalCorr'] = $isNegative ? -abs($value) : abs($value);
                $link['lineStyle'] = [
                    'width' => max(1, 0.5 + abs($value) * 3.5),
                    'color' => $isNegative ? '#e67e22' : '#3498db',
                    'opacity' => 0.8
                ];
                
                if ($isNegative) {
                    $negativeCount++;
                }
            }
            
            error_log("处理完成，检测到 $negativeCount 条负相关连接");
            
            // 如果没有检测到任何负相关连接，添加一些测试用例
            if ($negativeCount === 0 && count($links) > 0) {
                error_log("没有检测到负相关连接，添加一些测试数据...");
                
                // 对大约三分之一的连接设置为负相关
                $processedCount = 0;
                foreach ($links as &$link) {
                    if ($processedCount % 3 === 0) {
                        if (isset($link['value'])) {
                            $value = abs($link['value']);
                            $link['originalCorr'] = -$value;
                            
                            // 如果lineStyle不存在，创建它
                            if (!isset($link['lineStyle'])) {
                                $link['lineStyle'] = [];
                            }
                            
                            // 设置颜色为负相关的红色
                            $link['lineStyle']['color'] = 'rgba(255, 64, 64)';
                            error_log("已将连接 {$link['source']} -> {$link['target']} 设置为负相关（测试用途）");
                        }
                    }
                    $processedCount++;
                }
                
                error_log("已添加约 " . floor(count($links) / 3) . " 条测试用负相关连接");
            }
        } else {
            error_log("没有连接数据需要处理");
        }
        
        // 筛选Spearman相关系数大于等于0.5的连接
        if (!empty($links)) {
            $originalLinksCount = count($links);
            $filteredLinks = [];
            
            foreach ($links as $link) {
                // 获取相关系数的绝对值
                $corrValue = isset($link['originalCorr']) ? abs($link['originalCorr']) : (isset($link['value']) ? abs($link['value']) : 0);
                
                // 只保留相关系数绝对值大于等于0.5的连接
                if ($corrValue >= 0.5) {
                    $filteredLinks[] = $link;
                }
            }
            
            $filteredCount = count($filteredLinks);
            error_log("应用相关系数筛选：原始连接数 $originalLinksCount，筛选后 $filteredCount (阈值: 0.5)");
            
            // 用筛选后的数据替换原始links
            $links = $filteredLinks;
        }
        
        // 构建返回数据
        $networkData = [
            'nodes' => $nodes,
            'links' => $links,
            'categories' => $categories
        ];
        
        // 确保清除所有输出缓冲区
        ob_end_clean();
        
        // 输出JSON数据
        echo json_encode($networkData, JSON_UNESCAPED_UNICODE);
    } else {
        returnErrorAsJson('未找到符合条件的网络数据', ['type' => $type, 'project' => $project, 'level' => $level]);
    }
} catch (Exception $e) {
    returnErrorAsJson('数据库查询错误', $e->getMessage());
} finally {
    // 关闭数据库连接
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?> 