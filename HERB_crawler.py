# -*- coding: utf-8 -*-
"""
herb数据库爬虫 下载药效 疾病 靶点相关信息 
新版HERB数据库抓取脚本 - v10 (最终API版)
作者: Gemini
描述: 此版本为最终版。根据用户提供的正确API请求载荷，脚本不再访问网页，而是直接向后端API
      发送带有正确JSON负载的POST请求来获取原始数据，从根本上解决了所有数据抓取失败的问题。
"""

import os
import time
import pandas as pd
import requests
from urllib.parse import urljoin
import json

# --- 1. 全局配置 (请根据您的实际情况修改) ---

# 包含Herb ID的文本文件路径，此文件将作为唯一的任务源
ID_FILE_PATH = r"D:\Software\XAMPP\htdocs\MPRAM\2.txt"

# 最终所有数据文件夹的存放根目录
MAIN_OUTPUT_DIR = r"D:\Software\XAMPP\htdocs\MPRAM\HRRB"

# 目标数据库的基础URL
BASE_URL = "http://47.92.70.12/"
# (新) 正确的API端点
API_URL = urljoin(BASE_URL, "chedi/api/")

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'X-Requested-With': 'XMLHttpRequest', # 模拟AJAX请求
    'Content-Type': 'application/json;charset=UTF-8', # 明确指定发送的是JSON
    'Referer': BASE_URL
}

def fetch_and_save_data(session, herb_id, herb_name, output_folder):
    """
    (全新核心函数) 使用会话对象，向API发送带有正确JSON负载的POST请求。
    """
    # 1. 准备POST请求需要发送的JSON数据
    post_payload = {
        "v": herb_id,
        "label": "Herb",
        "key_id": herb_id,
        "func_name": "detail_api"
    }
    print(f"  [请求] 正在为 {herb_name} 发送POST请求...")

    try:
        # 2. 使用会话对象发起POST请求，使用 json=... 来发送JSON负载
        response = session.post(API_URL, headers=HEADERS, json=post_payload, timeout=30)
        response.raise_for_status()
        
        # 3. 解析返回的JSON数据
        json_response = response.json()
        
        # API返回的数据本身又是一个JSON字符串，需要再次解析
        if 'data' in json_response and isinstance(json_response['data'], str):
            json_data = json.loads(json_response['data'])
        else:
            json_data = json_response

        # 定义要抓取的数据表和对应的文件名
        tables_to_process = {
            'herb_ingredient': 'ingredients.csv',
            'herb_target': 'targets.csv',
            'herb_disease': 'diseases.csv'
        }

        for data_key, filename in tables_to_process.items():
            if data_key in json_data and json_data[data_key] and len(json_data[data_key]) > 1:
                table_data = json_data[data_key]
                header = table_data[0]
                rows = table_data[1:]
                
                processed_rows = []
                for row in rows:
                    processed_row = list(row)
                    if isinstance(processed_row[0], dict) and 'title' in processed_row[0]:
                        processed_row[0] = processed_row[0]['title']
                    processed_rows.append(processed_row)
                
                df = pd.DataFrame(processed_rows, columns=header)
                output_path = os.path.join(output_folder, filename)
                df.to_csv(output_path, index=False, encoding='utf-8-sig')
                print(f"  [成功] {data_key} 数据 ({len(df)}行) 已保存到: {filename}")
            else:
                print(f"  [警告] 在API响应中未找到或 '{data_key}' 数据为空。")
                
    except requests.exceptions.RequestException as e:
        print(f"  [网络错误] 获取数据时出错: {e}")
    except json.JSONDecodeError:
         print(f"  [解析错误] 服务器返回的不是有效的JSON数据。可能是空内容或错误页面。")
    except Exception as e:
        print(f"  [程序错误] 处理 {herb_name} 的数据时出错: {e}")


def main():
    """主函数，执行整个抓取流程"""
    
    print(f"--- 正在加载任务文件: {ID_FILE_PATH} ---")
    if not os.path.exists(ID_FILE_PATH):
        print(f"[严重错误] ID文件不存在: {ID_FILE_PATH}")
        return

    try:
        herb_list_df = pd.read_csv(ID_FILE_PATH, sep='\t', header=None, names=['chinese_name', 'herb_id'])
        herb_list_df.dropna(inplace=True)
        herb_list_df['chinese_name'] = herb_list_df['chinese_name'].str.strip()
        herb_list_df['herb_id'] = herb_list_df['herb_id'].str.strip()
        print(f"成功加载 {len(herb_list_df)} 味中药作为处理目标。")
    except Exception as e:
        print(f"[严重错误] 读取或解析ID文件时出错: {e}")
        return

    os.makedirs(MAIN_OUTPUT_DIR, exist_ok=True)
    print(f"\n--- 所有数据将被保存在根目录: {MAIN_OUTPUT_DIR} ---")
    
    with requests.Session() as session:
        print("\n--- 正在初始化会话... ---")
        try:
            session.get(BASE_URL, headers=HEADERS, timeout=30)
            print("会话初始化成功。")
        except requests.exceptions.RequestException as e:
            print(f"[严重警告] 初始化会话失败: {e}。后续请求可能因此失败。")

        for index, row in herb_list_df.iterrows():
            chinese_name = row['chinese_name']
            herb_id = row['herb_id']
            
            if 'name' in chinese_name.lower() or 'id' in herb_id.lower():
                continue
            
            print(f"\n({index + 1}/{len(herb_list_df)}) 正在处理: {chinese_name} (ID: {herb_id})")

            safe_folder_name = "".join(c for c in chinese_name if c.isalnum() or c in (' ', '_')).rstrip()
            herb_folder_path = os.path.join(MAIN_OUTPUT_DIR, safe_folder_name)
            os.makedirs(herb_folder_path, exist_ok=True)
            print(f"  [信息] 已创建/确认文件夹: {herb_folder_path}")

            fetch_and_save_data(session, herb_id, chinese_name, herb_folder_path)
            
            time.sleep(1) 

    print("\n--- 所有任务处理完成！---")

if __name__ == "__main__":
    main()
