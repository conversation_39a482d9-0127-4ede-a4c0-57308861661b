<?php
// Include database connection file
$conn = require_once(__DIR__ . '/../../includes/config/db_connection.php');

// Initialize variables
$result = null;
$has_search = false;
$error_message = '';
$cache_file = __DIR__ . '/../../cache/dropdown_data.json';
$cache_lifetime = 3600; // 1 hour cache

// Function to get dropdown data
function getDropdownData($conn) {
    global $cache_file, $cache_lifetime;
    
    // Check if cache exists and is valid
    if (file_exists($cache_file) && (time() - filemtime($cache_file) < $cache_lifetime)) {
        return json_decode(file_get_contents($cache_file), true);
    }
    
    $dropdowns = [
        'study_names' => [],
        'soil_treatments' => [],
        'cultivation_practices' => [],
        'effect_categories' => [],
        'sources' => [],
        'provinces' => [],
        'countries' => [],
        'studied_microbes_list' => []
    ];
    
    // Prepare single query to get all distinct values
    $fields = [
        ['Study Name', 'study_names'],
        ['Soil Treatment Groups', 'soil_treatments'],
        ['Cultivation Practice', 'cultivation_practices'],
        ['Effect Category', 'effect_categories'],
        ['Source', 'sources'],
        ['Province', 'provinces'],
        ['Country', 'countries'],
        ['Studied microbes', 'studied_microbes_list']
    ];
    
    foreach ($fields as $field) {
        $sql = "SELECT DISTINCT `{$field[0]}` FROM `advanced search` 
                WHERE `{$field[0]}` IS NOT NULL AND `{$field[0]}` != '' 
                ORDER BY `{$field[0]}`";
        $result = $conn->query($sql);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $dropdowns[$field[1]][] = $row[$field[0]];
            }
        }
    }
    
    // Cache the results
    if (!is_dir(dirname($cache_file))) {
        mkdir(dirname($cache_file), 0777, true);
    }
    file_put_contents($cache_file, json_encode($dropdowns));
    
    return $dropdowns;
}

// Get dropdown data
$dropdowns = getDropdownData($conn);
extract($dropdowns);

// Check if form has been submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" || !empty($_GET)) {
    $has_search = true;
    
    // Build query parts
    $conditions = [];
    $params = [];
    $types = '';
    
    // Function to add condition
    function addCondition($field, $value, &$conditions, &$params, &$types, $like = false) {
        if (!empty($value)) {
            // Check for exact match mode from example click
            $exactMatch = isset($_POST['exact_match']) || isset($_GET['exact_match']);
            
            if ($like && !$exactMatch) {
                $conditions[] = "`{$field}` LIKE ?";
                $params[] = "%$value%";
            } else {
                $conditions[] = "`{$field}` = ?";
                $params[] = $value;
            }
            $types .= 's';
        }
    }

    // Handle both POST and GET parameters
    $search_fields = [
        'search_query' => ['field' => $_POST['search_by'] ?? 'Study ID', 'like' => true],
        'study_name' => ['field' => 'Study Name', 'like' => true],
        'studied_microbes' => ['field' => 'Studied microbes', 'like' => false],
        'soil_treatment' => ['field' => 'Soil Treatment Groups', 'like' => false],
        'cultivation_practice' => ['field' => 'Cultivation Practice', 'like' => false],
        'effect_category' => ['field' => 'Effect Category', 'like' => false],
        'source' => ['field' => 'Source', 'like' => true],
        'province' => ['field' => 'Province', 'like' => false],
        'country' => ['field' => 'Country', 'like' => false]
    ];
    
    // Process POST parameters
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        foreach ($search_fields as $post_key => $field_info) {
            if (!empty($_POST[$post_key])) {
                addCondition($field_info['field'], $_POST[$post_key], $conditions, $params, $types, $field_info['like']);
            }
        }
    }
    
    // Process GET parameters
    foreach ($search_fields as $get_key => $field_info) {
        if (isset($_GET[$get_key])) {
            addCondition($field_info['field'], $_GET[$get_key], $conditions, $params, $types, $field_info['like']);
        }
    }
    
    // Build the final query with DISTINCT to avoid duplicates
    $sql = "SELECT DISTINCT `Study ID`, `Study Name`, `Chinese Name`, `English Name`, 
            `Studied microbes`, `Soil Treatment Groups`, `Cultivation Practice`, 
            `Effect Category`, `Source` as `Pharmacopoeia`, `Province`, `Country` 
            FROM `advanced search`";
    
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    
    $sql .= " GROUP BY `Study ID`, `Study Name`, `Chinese Name`, `English Name`, 
              `Studied microbes`, `Soil Treatment Groups`, `Cultivation Practice`, 
              `Effect Category`, `Source`, `Province`, `Country`";
    
    // Add ORDER BY clause to prioritize exact matches first when clicking on examples
    if (isset($_POST['exact_match']) || isset($_GET['exact_match'])) {
        foreach ($search_fields as $field_key => $field_info) {
            if (!empty($_POST[$field_key]) || !empty($_GET[$field_key])) {
                $match_field = $field_info['field'];
                $match_value = !empty($_POST[$field_key]) ? $_POST[$field_key] : $_GET[$field_key];
                $sql .= " ORDER BY `{$match_field}` = '{$match_value}' DESC";
                break;
            }
        }
    }
    
    // Execute query
    try {
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }
        
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result === false) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// 如果是通过GET请求来的，自动选中相应的下拉框
$auto_select_fields = [
    'study_name', 'soil_treatment', 'cultivation_practice', 
    'effect_category', 'studied_microbes', 'source', 
    'province', 'country'
];

foreach ($auto_select_fields as $field) {
    if (isset($_GET[$field])) {
        ${$field . '_selected'} = $_GET[$field];
    }
}

// Close database connection
$conn->close();

// Start output buffering
ob_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Search - MPRAM</title>
    <!-- 添加favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">
    
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/bootstrap-table.min.css">
    <link rel="stylesheet" href="../../public/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Fix body::before top space issue */
        body::before {
            content: none !important;
            display: none !important;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            will-change: scroll-position; /* 优化滚动性能 */
        }
        
        .container {
            max-width: 100% !important;
            padding: 2rem !important;
            margin: 0 auto !important;
            width: 100% !important;
            flex: 1;
        }
        
        /* Content section style */
        .content-section {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
            margin-top: 80px;
            width: 98%;
            max-width: 2200px;
            margin-left: auto;
            margin-right: auto;
            /* transition: all 0.3s ease; */ /* 移除平滑过渡 */
        }
        
        /* Section Title */
        .section-title {
            text-align: center;
            font-size: 1.5rem;
            color: #002060;
            font-weight: 600;
            margin: 30px 0 20px 0;
            padding-bottom: 0px;
            border-bottom: none;
        }
        
        /* 两栏布局样式 */
        .two-column-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            width: 100%;
        }
        
        /* 左侧搜索表单样式 */
        .search-column {
            flex: 0 0 22%;
            max-width: 22%;
            position: sticky;
            top: 100px; /* 滚动时固定位置 */
            height: 100%;
            min-height: 600px;
            z-index: 10;
            /* transition: height 0.3s ease; */ /* 移除高度过渡动画 */
        }
        
        /* 右侧结果表格样式 */
        .results-column {
            flex: 0 0 76%;
            max-width: 76%;
            display: flex;
            flex-direction: column;
            min-height: 600px;
        }
        
        /* 结果卡片自动适应高度 */
        .results-card {
            display: flex;
            flex-direction: column;
            height: 100%;
            flex-grow: 1;
            /* transition: height 0.3s ease; */ /* 移除高度过渡动画 */
        }
        
        /* 表格容器自动适应高度 */
        .results-card .table-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        /* 移动端响应式布局 */
        @media (max-width: 992px) {
            .search-column, .results-column {
                flex: 0 0 100%;
                max-width: 100%;
                min-height: auto;
            }
            
            .search-column {
                position: relative;
                top: 0;
                height: auto;
                margin-bottom: 20px;
            }
        }
        
        /* Search form container */
        #search-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin: 0 0 20px 0;
            width: 100%;
            padding: 20px;
            border: 1px solid #dee2e6;
            /* transition: all 0.3s ease; */ /* 移除过渡动画 */
        }
        
        /* 添加悬停效果 */
        #search-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }
        
        /* Search form title */
        .search-form-title {
            font-size: 1.2rem;
            color: #002060;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            font-weight: 500;
        }
        
        .form-label {
            color: #333;
            font-weight: 500;
            margin-bottom: 0.25rem; /* 减少间距 */
        }
        
        .form-select, .form-control {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            /* transition: border-color 0.2s ease; */ /* 移除边框颜色过渡 */
        }
        
        .form-select:focus, .form-control:focus {
            border-color: #002060;
            box-shadow: 0 0 0 0.2rem rgba(0, 32, 96, 0.25);
        }
        
        /* 下拉框加载中样式 - 已禁用动画 */
        .select-loading {
            position: relative;
        }

        .select-loading:after {
            content: '';
            display: block;
            position: absolute;
            right: 10px;
            top: calc(50% - 8px);
            width: 16px;
            height: 16px;
            border: 2px solid #ccc;
            border-top-color: #002060;
            border-radius: 50%;
            /* animation: spin 0.8s linear infinite; */ /* 移除旋转动画 */
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 下拉框视觉优化 */
        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23002060' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            /* transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out, background-position 0.2s; */ /* 移除过渡动画 */
        }
        
        .form-select:focus {
            background-position: right 0.5rem center;
        }
        
        /* 优化表单间距 */
        .mb-3 {
            margin-bottom: 0.75rem !important;
        }
        
        /* Form buttons container */
        .form-buttons {
            margin-top: 20px;
            text-align: right;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        
        .btn-primary {
            background-color: #002060;
            border-color: #002060;
            /* transition: all 0.2s ease; */ /* 移除按钮过渡动画 */
        }
        
        .btn-primary:hover {
            background-color: #001040;
            border-color: #001040;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            /* transition: all 0.2s ease; */ /* 移除按钮过渡动画 */
        }
        
        .btn-secondary:hover {
            transform: translateY(-1px);
        }
        
        /* Button with icon */
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-icon i {
            margin-right: 8px;
        }
        
        h1 {
            color: #002060;
            margin: 30px 0 20px 0;
            font-weight: 600;
        }
        
        /* Table container styles */
        .table-container {
            margin-top: 0;
            overflow-x: auto;
            width: 100%;
        }
        
        #toolbar {
            margin-bottom: 15px;
            text-align: right;
        }
        
        .bootstrap-table .fixed-table-toolbar .columns-dropdown {
            margin-left: 5px;
        }
        
        .bootstrap-table .fixed-table-toolbar .search {
            margin-bottom: 10px;
        }
        
        .bootstrap-table .table {
            font-size: 14px;
            width: 100% !important;
        }
        
        .bootstrap-table .table td,
        .bootstrap-table .table th {
            padding: 0.5rem;
            white-space: nowrap;
            text-align: center; /* Center align all text */
            vertical-align: middle; /* Vertical center alignment */
        }
        
        .bootstrap-table .table thead th {
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
            text-align: center; /* Center header text */
            font-weight: 600; /* Slightly bold headers */
        }
        
        /* Study ID link style */
        .study-id-link {
            color: #002060 !important; /* Deep blue color */
            font-weight: bold; /* Bold text */
            text-decoration: none !important; /* Remove underline */
            /* transition: all 0.2s ease; */ /* 移除链接过渡动画 */
        }
        
        .study-id-link:hover {
            opacity: 0.8; /* Slightly fade on hover */
            color: #001040 !important;
        }
        
        /* Latin name italic style */
        .latin-name {
            font-style: italic;
        }
        
        .row.justify-content-center {
            width: 100%;
            margin: 0;
            padding: 0;
        }
        
        /* 优化表格滚动性能 */
        .fixed-table-body {
            overflow-x: auto;
            overflow-y: auto;
            width: 100%;
            will-change: transform;
            -webkit-overflow-scrolling: touch;
        }
        
        /* Button icon styles */
        .bootstrap-table .fixed-table-toolbar .bs-bars,
        .bootstrap-table .fixed-table-toolbar .columns,
        .bootstrap-table .fixed-table-toolbar .search {
            margin-left: 5px;
            margin-right: 5px;
        }
        
        .bootstrap-table .fixed-table-toolbar button i {
            margin-right: 5px;
        }
        
        /* 优化分页样式 */
        .page-item.active .page-link {
            background-color: #002060 !important;
            border-color: #002060 !important;
            color: white !important;
        }
        
        .page-link {
            color: #002060 !important;
            /* transition: all 0.2s ease; */ /* 移除分页过渡动画 */
        }
        
        .page-link:hover {
            background-color: #f0f2f5;
            transform: translateY(-1px);
        }
        
        /* 加载动画 */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            background-color: rgba(255,255,255,0.8);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
            color: #002060;
        }
        
        .search-results-summary {
            margin: 20px 0;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
        }
        
        /* 错误消息样式 */
        .error-message {
            color: #dc3545;
            padding: 10px;
            margin: 10px 0;
            background-color: #f8d7da;
            border-radius: 4px;
            text-align: center;
            /* animation: fadeIn 0.3s; */ /* 移除淡入动画 */
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 结果表格卡片样式 */
        .results-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            padding: 20px;
            border: 1px solid #dee2e6;
            /* transition: all 0.3s ease; */ /* 移除卡片过渡动画 */
        }
        
        .results-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
        }
        
        /* 结果表格标题样式 */
        .results-title {
            font-size: 1.2rem;
            color: #002060;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        /* 帮助信息卡片样式 */
        .help-card {
            background-color: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }
        
        .help-content {
            padding: 10px;
            height: 100%;
            overflow-y: auto;
        }
        
        .help-section {
            margin-bottom: 20px;
            border-bottom: 1px dashed #e0e0e0;
            padding-bottom: 15px;
        }
        
        .help-section:last-child {
            border-bottom: none;
        }
        
        .help-section h4 {
            color: #002060;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .help-section h5 {
            color: #00499c;
            font-size: 1.05rem;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .help-section p {
            color: #555;
            line-height: 1.5;
            margin-bottom: 8px;
        }
        
        .example-tag {
            display: inline-block;
            background-color: rgba(0, 32, 96, 0.08);
            color: #002060;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            /* transition: all 0.2s ease; */ /* 移除标签过渡动画 */
        }
        
        .example-tag:hover {
            background-color: rgba(0, 32, 96, 0.15);
            transform: translateY(-1px);
        }
        
        .help-tip {
            display: flex;
            align-items: flex-start;
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 3px solid #ffc107;
            padding: 12px 15px;
            border-radius: 0 5px 5px 0;
        }
        
        .help-tip i {
            color: #ffc107;
            font-size: 1.2rem;
            margin-right: 10px;
            margin-top: 3px;
        }
        
        .help-tip p {
            margin: 0;
            color: #664d03;
            font-size: 0.95rem;
        }
        
        /* Modal styles */
        #infoModal .modal-content {
            border-radius: 8px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        #infoModal .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            border-radius: 8px 8px 0 0;
        }
        
        #infoModal .modal-title {
            color: #002060;
            font-weight: 600;
        }
        
        #infoModal .modal-body {
            padding: 1.5rem;
        }
        
        #infoModal .modal-body p {
            line-height: 1.6;
            color: #333;
        }
        
        #infoModal .modal-footer {
            border-top: 1px solid #eee;
            padding: 0.75rem 1rem;
        }
        
        #infoModal .btn-primary {
            background-color: #002060;
            border-color: #002060;
        }
        
        #infoModal .btn-primary:hover {
            background-color: #001040;
        }
    </style>
</head>
<body>
    <!--Navigation Bar-->
    <header>
        <!-- Logo and Title Area -->
        <div class="left-content">
            <!-- Logo Container -->
            <div class="logo-container">
                <img src="../../home/<USER>/logo.png" alt="MPRAM Logo" class="logo-image">
            </div>
            <!-- Title and Subtitle Container -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">MPRAM</h1>
                <p class="mb-0 small">Medicinal Plant Rhizosphere Associated Microbiome Database </p>
            </div>
        </div>
        <!-- Hamburger Button -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- Navigation Links Area -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="browse.php" class="nav-link active">Search</a></li>
                <li class="nav-item"><a href="Project.php" class="nav-link">Browse</a></li>
                <li class="nav-item"><a href="../microbes/Microbes.php" class="nav-link">Microbes</a></li>
                <li class="nav-item"><a href="../microbes/Network.php" class="nav-link">Network</a></li>
                <li class="nav-item"><a href="../../map/map.html" class="nav-link">Map</a></li>
                <li class="nav-item"><a href="../help/help.php" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

    <div class="container content-section">
        <h2 class="section-title">Advanced Search</h2>
        
        <!-- 两栏布局 -->
        <div class="two-column-layout">
            <!-- 左侧搜索表单 -->
            <div class="search-column">
                <div class="table-container" id="search-card">
                    <div class="search-form-title">Search Criteria</div>
                    <form id="search-form" method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="search-input" name="search_query" placeholder="Search: Herb name, microbes, properties..." value="<?php echo isset($_POST['search_query']) ? htmlspecialchars($_POST['search_query']) : ''; ?>">
                        </div>
                        <div class="mb-3">
                            <select class="form-select" id="search-by" name="search_by">
                                <option value="Study ID" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'Study ID') ? 'selected' : ''; ?>>Study ID</option>
                                <option value="Latin Name" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'Latin Name') ? 'selected' : ''; ?>>Latin Name</option>
                                <option value="Chinese Name" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'Chinese Name') ? 'selected' : ''; ?>>Chinese Name</option>
                                <option value="English Name" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'English Name') ? 'selected' : ''; ?>>English Name</option>
                                <option value="Studied microbes" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'Studied microbes') ? 'selected' : ''; ?>>Studied microbes</option>
                                <option value="Effect Category" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'Effect Category') ? 'selected' : ''; ?>>Effect Category</option>
                                <option value="Province" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'Province') ? 'selected' : ''; ?>>Province</option>
                                <option value="Country" <?php echo (isset($_POST['search_by']) && $_POST['search_by'] == 'Country') ? 'selected' : ''; ?>>Country</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="study-name-select" class="form-label">Latin name:</label>
                            <select class="form-select" id="study-name-select" name="study_name">
                                <option value="">Please select</option>
                                <?php foreach ($study_names as $name): ?>
                                    <option value="<?php echo htmlspecialchars($name); ?>" <?php echo (isset($_POST['study_name']) && $_POST['study_name'] == $name) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="soil-treatment-select" class="form-label">Soil Treatment:</label>
                            <select class="form-select" id="soil-treatment-select" name="soil_treatment">
                                <option value="">Please select</option>
                                <?php foreach ($soil_treatments as $treatment): ?>
                                    <option value="<?php echo htmlspecialchars($treatment); ?>" 
                                        <?php echo (isset($_POST['soil_treatment']) && $_POST['soil_treatment'] == $treatment) || 
                                                 (isset($_GET['soil_treatment']) && $_GET['soil_treatment'] == $treatment) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($treatment); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="cultivation-practice-select" class="form-label">Cultivation Practice:</label>
                            <select class="form-select" id="cultivation-practice-select" name="cultivation_practice">
                                <option value="">Please select</option>
                                <?php foreach ($cultivation_practices as $practice): ?>
                                    <option value="<?php echo htmlspecialchars($practice); ?>" 
                                        <?php echo (isset($_POST['cultivation_practice']) && $_POST['cultivation_practice'] == $practice) || 
                                                 (isset($_GET['cultivation_practice']) && $_GET['cultivation_practice'] == $practice) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($practice); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="effect-category-select" class="form-label">Therapeutic Class:</label>
                            <select class="form-select" id="effect-category-select" name="effect_category">
                                <option value="">Please select</option>
                                <?php foreach ($effect_categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category); ?>" <?php echo (isset($_POST['effect_category']) && $_POST['effect_category'] == $category) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="studied-microbes-select" class="form-label">Studied microbes:</label>
                            <select class="form-select" id="studied-microbes-select" name="studied_microbes">
                                <option value="">Please select</option>
                                <?php foreach ($studied_microbes_list as $microbe): ?>
                                    <option value="<?php echo htmlspecialchars($microbe); ?>" <?php echo (isset($_POST['studied_microbes']) && $_POST['studied_microbes'] == $microbe) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($microbe); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="source-select" class="form-label">Pharmacopoeia:</label>
                            <select class="form-select" id="source-select" name="source">
                                <option value="">Please select</option>
                                <?php foreach ($sources as $source): ?>
                                    <option value="<?php echo htmlspecialchars($source); ?>" <?php echo (isset($_POST['source']) && $_POST['source'] == $source) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($source); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="province-select" class="form-label">Province:</label>
                            <select class="form-select" id="province-select" name="province">
                                <option value="">Please select</option>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?php echo htmlspecialchars($province); ?>" <?php echo (isset($_POST['province']) && $_POST['province'] == $province) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($province); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="country-select" class="form-label">Country:</label>
                            <select class="form-select" id="country-select" name="country">
                                <option value="">Please select</option>
                                <?php foreach ($countries as $country): ?>
                                    <option value="<?php echo htmlspecialchars($country); ?>" <?php echo (isset($_POST['country']) && $_POST['country'] == $country) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($country); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-buttons">
                            <button type="reset" class="btn btn-outline-secondary btn-icon" id="reset-button">
                                <i class="fa-solid fa-rotate-left"></i> Reset
                            </button>
                            <button type="submit" class="btn btn-primary btn-icon" id="search-button">
                                <i class="fa-solid fa-search"></i> Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 右侧结果表格 -->
            <div class="results-column">
                <!-- Loading Animation -->
                <div class="loading" id="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Searching data, please wait...</p>
                </div>
                
                <!-- Error Message -->
                <?php if (!empty($error_message)): ?>
                <div class="error-message">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
                <?php endif; ?>
                
                <!-- Search Results Table -->
                <?php if ($has_search && $result): ?>
                <div class="results-card">
                    <div class="results-title">
                        <div>Search Results</div>
                        <span class="badge bg-primary"><?php echo $result->num_rows; ?> records</span>
                    </div>
                    
                    <div class="table-container">
                        <div id="toolbar"></div>
                        <table id="results-table" class="table table-striped table-hover"></table>
                    </div>
                </div>
                <?php else: ?>
                <div class="results-card help-card">
                    <div class="results-title">Help Information</div>
                    <div class="help-content">
                        <div class="help-section">
                            <h4>How to Use the Search Function</h4>
                            <p>Use the search form on the left to find information about medicinal plants and their rhizosphere microbiomes. You can filter using one or more fields.</p>
                        </div>

                        <div class="help-section">
                            <h5>Latin name:</h5>
                            <p>Scientific name of medicinal plants. Example: 
                                <span class="example-tag" data-field="study_name" data-value="Panax ginseng">Panax ginseng</span>
                            </p>
                        </div>

                        <div class="help-section">
                            <h5>Soil Treatment:</h5>
                            <p>Different soil treatment methods. Example: 
                                <span class="example-tag" data-field="soil_treatment" data-value="Biochar Amendment">Biochar Amendment</span>
                            </p>
                        </div>

                        <div class="help-section">
                            <h5>Cultivation Practice:</h5>
                            <p>Different cultivation methods for medicinal plants. Example: 
                                <span class="example-tag" data-field="cultivation_practice" data-value="Field">Field</span>
                            </p>
                        </div>

                        <div class="help-section">
                            <h5>Therapeutic Class:</h5>
                            <p>Therapeutic classification of medicinal plants. Example: 
                                <span class="example-tag" data-field="effect_category" data-value="Antipyretic Detoxicate Drugs">Antipyretic Detoxicate Drugs</span>
                            </p>
                        </div>

                        <div class="help-section">
                            <h5>Studied microbes:</h5>
                            <p>Types of microbes studied. Example: 
                                <span class="example-tag" data-field="studied_microbes" data-value="Bacteria">Bacteria</span>
                            </p>
                        </div>

                        <div class="help-section">
                            <h5>Pharmacopoeia:</h5>
                            <p>Pharmacopoeia source. Example: 
                                <span class="example-tag" data-field="source" data-value="ChP 2020">ChP 2020</span>
                            </p>
                        </div>

                        <div class="help-section">
                            <h5>Province:</h5>
                            <p>Province where samples were collected. Example: 
                                <span class="example-tag" data-field="province" data-value="Yunnan">Yunnan</span>
                            </p>
                        </div>

                        <div class="help-section">
                            <h5>Country:</h5>
                            <p>Country where samples were collected. Example: 
                                <span class="example-tag" data-field="country" data-value="China">China</span>
                            </p>
                        </div>
                        
                        <div class="help-tip">
                            <i class="fas fa-lightbulb"></i>
                            <p>Tip: Click on any example value to quickly search for related records.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <footer class="container-fluid py-3 text-center">
        <p class="small">Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
    </footer>

    <script src="../../public/js/jquery-3.7.1.min.js"></script>
    <script src="../../public/js/bootstrap.bundle.min.js"></script>
    <script src="../../public/js/bootstrap-table.min.js"></script>
    <script>
        // Debug flag
        const DEBUG = true;
        
        function log(message, data) {
            if (DEBUG) {
                console.log(message, data);
            }
        }

        function toggleNav() {
            const nav = document.querySelector('nav');
            const hamburger = document.querySelector('.hamburger');
            nav.classList.toggle('show');
            hamburger.classList.toggle('active');
        }

        // Show loading animation when search form is submitted - 已禁用
        document.getElementById('search-form').addEventListener('submit', function() {
            // document.getElementById('loading').style.display = 'block'; // 移除加载动画显示
        });
        
        // Reset button click event - clear all selections and search results
        document.getElementById('reset-button').addEventListener('click', function(e) {
            e.preventDefault(); // 阻止默认行为
            
            // 清空所有表单字段
            const selects = document.querySelectorAll('select');
            selects.forEach(select => select.value = '');
            document.getElementById('search-input').value = '';
            
            // 如果存在结果表格，刷新页面
            if (document.getElementById('results-table')) {
                window.location.href = 'browse.php';
            }
        });
        
        // Format Study Name to be displayed in italic
        function studyNameFormatter(value, row) {
            if (!value) return '';
            return '<span class="latin-name">' + value + '</span>';
        }
        
        // Format Study ID to be displayed as a link
        function studyIdFormatter(value, row) {
            if (!value) return '';
            return '<a href="Study.php?study_id=' + value + '" class="study-id-link">' + value + '</a>';
        }

        // Custom sorter for Study ID column
        function studyIdSorter(a, b) {
            // Extract numbers from MPRAMID01-X format
            const numA = parseInt(a.replace('MPRAMID01-', ''));
            const numB = parseInt(b.replace('MPRAMID01-', ''));
            return numA - numB;
        }
        
        // Initialize Bootstrap Table if search has been performed
        <?php if ($has_search && $result && $result->num_rows > 0): ?>
        $(document).ready(function() {
            // Prepare data for the table
            var tableData = [];
            var tableColumns = [];
            
            <?php
            // Reset result pointer
            $result->data_seek(0);
            
            // Get first row to determine columns
            if ($row = $result->fetch_assoc()) {
                echo "// Define columns\n";
                foreach ($row as $column => $value) {
                    if ($column == 'Study ID') {
                        echo "tableColumns.push({ 
                            field: '$column', 
                            title: '$column', 
                            formatter: studyIdFormatter, 
                            sortable: true, 
                            sorter: studyIdSorter,
                            align: 'center', 
                            valign: 'middle' 
                        });\n";
                    } else if ($column == 'Study Name') {
                        echo "tableColumns.push({ field: '$column', title: '$column', formatter: studyNameFormatter, sortable: true, align: 'center', valign: 'middle' });\n";
                    } else {
                        echo "tableColumns.push({ field: '$column', title: '$column', sortable: true, align: 'center', valign: 'middle' });\n";
                    }
                }
                
                // Add first row to data
                echo "// Add first row\n";
                echo "var firstRow = {};\n";
                foreach ($row as $column => $value) {
                    $value = addslashes($value);
                    echo "firstRow['$column'] = '$value';\n";
                }
                echo "tableData.push(firstRow);\n";
                
                // Add remaining rows
                echo "// Add remaining rows\n";
                while ($row = $result->fetch_assoc()) {
                    echo "var nextRow = {};\n";
                    foreach ($row as $column => $value) {
                        $value = addslashes($value);
                        echo "nextRow['$column'] = '$value';\n";
                    }
                    echo "tableData.push(nextRow);\n";
                }
            }
            ?>
            
            // Initialize the Bootstrap Table
            $('#results-table').bootstrapTable({
                columns: tableColumns,
                data: tableData,
                pagination: true,
                striped: true,
                search: false,
                showColumns: true,
                showToggle: true,
                showFullscreen: true,
                showRefresh: true,
                pageSize: 20,
                pageList: [10, 20, 50, 100],
                locale: 'en-US',
                toolbar: '#toolbar',
                toolbarAlign: 'right',
                sortName: 'Study ID',  // 设置默认排序列
                sortOrder: 'asc',      // 设置默认排序方向
                icons: {
                    fullscreen: 'fa-solid fa-expand',
                    columns: 'fa-solid fa-columns',
                    refresh: 'fa-solid fa-rotate',
                    toggleOff: 'fa-solid fa-table-list',
                    toggleOn: 'fa-solid fa-table-cells'
                },
                buttonsClass: 'btn btn-outline-secondary',
                iconsPrefix: 'fa',
                onSort: function(name, order) {
                    // 当排序发生变化时，重新应用自定义排序
                    if (name === 'Study ID') {
                        this.options.data.sort(function(a, b) {
                            const numA = parseInt(a['Study ID'].replace('MPRAMID01-', ''));
                            const numB = parseInt(b['Study ID'].replace('MPRAMID01-', ''));
                            return order === 'asc' ? numA - numB : numB - numA;
                        });
                        this.load(this.options.data);
                    }
                }
            });

            // 初始化时手动触发一次排序
            $('#results-table').bootstrapTable('sort', {
                field: 'Study ID',
                order: 'asc'
            });
        });
        <?php endif; ?>

        // 初始化 Bootstrap Table
        $(document).ready(function() {
            // 字段映射
            const fieldMapping = {
                'study_name': 'Study Name',
                'soil_treatment': 'Soil Treatment Groups',
                'cultivation_practice': 'Cultivation Practice',
                'effect_category': 'Effect Category',
                'studied_microbes': 'Studied microbes',
                'source': 'Source',
                'province': 'Province',
                'country': 'Country'
            };

            // 存储所有下拉菜单的原始选项
            const originalOptions = {};

            // 初始化原始选项
            function initializeOriginalOptions() {
                Object.keys(fieldMapping).forEach(fieldId => {
                    const select = $(`#${fieldId}-select`);
                    originalOptions[fieldId] = [];
                    
                    // 获取并清理选项文本
                    select.find('option').each(function() {
                        const value = $(this).val();
                        const text = $(this).text();
                        if (value) { // 只存储非空值
                            originalOptions[fieldId].push({
                                value: value,
                                text: text
                            });
                        }
                    });
                    
                    log(`Initialized options for ${fieldId}:`, originalOptions[fieldId]);
                });
            }

            // 恢复下拉菜单的原始选项
            function restoreOriginalOptions(fieldId) {
                const select = $(`#${fieldId}-select`);
                select.empty().append('<option value="">Please select</option>');
                
                if (originalOptions[fieldId] && originalOptions[fieldId].length > 0) {
                    originalOptions[fieldId].forEach(option => {
                        if (option.value && option.value.trim()) {
                            select.append(`<option value="${option.value}">${option.text}</option>`);
                        }
                    });
                } else {
                    // 如果没有原始选项，尝试从当前选项中获取
                    const currentOptions = [];
                    select.find('option').each(function() {
                        const value = $(this).val();
                        if (value) {
                            currentOptions.push({
                                value: value,
                                text: $(this).text()
                            });
                        }
                    });
                    originalOptions[fieldId] = currentOptions;
                }
                
                log(`Restored original options for ${fieldId}`);
            }

            // 初始化原始选项
            initializeOriginalOptions();

            // 绑定下拉菜单change事件
            Object.keys(fieldMapping).forEach(fieldId => {
                $(`#${fieldId}-select`).on('change', function() {
                    const selectedValue = $(this).val();
                    log(`${fieldId} changed to:`, selectedValue);
                    updateDropdownOptions(fieldId);
                });
            });

            // 重置按钮事件
            $('#reset-button').on('click', function() {
                log('Reset button clicked');
                Object.keys(fieldMapping).forEach(fieldId => {
                    restoreOriginalOptions(fieldId);
                    $(`#${fieldId}-select`).val('');
                });
            });

            // 添加帮助示例标签的点击事件
            $(document).on('click', '.example-tag', function() {
                const field = $(this).data('field');
                const value = $(this).data('value');
                
                if (field && value) {
                    // 清空所有字段
                    Object.keys(fieldMapping).forEach(fieldId => {
                        $(`#${fieldId}-select`).val('');
                    });
                    
                    // 设置选中的值
                    $(`#${field}-select`).val(value);
                    
                    // 触发change事件更新其他下拉菜单
                    $(`#${field}-select`).trigger('change');
                    
                    // 添加高亮效果
                    $(`#${field}-select`).addClass('highlight-select');
                    setTimeout(() => {
                        $(`#${field}-select`).removeClass('highlight-select');
                    }, 1500);
                    
                    // 显示加载指示器 - 已禁用
                    // $('#loading').css({
                    //     'display': 'block',
                    //     'opacity': '0.9'
                    // });
                    
                    // 添加精确匹配隐藏字段
                    if (!$('#exact-match-input').length) {
                        $('#search-form').append('<input type="hidden" id="exact-match-input" name="exact_match" value="1">');
                    }
                    
                    // 自动提交表单
                    $('#search-form').submit();
                    
                    // 显示相关信息弹窗
                    const relatedInfo = {
                        'study_name': {
                            title: 'Panax ginseng',
                            info: 'Latin name: Panax ginseng<br>Chinese name: 人参<br>English name: Ginseng<br>Related microbes: Bacteria, Fungi<br>Effects: Enhances immune function, antioxidant'
                        },
                        'soil_treatment': {
                            title: 'Biochar Amendment',
                            info: 'Purpose: Improve soil structure and fertility<br>Application rate: 10-20 t/ha<br>Benefits: Carbon sequestration, improved water retention'
                        },
                        'cultivation_practice': {
                            title: 'Field Cultivation',
                            info: 'Method: Traditional field cultivation<br>Duration: 4-6 years<br>Conditions: Partial shade, well-drained soil'
                        },
                        'effect_category': {
                            title: 'Antipyretic Detoxicate Drugs',
                            info: 'Therapeutic Class: Antipyretic and Detoxicating<br>Function: Reducing fever and removing toxins<br>Related herbs: Lonicera japonica, Forsythia suspensa<br>Common formulations: Decoction, powder<br>TCM theory: Clears heat and relieves toxicity'
                        },
                        'studied_microbes': {
                            title: 'Bacteria',
                            info: 'Common genera: Bacillus, Pseudomonas, Streptomyces<br>Functions: Nitrogen fixation, phosphate solubilization<br>Interactions: Plant growth promotion, pathogen suppression'
                        },
                        'source': {
                            title: 'Chinese Pharmacopoeia 2020',
                            info: 'Publication: Chinese Pharmacopoeia 2020 Edition<br>Authority: Chinese Pharmacopoeia Commission<br>Standards: Quality standards for herbs and herbal medicine<br>Includes: Over 5,500 medicinal substances'
                        },
                        'province': {
                            title: 'Yunnan',
                            info: 'Location: Southwest China<br>Climate: Subtropical highland climate<br>Biodiversity: One of the most biodiverse regions in China<br>Notable herbs: Dendrobium, Gastrodia elata'
                        },
                        'country': {
                            title: 'China',
                            info: 'Region: East Asia<br>Traditional medicine system: Traditional Chinese Medicine (TCM)<br>Herbal medicine history: Over 2000 years<br>Pharmacopoeia: Chinese Pharmacopoeia'
                        }
                    };
                    
                    if (relatedInfo[field]) {
                        setTimeout(() => {
                            // Create modal if it doesn't exist
                            if (!$('#infoModal').length) {
                                $('body').append(`
                                    <div class="modal fade" id="infoModal" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title"></h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body"></div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `);
                            }
                            
                            // Update modal content
                            $('#infoModal .modal-title').html(relatedInfo[field].title);
                            $('#infoModal .modal-body').html(`<p>${relatedInfo[field].info}</p>`);
                            
                            // Show modal
                            const modal = new bootstrap.Modal(document.getElementById('infoModal'));
                            modal.show();
                        }, 500);
                    }
                }
            });
            
            // 调整初始布局
            setTimeout(adjustColumnHeights, 300);
            
            // 监听窗口大小变化，调整布局
            $(window).on('resize', debounce(adjustColumnHeights, 300));

            // 初始化 Bootstrap Table（如果存在）
            if ($('#results-table').length > 0 && typeof tableColumns !== 'undefined' && typeof tableData !== 'undefined') {
                $('#results-table').bootstrapTable({
                    columns: tableColumns,
                    data: tableData,
                    pagination: true,
                    search: true,
                    sortable: true,
                    showColumns: true,
                    showToggle: true,
                    showFullscreen: true,
                    showRefresh: true,
                    pageSize: 25,
                    pageList: [10, 25, 50, 100],
                    locale: 'en-US',
                    toolbar: '#toolbar',
                    toolbarAlign: 'right',
                    icons: {
                        fullscreen: 'fa-solid fa-expand',
                        columns: 'fa-solid fa-columns',
                        refresh: 'fa-solid fa-rotate',
                        toggleOff: 'fa-solid fa-table-list',
                        toggleOn: 'fa-solid fa-table-cells',
                        search: 'fa-solid fa-search'
                    },
                    onPostBody: function() {
                        // 表格内容加载后调整高度
                        adjustColumnHeights();
                    }
                });
            }
        });

        // 调整两栏高度保持对齐
        function adjustColumnHeights() {
            const searchColumn = document.querySelector('.search-column');
            const resultsColumn = document.querySelector('.results-column');
            const resultsCard = document.querySelector('.results-card');
            
            if (window.innerWidth > 992 && searchColumn && resultsColumn) {
                // 重置高度
                searchColumn.style.height = 'auto';
                
                // 获取结果列和搜索列的高度
                const resultsHeight = resultsColumn.offsetHeight;
                const searchHeight = searchColumn.offsetHeight;
                
                // 如果结果列高于搜索列，则调整搜索列高度
                if (resultsHeight > searchHeight) {
                    searchColumn.style.height = resultsHeight + 'px';
                } else if (resultsCard && searchHeight > resultsHeight) {
                    // 如果搜索列高于结果列，则调整结果卡片高度
                    resultsCard.style.minHeight = (searchHeight - 20) + 'px';
                }
            } else {
                // 移动端重置高度
                if (searchColumn) searchColumn.style.height = 'auto';
                if (resultsCard) resultsCard.style.minHeight = 'auto';
            }
        }
        
        // 防抖函数 - 避免频繁调整布局
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait);
            };
        }
        
        // 高亮选择字段的样式
        let styleSheet = document.createElement("style");
        styleSheet.innerText = `
            .highlight-select {
                box-shadow: 0 0 0 0.25rem rgba(0, 32, 96, 0.35);
                border-color: #002060;
                /* animation: pulse 1.5s; */ /* 移除脉冲动画 */
            }
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(0, 32, 96, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(0, 32, 96, 0); }
                100% { box-shadow: 0 0 0 0 rgba(0, 32, 96, 0); }
            }
            
            /* 优化为小屏幕下拉菜单样式 */
            .form-select {
                font-size: 0.9rem;
                padding: 0.375rem 0.5rem;
            }
            
            .form-label {
                font-size: 0.9rem;
                margin-bottom: 0.2rem;
            }
            
            .search-form-title {
                font-size: 1.1rem;
            }
            
            #search-card {
                padding: 15px;
            }
            
            /* 让表单占用更少空间 */
            .mb-3 {
                margin-bottom: 0.6rem !important;
            }
        `;
        document.head.appendChild(styleSheet);
        
        // 更新下拉菜单选项
        function updateDropdownOptions(changedFieldId) {
            const selectedValues = {};
            const selectedFields = [];
            const selectedValuesArray = [];
            
            // 获取所有已选择的值
            Object.keys(fieldMapping).forEach(fieldId => {
                const value = $(`#${fieldId}-select`).val();
                if (value) {
                    selectedFields.push(fieldMapping[fieldId]);
                    selectedValuesArray.push(value);
                    selectedValues[fieldMapping[fieldId]] = value;
                }
            });

            log('Selected values:', selectedValues);

            // 如果没有选择任何值，恢复所有下拉菜单的原始选项
            if (Object.keys(selectedValues).length === 0) {
                log('No selections, restoring all dropdowns');
                Object.keys(fieldMapping).forEach(fieldId => {
                    if (fieldId !== changedFieldId) {
                        restoreOriginalOptions(fieldId);
                    }
                });
                return;
            }

            // 更新其他下拉菜单
            Object.keys(fieldMapping).forEach(fieldId => {
                if (fieldId !== changedFieldId) {
                    const select = $(`#${fieldId}-select`);
                    const currentValue = select.val();

                    log(`Updating ${fieldId} dropdown`, {
                        currentValue: currentValue,
                        selectedFields: selectedFields,
                        selectedValues: selectedValuesArray
                    });

                    // 显示加载状态
                    select.prop('disabled', true);

                    // 获取相关选项
                    $.ajax({
                        url: 'get_related_options.php',
                        method: 'POST',
                        data: {
                            selected_field: selectedFields.join(','),
                            selected_value: selectedValuesArray.join(','),
                            target_field: fieldMapping[fieldId]
                        },
                        success: function(response) {
                            log(`Response for ${fieldId}:`, response);
                            
                            try {
                                if (response.success && Array.isArray(response.options)) {
                                    // 更新选项
                                    select.empty().append('<option value="">Please select</option>');
                                    
                                    // 移除重复选项并排序
                                    const uniqueOptions = [...new Set(response.options)].sort();
                                    
                                    // 修改Source字段显示名称为Pharmacopoeia
                                    const isSourceField = fieldId === 'source';
                                    const fieldDisplay = isSourceField ? 'Pharmacopoeia' : fieldMapping[fieldId];
                                    
                                    uniqueOptions.forEach(option => {
                                        if (option && option.trim()) {
                                            const selected = option === currentValue ? 'selected' : '';
                                            select.append(`<option value="${option}" ${selected}>${option}</option>`);
                                        }
                                    });

                                    // 如果当前值不在新选项中，清除选择
                                    if (currentValue && !uniqueOptions.includes(currentValue)) {
                                        select.val('');
                                    }

                                    log(`Updated ${fieldId} with ${uniqueOptions.length} options`);
                                    
                                    // 保存新的选项到originalOptions
                                    originalOptions[fieldId] = uniqueOptions.map(value => ({
                                        value: value,
                                        text: value
                                    }));
                                } else {
                                    throw new Error(response.error || 'Invalid response format');
                                }
                            } catch (error) {
                                console.error(`Error processing response for ${fieldId}:`, error);
                                console.error('Response:', response);
                                restoreOriginalOptions(fieldId);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error(`AJAX error for ${fieldId}:`, {
                                status: status,
                                error: error,
                                response: xhr.responseText
                            });
                            restoreOriginalOptions(fieldId);
                        },
                        complete: function() {
                            select.prop('disabled', false);
                        }
                    });
                }
            });
        }
    </script>
</body>
</html>