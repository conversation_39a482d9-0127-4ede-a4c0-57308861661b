<?php
// 设置响应头为JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// 启用错误报告，方便调试
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 数据库连接配置
$host = 'localhost';
$dbname = 'HRMA';
$username = 'root';
$password = 'bidd@2012';

try {
    // 创建PDO连接
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 准备查询语句 - 获取功效数据
    $effectQuery = "SELECT h.`Study ID` as study_id,
                          COALESCE(pd.`Study Name`, '') as latin_name,
                          COALESCE(pd.`Chinese Name`, '') as chinese_name,
                          COALESCE(h.`Effect Category`, '其他') as effect_category
                   FROM herb h
                   LEFT JOIN `project details` pd ON h.`Study ID` = pd.`Study ID`
                   WHERE h.`Study ID` IS NOT NULL";
    $effectStmt = $pdo->prepare($effectQuery);
    $effectStmt->execute();
    $effectData = $effectStmt->fetchAll(PDO::FETCH_ASSOC);

    // 准备查询语句 - 获取土壤处理组数据
    $treatmentQuery = "SELECT 
                          `Study ID` as study_id,
                          `Latin Name` as latin_name,
                          `Chinese Name` as chinese_name,
                          `Soil Treatment Groups` as soil_treatment_groups
                       FROM browse
                       WHERE `Study ID` IS NOT NULL";
    $treatmentStmt = $pdo->prepare($treatmentQuery);
    $treatmentStmt->execute();
    $treatmentData = $treatmentStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 准备查询语句 - 获取地理位置数据
    $geoQuery = "SELECT 
                    COALESCE(`Study ID`, '') as study_id,
                    COALESCE(`Sample ID`, '') as sample_id,
                    COALESCE(`Sample Name`, '') as sample_name,
                    COALESCE(`Collection Sites`, '') as collection_sites,
                    COALESCE(`Province`, '') as province,
                    COALESCE(`Country`, '') as country,
                    COALESCE(`Latitude`, '') as latitude,
                    COALESCE(`Longitude`, '') as longitude
                 FROM geo
                 WHERE `Study ID` IS NOT NULL";
    $geoStmt = $pdo->prepare($geoQuery);
    $geoStmt->execute();
    $geoData = $geoStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 数据验证
    $validEffectData = [];
    foreach ($effectData as $row) {
        // 过滤掉无效数据
        if (!empty($row['study_id'])) {
            $validEffectData[] = $row;
        }
    }
    
    $validGeoData = [];
    foreach ($geoData as $row) {
        // 过滤掉无效数据
        if (!empty($row['study_id'])) {
            $validGeoData[] = $row;
        }
    }

    $validTreatmentData = [];
    foreach ($treatmentData as $row) {
        // 过滤掉无效数据
        if (!empty($row['study_id'])) {
            $validTreatmentData[] = $row;
        }
    }
    
    // 组织返回数据
    $response = [
        'effect' => $validEffectData,
        'geo' => $validGeoData,
        'treatments' => $validTreatmentData,
        'meta' => [
            'effect_count' => count($validEffectData),
            'geo_count' => count($validGeoData),
            'treatment_count' => count($validTreatmentData),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    // 返回JSON数据
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch(PDOException $e) {
    // 记录错误信息
    error_log("Database Error: " . $e->getMessage());
    error_log("Error Code: " . $e->getCode());
    error_log("Error File: " . $e->getFile());
    error_log("Error Line: " . $e->getLine());
    error_log("Error Trace: " . $e->getTraceAsString());
    
    // 返回错误信息
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'code' => $e->getCode(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>