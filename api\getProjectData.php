<?php
// 允许跨域请求的头部信息
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // 引入数据库连接
    include __DIR__ . '/../includes/config/db_connection.php';

    // 设置字符集为 utf8mb4
    $conn->set_charset("utf8mb4");

    // 接收搜索参数
    $search_by = isset($_GET['search_by']) ? $_GET['search_by'] : '';
    $search_query = isset($_GET['search_query']) ? trim($_GET['search_query']) : '';

    // 构建基础查询
    $sql = "SELECT * FROM browse";

    // 如果有搜索参数，添加搜索条件
    if (!empty($search_query) && !empty($search_by)) {
        $sql .= " WHERE `" . $conn->real_escape_string($search_by) . "` LIKE ?";
        $stmt = $conn->prepare($sql);
        $search_term = "%$search_query%";
        $stmt->bind_param("s", $search_term);
    } else {
        $stmt = $conn->prepare($sql);
    }

    // 执行查询
    $stmt->execute();
    $result = $stmt->get_result();

    // 将结果转换为数组
    $data = array();
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    // 设置响应头为JSON
    header('Content-Type: application/json');

    // 返回JSON数据
    echo json_encode($data, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 设置HTTP状态码为500
    http_response_code(500);
    // 返回错误信息
    echo json_encode(array(
        'error' => $e->getMessage(),
        'status' => 500
    ), JSON_UNESCAPED_UNICODE);
} finally {
    // 关闭数据库连接
    if (isset($conn)) {
        $stmt->close();
        $conn->close();
    }
}
?>
