<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample</title>
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/style.css">
    <link rel="stylesheet" href="../../public/css/bootstrap-table.min.css">
</head>
<body>
    <!--导航栏-->
    <nav class="navbar navbar-expand-lg bg-dark bg-gradient navbar-dark fixed-top">
        <div class="container">
            <a href="../../public/index.php" class="navbar-brand">
                <img src="../../public/img/logo.png" alt="1" width="30" height="24">
                Soil Multimodal Database
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navmenu">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navmenu">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="../../public/index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="../browse/Project.php">Project</a></li>
                    <li class="nav-item"><a class="nav-link active" href="Sample.php">Sample</a></li>
                    <li class="nav-item"><a class="nav-link" href="#">about</a></li>
                    <li class="nav-item"><a class="nav-link" href="#">help</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 使用 Bootstrap grid 系统来居中显示和自适应大小 -->
    <div class="container mt-5 text-center">
        <div class="row justify-content-center">
                <table id="table"></table>
        </div>
    </div>

    <script src="../../public/js/jquery-3.7.1.min.js"></script>
    <script src="../../public/js/bootstrap.bundle.min.js"></script>
    <script src="../../public/js/bootstrap-table.min.js"></script>

    <script>
        var $table;

        function InitMainTable() {
            $table = $('#table').bootstrapTable({
                url: "../../api/getSampleData.php",  // 修改为你实际的数据获取 URL
                method: 'GET',
                pagination: true,
                striped: true,
                search: true,
                showColumns: true,
                pageSize: 10,
                pageList: [10, 25, 50, 100],
                columns: [
                    { field: 'Sample ID', title: 'Sample ID',formatter: sampleIdFormatter, sortable: true },
                    { field: 'Sample Name', title: 'Sample Name', sortable: true },
                    { field: 'Study Name', title: 'Study Name', sortable: true },
                    { field: 'Collection Sites', title: 'Collection Sites', sortable: true },
                    { field: 'Province', title: 'Province', sortable: true },
                    { field: 'Country', title: 'Country', sortable: true },
                    { field: 'Latitude', title: 'Latitude', sortable: true, visible:false },
                    { field: 'Longitude', title: 'Longitude', sortable: true, visible:false },
                    { field: 'Elevation', title: 'Elevation', sortable: true, visible:false },
                    { field: 'Organism', title: 'Organism', sortable: true }
                ]
            });
        }
        

        function sampleIdFormatter(value, row) {
            return '<a href="sampledetails.php?sample_id=' + value + '">' + value + '</a>';
        }

        $(function () {
            InitMainTable();
        });
    </script>
</body>
</html>
