{"study_names": ["Achy<PERSON><PERSON> bidentata", "Aconitum car<PERSON>", "<PERSON><PERSON><PERSON> altissima", "<PERSON>", "<PERSON> sinensis", "Areca catechu", "<PERSON><PERSON> a<PERSON>i", "As<PERSON> tataricus", "Atractylo<PERSON> lancea", "Atractylo<PERSON> macrocephala", "Bletilla striata", "Bupleurum chinense", "Capsicum annuum", "<PERSON><PERSON><PERSON> tinctorius", "<PERSON>losia argentea", "Chrysanthemum × morifolium", "Chrysanthemum indicum", "Cirsium arvense", "Citrus reticulata", "Codonopsis pilosula", "Commelina communis", "Coptis deltoidea", "<PERSON><PERSON><PERSON> sativus", "<PERSON><PERSON>ia odorifera", "Dendrobium nobile", "<PERSON><PERSON><PERSON>", "Frax<PERSON> chinensis subsp. rhynchophylla", "Fr<PERSON><PERSON>ia cirrhosa", "<PERSON><PERSON><PERSON><PERSON> usuriensis", "Ganoderma lucidum", "<PERSON><PERSON><PERSON> elata", "Glycine max", "Glycyrr<PERSON><PERSON> uralensis", "Hippopha<PERSON> rhamnoides", "Hordeum vulgare", "Illicium verum", "<PERSON><PERSON><PERSON><PERSON> balsamina", "Imperata cylindrica", "Juglans regia", "<PERSON><PERSON> effusus", "<PERSON><PERSON> brownii var. viridulum", "Linum usitatissimum", "<PERSON><PERSON><PERSON> chinensis", "Lonicera japonica", "Lycium barbarum", "Ophiopogon japonicus", "<PERSON><PERSON><PERSON> lactiflora", "Panax ginseng", "Panax notog<PERSON>eng", "Pan<PERSON> quinquefolius", "Paris polyphylla var. chinensis", "<PERSON><PERSON> frutescens", "<PERSON>llia ternata", "<PERSON><PERSON>na", "Pogostemon cablin", "Polygonatum kingianum", "Portulaca oleracea", "Psammosilene tunicoides", "P<PERSON><PERSON><PERSON><PERSON>ia heterophylla", "Pulsatilla chinensis", "Punica granatum", "<PERSON><PERSON><PERSON> glutinosa", "R<PERSON><PERSON> communis", "<PERSON> rugosa", "<PERSON><PERSON> miltiorrhiza", "<PERSON><PERSON><PERSON> sphenanthera", "<PERSON><PERSON><PERSON><PERSON> baicalensis", "So<PERSON>ora flavescens", "So<PERSON><PERSON>ensis", "<PERSON><PERSON> orientalis", "Wurfbainia villosa", "Zanthoxylum bungeanum", "Zingiber officinale", "Ziziphus jujuba"], "soil_treatments": ["Biochar Amendment", "Cultivation Practice", "Cultivation Site", "Disease and Health​", "Disease and Health​；Microbial Inoculant Inoculation", "Fertilizer Application", "Growth Stage", "Inorganic Compound Supplementation", "Microbial Inoculant Inoculation", "Nanoparticle Incorporation", "Organic Acid Amendment", "Pedological Classification", "Plant Age", "Plant Cultivar", "Rhizosphere Isolation", "Sampling Depth Stratification", "Shade tolerance; Soil biota (live/sterile)", "Soil Contamination Level", "Soil Fumigation", "Soil Moisture Content"], "cultivation_practices": ["Epiphytic", "Field", "Greenhouse", "​Orchard", "Pot", "Wetland", "Wild"], "effect_categories": ["Antipyretic Detoxicate Drugs", "Antitussive Antiasthmetics", "Astringent Hemostatic Medicinal", "Astringent Medicinal", "Blood Activating Stasis Removing Drugs", "Blood-Cooling Hemostatic Medicinal", "Blood-Tonifying Medicinal", "Channel-Warming Hemostatic", "Dampness Removing Drugs", "Digestants", "Diuretic Dampness Excreting Drugs", "External Medicinal (Draw Out Toxin, Resolve Putridity)", "Fire Purging Drugs", "Heat Clearing Blood Cooling Drugs", "Heat-Clearing And Dampnessdrying Medicinal", "Heat-Clearing Medicinal", "Laxatives", "Liver-Pacifying Wind-Extinguishing Medicinal", "Phlegresolving Medicine", "Pungent Cool Diaphoretics", "Pungent-Warm Exterior-Releasing Medicinal", "Qi Regulating Drugs", "Qi Reinforcing Drugs", "Stasis-Resolving Hemostatic Medicinal", "Tonifying And Replenishing Medicinal", "Tranguilizing Medicinal", "Warming Interior Drugs", "Wind-dampnessdispelling and colddispersing medicinal", "Wind-Dampnessdispelling Medicinal", "Yang Reinforcing Drugs", "Yin-Tonifying Medicinal"], "sources": ["ChP 2020", "ChP 2020; Ph. Eur. 10.0"], "provinces": ["<PERSON><PERSON>", "Anhui; Liaoning; HeiBei", "Beijing", "California", "Chongqing", "Florida", "Fujian", "Gansu", "Gansu; Sichuan; Qinghai", "Guangdong", "Guangxi", "Guangzhou", "Guizhou", "Guizhou Province", "Gyeongsangbuk-do", "Hainan", "Hebei", "Heilongjiang", "<PERSON><PERSON>", "<PERSON><PERSON> ", "Hubei", "Hubei;Hunan", "Hunan", "Jeollabuk-do", "Jiangsu", "Jiangxi", "<PERSON><PERSON>", "Jilin; Liaoning", "Karnataka", "LA", "Liaoning; Qinghai; Tibet; Gansu; Shanxi; Sichuan", "Madrid", "Murcia", "n.a.", "Nanjing", "NingXia", "North Dakota; Montana; Washington", "Qinghai", "Shaanx", "Shandong", "Shandong ", "Shanghai", "Shanxi", "Shanxi; Yunnan; Liaoning", "Sichuan", "Taiwan", "Tianjin", "Virginia", "Xinjiang", "Yeoncheon; Paju", "Yunnan", "Yunnan Province", "Yunnan; Haikou city; Fujian; Guizhou Province", "Zhejiang"], "countries": ["China", "India", "Korea", "South Korea", "Spain", "USA"], "studied_microbes_list": ["Bacteria", "Bacteria; Fungi", "Fungi"]}