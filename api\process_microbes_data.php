<?php
require_once(__DIR__ . '/../includes/config/db_connection.php');
$conn = require(__DIR__ . '/../includes/config/db_connection.php');

// 设置响应头为JSON
header('Content-Type: application/json');

// 获取数据
$sql = "SELECT * FROM microbes";
$result = $conn->query($sql);

$data = array();
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
}

// 返回JSON数据
echo json_encode($data);

$conn->close();
?> 