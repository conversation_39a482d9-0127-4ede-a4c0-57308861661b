<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/php_error.log');

// 添加调试信息
echo "<!-- PHP is running -->\n";
echo "<!-- Current PHP version: " . phpversion() . " -->\n";
echo "<!-- Current working directory: " . getcwd() . " -->\n";
echo "<!-- Script path: " . __FILE__ . " -->\n";
echo "<!-- Include path: " . get_include_path() . " -->\n";
echo "<!-- DIR: " . __DIR__ . " -->\n";

// 设置缓存头
$cache_time = 3600; // 1小时
header('Cache-Control: no-transform, max-age=' . $cache_time);
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $cache_time) . ' GMT');
header('Vary: Accept-Encoding');

// 设置执行时间和内存限制
ini_set('max_execution_time', 300);
ini_set('memory_limit', '512M');

// 开始计时
$start_time = microtime(true);

// 设置字符编码
header('Content-Type: text/html; charset=utf-8');

// 添加错误处理函数
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    error_log("错误: {$errstr} in {$errfile} on line {$errline}");
    echo "<!-- 错误: {$errstr} in {$errfile} on line {$errline} -->\n";
    return true;
}
set_error_handler("custom_error_handler");

// 创建日志目录
$log_dir = __DIR__ . '/../../logs';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0777, true);
}

// 读取预处理的统计数据
try {
    echo "<!-- 尝试加载数据库连接文件 -->\n";
    $db_conn_file = __DIR__ . '/../../includes/config/db_connection.php';
    echo "<!-- 数据库连接文件路径: " . $db_conn_file . " -->\n";
    
    if (file_exists($db_conn_file)) {
        echo "<!-- 数据库连接文件存在 -->\n";
        require_once($db_conn_file);
        echo "<!-- 数据库连接文件已加载 -->\n";
    } else {
        echo "<!-- 数据库连接文件不存在 -->\n";
        throw new Exception("数据库连接文件不存在: " . $db_conn_file);
    }
    
    // 数据库连接检查
    if (!isset($conn) || !$conn) {
        echo "<!-- 数据库连接对象不存在或为空 -->\n";
        throw new Exception("数据库连接失败");
    } else {
        echo "<!-- 数据库连接已建立 -->\n";
    }
    
    // 获取当前选择的分类级别（默认为genus）
    $current_level = isset($_GET['level']) ? $_GET['level'] : 'genus';

    // 获取排序参数（默认按nr_hosts降序）
    $sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'nr_hosts';
    $sort_dir = isset($_GET['dir']) ? $_GET['dir'] : 'desc';

    // 获取当前选择的分类级别和类群
    $current_group = isset($_GET['group']) ? str_replace('d__', '', $_GET['group']) : 'Bacteria';
    $search_term = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    // 根据当前选择的类群确定使用的表名
    $table_name = strtolower($current_group);
    // 默认使用bacteria表
    if ($table_name !== 'bacteria' && $table_name !== 'fungi') {
        $table_name = 'bacteria';
    }
    
    // 调试信息
    error_log("使用表: {$table_name}");

    // 修改基础查询
    $base_query = "SELECT id, name, taxonomy_path, nr_samples, nr_projects, nr_hosts, level FROM {$table_name} WHERE 1=1";
    $count_query = "SELECT COUNT(*) as total FROM {$table_name} WHERE 1=1";
    $params = array();
    
    // 调试信息 - 输出请求参数
    error_log("Request parameters: level={$current_level}, group={$current_group}, search={$search_term}");

    // 添加level筛选条件
    if ($current_level) {
        $base_query .= " AND level = ?";
        $count_query .= " AND level = ?";
        $params[] = $current_level;
    }

    // 添加group筛选条件，直接匹配taxonomy_path的开头
    if ($current_group) {
        $base_query .= " AND taxonomy_path LIKE ?";
        $count_query .= " AND taxonomy_path LIKE ?";
        $group_param = $current_group . ';%';  // 直接使用原始值匹配
        $params[] = $group_param;
    }

    // 添加搜索条件
    if ($search_term) {
        $base_query .= " AND (name LIKE ? OR taxonomy_path LIKE ?)";
        $count_query .= " AND (name LIKE ? OR taxonomy_path LIKE ?)";
        $search_param = '%' . $search_term . '%';
        $params[] = $search_param;
        $params[] = $search_param;
    }

    // 调试信息 - 输出完整的SQL查询
    $debug_query = $count_query;
    foreach ($params as $param) {
        $debug_query = preg_replace('/\?/', "'" . $param . "'", $debug_query, 1);
    }
    error_log("完整的SQL查询: " . $debug_query);

    // 打印SQL查询
    echo "<!-- SQL Query: " . $base_query . " -->\n";
    echo "<!-- Parameters: " . print_r($params, true) . " -->\n";

    // 获取总记录数
    $stmt = $conn->prepare($count_query);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数
    if ($params) {
        $types = str_repeat('s', count($params));
        $ref_params = array();
        $ref_params[] = &$types;
        foreach($params as $key => $value) {
            $ref_params[] = &$params[$key];
        }
        
        if (!call_user_func_array(array($stmt, 'bind_param'), $ref_params)) {
            throw new Exception("参数绑定失败: " . $stmt->error);
        }
    }

    // 执行查询
    if (!$stmt->execute()) {
        throw new Exception("执行查询失败: " . $stmt->error);
    }

    // 获取结果
    $stmt->store_result();
    $total = 0;
    $stmt->bind_result($total);
    if ($stmt->fetch()) {
        $filtered_count = $total;
    } else {
        throw new Exception("获取结果失败");
    }
    $stmt->close();

    // 调试信息
    error_log("查询结果: 找到 {$filtered_count} 条记录");

    // 修改基础查询中的排序
    $base_query .= " ORDER BY " . $sort_by . " " . ($sort_dir === 'asc' ? 'ASC' : 'DESC');

    // 分页设置
    $page_sizes = [20, 50, 100, 500, 'all'];  // 修改选项
    $items_per_page_param = isset($_GET['per_page']) ? $_GET['per_page'] : 'all';  // 默认显示所有

    if ($items_per_page_param === 'all') {
        $items_per_page = $filtered_count;  // 显示所有记录
    } else {
        $items_per_page = intval($items_per_page_param);
        if (!in_array($items_per_page, array_filter($page_sizes, 'is_numeric')) || $items_per_page <= 0) {
            $items_per_page = $filtered_count;  // 如果无效，显示所有记录
        }
    }

    $total_items = $filtered_count;
    $total_pages = 1;  // 只有一页
    $current_page = 1;  // 始终在第一页
    $offset = 0;  // 从第一条记录开始

    // 修改查询，不添加 LIMIT 子句
    // $base_query .= " LIMIT ?, ?";
    // $params[] = $offset;
    // $params[] = $items_per_page;

    // 执行最终查询
    $stmt = $conn->prepare($base_query);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数
    if ($params) {
        $types = str_repeat('s', count($params));
        $ref_params = array();
        $ref_params[] = &$types;
        foreach($params as $key => $value) {
            $ref_params[] = &$params[$key];
        }
        
        if (!call_user_func_array(array($stmt, 'bind_param'), $ref_params)) {
            throw new Exception("参数绑定失败: " . $stmt->error);
        }
    }

    // 执行查询
    if (!$stmt->execute()) {
        throw new Exception("执行查询失败: " . $stmt->error);
    }

    // 获取结果
    $stmt->store_result();
    echo "<!-- 查询结果行数: " . $stmt->num_rows . " -->\n";

    // 创建一个数组来存储结果集的列
    $row = array();
    $row['id'] = '';
    $row['name'] = '';
    $row['taxonomy_path'] = '';
    $row['nr_samples'] = '';
    $row['nr_projects'] = '';
    $row['nr_hosts'] = '';
    $row['level'] = '';  // 添加level字段

    // 绑定结果列
    $stmt->bind_result(
        $row['id'],
        $row['name'],
        $row['taxonomy_path'],
        $row['nr_samples'],
        $row['nr_projects'],
        $row['nr_hosts'],
        $row['level']    // 添加level字段
    );

    // 获取结果
    $current_page_results = array();
    while ($stmt->fetch()) {
        $current_page_results[] = array(
            'id' => $row['id'],
            'name' => $row['name'],
            'taxonomy_path' => $row['taxonomy_path'],
            'nr_samples' => $row['nr_samples'],
            'nr_projects' => $row['nr_projects'],
            'nr_hosts' => $row['nr_hosts'],
            'level' => $row['level']    // 添加level字段
        );
    }

    $stmt->close();

    // 输出调试信息
    error_log("当前分类级别：{$current_level}");
    error_log("当前类群：{$current_group}");
    if ($search_term) {
        error_log("搜索关键词：{$search_term}");
    }

    // 计算执行时间
    $execution_time = microtime(true) - $start_time;
    error_log(sprintf(
        "执行时间: %.4f 秒, 内存使用: %.2f MB, 峰值内存: %.2f MB",
        $execution_time,
        memory_get_usage() / 1024 / 1024,
        memory_get_peak_usage() / 1024 / 1024
    ));

} catch (Exception $e) {
    error_log("错误: " . $e->getMessage());
    $error_message = "加载数据时出错。请稍后再试或联系管理员。";
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        header('HTTP/1.1 500 Internal Server Error');
        echo json_encode(['error' => $error_message]);
        exit;
    }
    die($error_message);
}

// 获取当前选择的分类级别（默认为genus）
$current_level = isset($_GET['level']) ? $_GET['level'] : 'genus';
echo "<!-- 当前分类级别: {$current_level} -->\n";

// 获取当前选择的类群（细菌、真菌、古菌）
$current_group = isset($_GET['group']) ? $_GET['group'] : 'Bacteria';
// 确保类群名称格式正确（移除d__前缀，因为数据中没有这个前缀）
$current_group = str_replace('d__', '', $current_group);
echo "<!-- 当前类群: {$current_group} -->\n";

// 确定当前使用的表名
$table_name = strtolower($current_group);
if ($table_name !== 'bacteria' && $table_name !== 'fungi') {
    $table_name = 'bacteria'; // 默认使用bacteria表
}
echo "<!-- 使用表: {$table_name} -->\n";

// 获取搜索关键词
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';

// 输出更多调试信息
echo "<!-- URL参数: " . http_build_query($_GET) . " -->\n";
echo "<!-- 过滤后数据数量: {$filtered_count} -->\n";

// 如果没有数据，显示提示信息
if ($filtered_count === 0) {
    echo '<div class="alert alert-info" style="margin-top: 20px;">';
    echo '<strong>No matching data found</strong><br>';
    echo 'Current filter conditions:<br>';
    echo '- Taxonomy Level: ' . htmlspecialchars($current_level) . '<br>';
    echo '- Group: ' . htmlspecialchars($current_group) . '<br>';
    if ($search_term) {
        echo '- Search Term: ' . htmlspecialchars($search_term) . '<br>';
    }
    echo '</div>';
}

// 生成分页URL的函数
function get_pagination_url($page, $per_page = null) {
    $params = $_GET;
    $params['page'] = $page;
    if ($per_page !== null) {
        $params['per_page'] = $per_page;
    }
    return '?' . http_build_query($params);
}

// 生成每页显示数量的URL
function get_per_page_url($per_page) {
    $params = $_GET;
    $params['per_page'] = $per_page;
    $params['page'] = 1;  // 切换每页显示数量时重置为第一页
    return '?' . http_build_query($params);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microbes Browser</title>
    <!-- 添加favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">
    
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/bootstrap-table.min.css">
    <link rel="stylesheet" href="../../public/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
    /* 修改容器样式 */
    body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            width: 100%;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
    }

    .container {
        max-width: 100% !important;
        padding: 2rem !important;
        margin: 0 auto !important;
        width: 100% !important;
    }
    
    /* 强制所有搜索框和工具栏样式 - 最高优先级 */
    .fixed-table-toolbar {
        height: 38px !important;
        display: flex !important;
        align-items: center !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .search input, .fixed-table-toolbar input {
        height: 38px !important;
        margin: 0 !important;
        padding: 6px 12px !important;
    }
    
    .fixed-table-toolbar .btn {
        height: 38px !important;
        margin: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* 添加表格与分页栏间距样式 */
    .bootstrap-table .fixed-table-pagination {
        margin-top: 20px !important;
        padding-top: 15px !important;
        border-top: 1px solid #dee2e6;
    }

    /* 标题和分隔线样式 */
    h3 {
        margin: 20px 0;
        color: #002060;
        font-weight: 600;
    }

    /* 分类按钮样式 */
    .taxonomy-tabs {
        margin: 20px 0;
        display: flex;
        gap: 10px;
    }

    .taxonomy-tabs .tab {
        padding: 8px 20px;
        border: 1px solid #dee2e6;
        background-color: #fff;
        color: #002060;
        border-radius: 4px;
        cursor: pointer;
        /* transition: all 0.3s ease; */ /* 移除过渡动画 */
        font-weight: 500;
    }

    .taxonomy-tabs .tab:hover {
        background-color: #f8f9fa;
        border-color: #002060;
    }

    .taxonomy-tabs .tab.active {
        background-color: #002060;
        color: #fff;
        border-color: #002060;
    }

    /* 分类级别按钮样式 */
    .taxonomy-levels {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        align-items: center;
        height: 38px; /* 固定高度 */
    }

    .taxonomy-levels .level {
        padding: 6px 15px;
        border: 1px solid #dee2e6;
        background-color: #fff;
        color: #002060;
        border-radius: 4px;
        cursor: pointer;
        /* transition: all 0.3s ease; */ /* 移除过渡动画 */
        font-size: 14px;
        height: 38px; /* 固定高度 */
        display: inline-flex;
        align-items: center;
    }

    .taxonomy-levels .level:hover {
        background-color: #f8f9fa;
        border-color: #002060;
    }

    .taxonomy-levels .level.active {
        background-color: #002060;
        color: #fff;
        border-color: #002060;
    }
    
    /* 工具栏和分类级别容器 */
    .toolbar-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0;
        min-height: 45px; /* 添加最小高度 */
    }
    
    /* 自定义工具栏容器 */
    #custom-toolbar {
        display: flex;
        align-items: center;
        height: 38px; /* 与分类级别按钮高度匹配 */
    }
    
    /* 确保工具栏右对齐 */
    .bootstrap-table .fixed-table-toolbar {
        margin: 0 !important;
        align-self: center; /* 确保垂直居中 */
    }

    /* 移动设备适配 */
    @media (max-width: 768px) {
        .container {
            max-width: 95% !important;
            width: 95% !important;
        }
        
        .taxonomy-tabs, .taxonomy-levels {
            flex-wrap: wrap;
        }
        
        .taxonomy-tabs .tab, .taxonomy-levels .level {
            flex: 1 1 auto;
            text-align: center;
        }
        
        .toolbar-container {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .bootstrap-table .fixed-table-toolbar {
            margin-top: 15px !important;
            width: 100%;
        }
    }
    </style>
</head>
<body>
    <!-- Loading indicator -->
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Navigation bar -->
    <header>
        <!-- Left Logo and title area -->
        <div class="left-content">
            <!-- Logo container -->
            <div class="logo-container">
                <img src="../../home/<USER>/logo.png" alt="MPRAM Logo" class="logo-image">
            </div>
            <!-- Title and subtitle container -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">MPRAM</h1>
                <p class="mb-0 small">Medicinal Plant Rhizosphere Associated Microbiome Database </p>
            </div>
        </div>
        <!-- Hamburger button -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- Navigation links area -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="../browse/browse.php" class="nav-link">Search</a></li>
                <li class="nav-item"><a href="../browse/Project.php" class="nav-link">Browse</a></li>
                <li class="nav-item"><a href="Microbes.php" class="nav-link active">Microbes</a></li>
                <li class="nav-item"><a href="Network.php" class="nav-link">Network</a></li>
                <li class="nav-item"><a href="../../map/map.html" class="nav-link">Map</a></li>
                <li class="nav-item"><a href="../help/help.html" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

<div class="container" style="margin-top: 80px;text-align: center;">
    <h3>Microbes Browser</h3>
    
    <div class="taxonomy-tabs">
        <button class="tab <?php echo $current_group === 'Bacteria' ? 'active' : ''; ?>" 
                onclick="window.location.href='?group=Bacteria&level=<?php echo $current_level; ?>'">
            Bacteria
        </button>
        <button class="tab <?php echo $current_group === 'Fungi' ? 'active' : ''; ?>"
                onclick="window.location.href='?group=Fungi&level=<?php echo $current_level; ?>'">
            Fungi
        </button>
        <button class="tab <?php echo $current_group === 'Archaea' ? 'active' : ''; ?>"
                onclick="window.location.href='?group=Archaea&level=<?php echo $current_level; ?>'">
            Archaea
        </button>
    </div>

    <div class="toolbar-container" style="display:flex; justify-content:space-between; align-items:center; height:38px; margin:20px 0;">
        <div class="taxonomy-levels" style="display:flex; gap:10px; align-items:center; height:38px;">
            <?php
            $levels = [
                'phylum' => 'Phylum', 
                'class' => 'Class', 
                'order' => 'Order', 
                'family' => 'Family', 
                'genus' => 'Genus'
            ];
            foreach ($levels as $level => $label):
            ?>
            <button class="level <?php echo $current_level === $level ? 'active' : ''; ?>"
                    style="height:38px; display:flex; align-items:center; padding:6px 15px; border:1px solid #dee2e6; background-color:#fff; color:#002060; border-radius:4px; cursor:pointer; font-size:14px; <?php echo $current_level === $level ? 'background-color:#002060; color:#fff; border-color:#002060;' : ''; ?>"
                    onclick="window.location.href='?level=<?php echo $level; ?>&group=<?php echo $current_group; ?>'">
                <?php echo $label; ?>
            </button>
            <?php endforeach; ?>
        </div>
        <div style="display:flex; align-items:center; height:38px;">
            <div id="toolbar" style="height:38px;"></div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="table-container">
            <table id="table" 
                   data-toggle="table"
                   data-pagination="true"
                   data-page-size="20"
                   data-page-list="[10, 20, 50, 100, 500]"
                   data-search="true"
                   data-show-columns="true"
                   data-show-refresh="true"
                   data-show-fullscreen="true"
                   data-buttons-class="btn"
                   data-search-align="right"
                   data-toolbar-align="right"
                   data-classes="table table-hover table-bordered"
                   data-thead-classes="thead-light"
                   data-striped="false"
                   data-pagination-h-align="right"
                   data-pagination-detail-h-align="left"
                   data-show-extended-pagination="true"
                   data-pagination-pre-text="‹"
                   data-pagination-next-text="›"
                   data-pagination-first-text="«"
                   data-pagination-last-text="»"
                   data-width="100%">
                <thead>
                    <tr>
                        <th data-field="name" data-sortable="true" data-halign="left" data-align="left">Name</th>
                        <th data-field="taxonomy_path" data-sortable="true" data-halign="left" data-align="left">Taxonomy Path</th>
                        <th data-field="nr_samples" data-sortable="true" data-halign="right" data-align="right">Nr. Samples</th>
                        <th data-field="nr_projects" data-sortable="true" data-halign="right" data-align="right">Nr. Projects</th>
                        <th data-field="nr_hosts" data-sortable="true" data-halign="right" data-align="right">Nr. Hosts</th>
                    </tr>
                </thead>
                <tbody>
                <?php if (count($current_page_results) > 0): ?>
                    <?php foreach($current_page_results as $row): ?>
                        <tr>
                            <td>
                                <a href="microbe_detail.php?id=<?php echo urlencode($row['id']); ?>&name=<?php echo urlencode($row['name']); ?>&level=<?php echo urlencode($row['level']); ?>&group=<?php echo urlencode(strtolower($current_group)); ?>" 
                                   class="microbe-link" style="color: #002060 !important; text-decoration: none !important; font-weight: bold !important;">
                                    <?php 
                                    $display_name = $row['name'];
                                    // 移除常见的分类学后缀
                                    $suffixes_to_remove = ['_unclassified', '_gen_Incertae_sedis'];
                                    foreach ($suffixes_to_remove as $suffix) {
                                        $display_name = str_replace($suffix, '', $display_name);
                                    }
                                    echo htmlspecialchars($display_name); 
                                    ?>
                                </a>
                            </td>
                            <td><?php echo htmlspecialchars($row['taxonomy_path']); ?></td>
                            <td><?php echo number_format($row['nr_samples']); ?></td>
                            <td><?php echo number_format($row['nr_projects']); ?></td>
                            <td><?php echo number_format($row['nr_hosts']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 页脚区域 -->
<footer class="container-fluid py-3 text-center">
    <p>Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
</footer>

<script src="../../public/js/jquery-3.7.1.min.js"></script>
<script src="../../public/js/bootstrap.bundle.min.js"></script>
<script src="../../public/js/bootstrap-table.min.js"></script>

<!-- 立即执行的脚本，修复布局 -->
<script>
// 工具栏修复函数
function fixToolbarHeight() {
    // 选择所有相关元素
    const toolbars = document.querySelectorAll('.fixed-table-toolbar');
    const searchInputs = document.querySelectorAll('.search input');
    const buttons = document.querySelectorAll('.fixed-table-toolbar .btn');
    
    // 应用固定高度和对齐样式
    toolbars.forEach(toolbar => {
        toolbar.style.cssText = 'height:38px !important; display:flex !important; align-items:center !important; margin:0 !important;';
    });
    
    searchInputs.forEach(input => {
        input.style.cssText = 'height:38px !important; margin:0 !important; padding:6px 12px !important;';
    });
    
    buttons.forEach(btn => {
        btn.style.cssText = 'height:38px !important; margin:0 !important; display:flex !important; align-items:center !important; justify-content:center !important;';
    });
}

// 添加DOMContentLoaded事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 在不同时间点多次执行
    setTimeout(fixToolbarHeight, 10);
    setTimeout(fixToolbarHeight, 100);
    setTimeout(fixToolbarHeight, 500);
    setTimeout(fixToolbarHeight, 1000);
});

// 也添加到窗口加载事件
window.addEventListener('load', function() {
    // 在不同时间点多次执行
    setTimeout(fixToolbarHeight, 10);
    setTimeout(fixToolbarHeight, 100);
    setTimeout(fixToolbarHeight, 500);
    setTimeout(fixToolbarHeight, 1000);
});
</script>
<script>
    function toggleNav() {
        const nav = document.querySelector('nav');
        const hamburger = document.querySelector('.hamburger');
        nav.classList.toggle('show');
        hamburger.classList.toggle('active');
    }

    // 初始化表格
    $(document).ready(function() {
        const $table = $('#table');
        const $container = $('.table-container');
        
        // 直接应用工具栏对齐函数
        function applyToolbarStyles() {
            document.querySelectorAll('.fixed-table-toolbar').forEach(toolbar => {
                toolbar.setAttribute('style', 'height:38px !important; display:flex !important; align-items:center !important; margin:0 !important;');
            });
            
            document.querySelectorAll('.search input').forEach(input => {
                input.setAttribute('style', 'height:38px !important; margin:0 !important; padding:6px 12px !important;');
            });
            
            document.querySelectorAll('.fixed-table-toolbar .btn').forEach(btn => {
                btn.setAttribute('style', 'height:38px !important; margin:0 !important; display:flex !important; align-items:center !important; justify-content:center !important;');
            });
        }
        
        // 在多个时间点应用样式，确保正确渲染
        setTimeout(applyToolbarStyles, 0);
        setTimeout(applyToolbarStyles, 100);
        setTimeout(applyToolbarStyles, 500);
        
        $table.bootstrapTable({
            locale: 'en-US',
            classes: 'table table-hover table-bordered',
            theadClasses: 'thead-light',
            buttonsClass: 'btn',
            iconsPrefix: 'fa',
            icons: {
                fullscreen: 'fa-solid fa-expand',
                columns: 'fa-solid fa-columns',
                refresh: 'fa-solid fa-rotate',
                search: 'fa-solid fa-search',
                clearSearch: 'fa-solid fa-times',
                reset: 'fa-solid fa-undo'
            },
            pagination: true,
            search: true,
            sortable: true,
            pageSize: 20,
            pageList: [10, 20, 50, 100, 500],
            paginationHAlign: 'right',
            paginationVAlign: 'bottom',
            paginationDetailHAlign: 'left',
            paginationPreText: '‹',
            paginationNextText: '›',
            paginationFirstText: '«',
            paginationLastText: '»',
            showColumns: true,
            showRefresh: true,
            showFullscreen: true,
            showReset: true,  // 显示重置按钮
            sortName: 'nr_hosts',  // 设置默认排序字段
            sortOrder: 'desc',     // 设置默认排序方向
            toolbar: '#toolbar',   // 添加工具栏
            toolbarAlign: 'right',
            onPostBody: function() {
                // 直接应用样式
                applyToolbarStyles();
            },
            onRefresh: function() {
                // 刷新时应用样式
                applyToolbarStyles();
            },
            onSearch: function() {
                // 搜索时应用样式
                setTimeout(applyToolbarStyles, 0);
            },
            onToggle: function() {
                // 切换视图时应用样式
                setTimeout(applyToolbarStyles, 0);
            },
            buttons: [{
                text: 'Reset',
                icon: 'fa-solid fa-undo',
                event: function () {
                    // 重置搜索
                    $table.bootstrapTable('resetSearch');
                    
                    // 重置排序为默认
                    $table.bootstrapTable('refreshOptions', {
                        sortName: 'nr_hosts',
                        sortOrder: 'desc'
                    });
                    
                    // 显示所有列
                    $table.bootstrapTable('showAllColumns');
                    
                    // 重置到第一页
                    $table.bootstrapTable('selectPage', 1);
                    
                    // 刷新表格
                    $table.bootstrapTable('refresh');
                },
                attributes: {
                    title: 'Reset all settings'
                }
            }]
        });

        // 添加自定义样式
        const style = document.createElement('style');
        style.textContent = `
            .bootstrap-table .fixed-table-pagination .pagination-detail,
            .bootstrap-table .fixed-table-pagination .pagination {
                margin: 0;
            }
            
            .bootstrap-table .pagination .page-link {
                padding: 6px 12px;
                border: 1px solid #dee2e6;
                margin: 0 2px;
                color: #333;
                background-color: #fff;
                border-radius: 4px;
            }
            
            .bootstrap-table .pagination .active .page-link,
            .bootstrap-table .pagination .active a,
            .bootstrap-table .pagination .active span,
            .bootstrap-table .pagination .active a:hover,
            .bootstrap-table .pagination .active span:hover {
                background-color: #002060 !important;
                border-color: #002060 !important;
                color: #fff !important;
            }
            
            .bootstrap-table .pagination li:not(.active) .page-link:hover {
                color: #002060;
                border-color: #002060;
                background-color: #f8f9fa;
            }
            
            .bootstrap-table .page-list .btn-group .dropdown-toggle {
                color: #002060;
                border-color: #002060;
            }
            
            .bootstrap-table .page-list .btn-group.show .dropdown-toggle {
                background-color: #002060;
                color: #fff;
            }
            
            .bootstrap-table .dropdown-item.active, 
            .bootstrap-table .dropdown-item:active {
                background-color: #002060;
                color: #fff;
            }
            
            .table-container {
                margin: 20px 0;
                width: 100%;
            }
            
            .bootstrap-table .table {
                border: 1px solid #dee2e6;
                margin-bottom: 0;
            }
            
            .bootstrap-table .fixed-table-container {
                border: none;
            }
            
            .bootstrap-table .search input {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px 12px;
            }
            
            .microbe-link {
                color: #002060 !important;
                text-decoration: none !important;
                font-weight: bold !important;
            }
            
            /* 自定义工具栏容器样式 */
            #toolbar {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                height: 38px; /* 固定高度与按钮一致 */
            }
            
            /* 确保搜索框和工具栏按钮组在同一行 */
            .bootstrap-table .fixed-table-toolbar {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                gap: 10px;
                height: 38px; /* 固定高度与按钮一致 */
                padding: 0;
                margin: 0 !important;
            }
            
            .bootstrap-table .fixed-table-toolbar .bs-bars,
            .bootstrap-table .fixed-table-toolbar .search,
            .bootstrap-table .fixed-table-toolbar .columns {
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                padding-top: 0 !important;
                padding-bottom: 0 !important;
                display: flex;
                align-items: center;
                height: 38px; /* 固定高度与按钮一致 */
            }
            
            /* 修正搜索框样式 */
            .bootstrap-table .search input {
                height: 38px !important;
                margin: 0 !important;
                padding: 6px 12px !important;
            }
            
            /* 修正工具栏按钮样式 */
            .bootstrap-table .fixed-table-toolbar .btn {
                height: 38px !important;
                margin: 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
            
            /* 添加重置按钮样式 */
            .bootstrap-table .fixed-table-toolbar .bs-bars button[name="reset"] {
                margin-left: 5px;
                background-color: #f8f9fa;
                border-color: #dee2e6;
                color: #002060;
            }
            
            .bootstrap-table .fixed-table-toolbar .bs-bars button[name="reset"]:hover {
                background-color: #e9ecef;
                border-color: #002060;
            }
            
            /* 移除表格加载过渡效果 */
            .bootstrap-table .fixed-table-body {
                transition: none !important;
            }
            
            /* 确保数字列始终右对齐 */
            .bootstrap-table td[data-field="nr_samples"],
            .bootstrap-table td[data-field="nr_projects"],
            .bootstrap-table td[data-field="nr_hosts"] {
                text-align: right !important;
            }
            
            /* 移除所有可能的过渡效果 */
            .bootstrap-table * {
                transition: none !important;
            }
            
            /* 确保表格立即显示，无加载动画 */
            .bootstrap-table .fixed-table-loading {
                display: none !important;
            }
        `;
        document.head.appendChild(style);
        
                        // 监听表格事件
        $table.on('post-body.bs.table', applyToolbarStyles);
        $table.on('post-header.bs.table', applyToolbarStyles);
        $table.on('refresh.bs.table', applyToolbarStyles);
        
        // 页面大小改变时重新应用
        window.addEventListener('resize', applyToolbarStyles);
        
        // 表格全部渲染完成后
        $table.on('load-success.bs.table', function() {
            setTimeout(applyToolbarStyles, 0);
            setTimeout(applyToolbarStyles, 100);
            setTimeout(applyToolbarStyles, 300);
        });
        
        // 强制在初始化后多次调整（确保工具栏元素已完全渲染）
        setTimeout(applyToolbarStyles, 100);
        setTimeout(applyToolbarStyles, 300);
        setTimeout(applyToolbarStyles, 500);
        setTimeout(applyToolbarStyles, 1000);
    });

    // 列显示控制功能
    function toggleColumnMenu() {
        const menu = document.getElementById('columnMenu');
        menu.classList.toggle('show');
    }

    // 点击其他地方时关闭菜单
    document.addEventListener('click', function(e) {
        const menu = document.getElementById('columnMenu');
        const btn = document.querySelector('.column-toggle-btn');
        if (!menu.contains(e.target) && !btn.contains(e.target)) {
            menu.classList.remove('show');
        }
    });

    // 控制列的显示/隐藏
    document.querySelectorAll('.column-menu input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const columnIndex = this.getAttribute('data-column');
            const table = document.querySelector('.microbes-table');
            const cells = table.querySelectorAll(`th:nth-child(${parseInt(columnIndex) + 1}), td:nth-child(${parseInt(columnIndex) + 1})`);
            
            cells.forEach(cell => {
                cell.classList.toggle('hidden-column', !this.checked);
            });
        });
    });

    // 修改下载数据功能，只导出显示的列
    function downloadData() {
        const table = document.querySelector('.microbes-table');
        const headers = [];
        const visibleColumns = [];
        
        // 获取可见列的索引
        table.querySelectorAll('th').forEach((th, index) => {
            if (!th.classList.contains('hidden-column')) {
                headers.push(th.textContent.trim());
                visibleColumns.push(index);
            }
        });
        
        let csv = headers.join('\t') + '\n';
        
        // 获取可见列的数据
        table.querySelectorAll('tbody tr').forEach(row => {
            const rowData = [];
            visibleColumns.forEach(colIndex => {
                const cell = row.cells[colIndex];
                if (cell) {
                    const text = cell.textContent.trim();
                    rowData.push(text.includes('\t') ? `"${text}"` : text);
                }
            });
            csv += rowData.join('\t') + '\n';
        });
        
        const blob = new Blob([csv], { type: 'text/tab-separated-values' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `microbes_${<?php echo json_encode($current_group); ?>}_${<?php echo json_encode($current_level); ?>}.tsv`;
        a.click();
        window.URL.revokeObjectURL(url);
    }

    // 页面加载完成后处理
    window.addEventListener('load', function() {
        // 隐藏加载指示器
        document.querySelector('.loading-overlay').style.display = 'none';
        
        // 立即应用右对齐样式，确保没有过渡效果
        document.querySelectorAll('td[data-field="nr_samples"], td[data-field="nr_projects"], td[data-field="nr_hosts"]').forEach(cell => {
            cell.style.textAlign = 'right';
        });
        
        // 检查表格是否有数据
        const tbody = document.querySelector('.microbes-table tbody');
        if (tbody && tbody.children.length === 0) {
            console.log("No data in table, adding notification row");
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = '<td colspan="5" style="text-align: center; padding: 20px;">No matching data found</td>';
            tbody.appendChild(emptyRow);
        }
    });

</script>

</body>
</html> 