<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MPRAM - Projects</title>
    <!-- 添加favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">
    
    <meta name="description" content="MPRAM - 中药材根际微生物组数据库，项目浏览页面。">
    <meta name="keywords" content="中药材,微生物组,根际微生物,MPRAM,数据库,项目">
    <meta name="author" content="MPRAM开发团队">
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/common.css">
    <link rel="stylesheet" href="../../public/css/bootstrap-table.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 修复body::before导致的顶部空白 */
        body::before {
            content: none !important;
            display: none !important;
        }

        /* 标签页样式 */
        .nav-tabs {
            border-bottom: 2px solid #002060;
            margin-bottom: 30px;
        }

        .nav-tabs .nav-link {
            border: none;
            border-bottom: 3px solid transparent;
            color: #002060;
            font-weight: 600;
            padding: 12px 24px;
            margin: 0 5px;
            border-radius: 0;
        }

        .nav-tabs .nav-link:hover {
            border-bottom-color: #002060;
            background-color: rgba(0, 32, 96, 0.05);
        }

        .nav-tabs .nav-link.active {
            color: #002060;
            background-color: rgba(0, 32, 96, 0.1);
            border-bottom-color: #002060;
            font-weight: 700;
        }

        /* 药典按钮样式 */
        .pharmacopoeia-btn {
            font-size: 14px;
            padding: 8px 16px;
            font-weight: 500;
        }

        .pharmacopoeia-btn.active {
            background-color: #002060;
            color: white;
            border-color: #002060;
        }

        .pharmacopoeia-btn:hover:not(.active) {
            background-color: rgba(0, 32, 96, 0.1);
            border-color: #002060;
        }

        /* 响应式设计 */
        @media (max-width: 1199px) {
            .pharmacopoeia-sidebar {
                margin-bottom: 20px;
                position: static;
            }

            .pharmacopoeia-list {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .pharmacopoeia-item {
                padding: 8px 10px;
            }

            .pharmacopoeia-item .item-title {
                font-size: 12px;
            }

            .pharmacopoeia-item .item-subtitle {
                font-size: 9px;
            }
        }

        @media (max-width: 991px) {
            .pharmacopoeia-sidebar {
                padding: 15px;
            }

            .pharmacopoeia-list {
                grid-template-columns: repeat(3, 1fr);
                gap: 6px;
            }

            .pharmacopoeia-item {
                padding: 6px 8px;
            }

            .pharmacopoeia-item .item-content {
                margin-left: 6px;
            }

            .pharmacopoeia-item .item-title {
                font-size: 11px;
            }

            .pharmacopoeia-item .item-subtitle {
                font-size: 8px;
            }
        }

        @media (max-width: 768px) {
            .nav-tabs .nav-link {
                padding: 8px 16px;
                font-size: 14px;
            }

            .pharmacopoeia-sidebar {
                padding: 15px;
            }

            .pharmacopoeia-list {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .pharmacopoeia-item {
                padding: 8px 10px;
            }

            .pharmacopoeia-item .item-content {
                margin-left: 8px;
            }

            .pharmacopoeia-item .item-title {
                font-size: 12px;
            }

            .pharmacopoeia-item .item-subtitle {
                font-size: 9px;
            }

            .pharmacopoeia-badge {
                width: 12px;
                height: 12px;
                margin-right: 6px;
            }

            .table-section {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .pharmacopoeia-list {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .pharmacopoeia-item {
                padding: 10px;
            }

            .pharmacopoeia-item .item-title {
                font-size: 13px;
            }

            .pharmacopoeia-item .item-subtitle {
                font-size: 10px;
            }

            .pharmacopoeia-badge {
                width: 14px;
                height: 14px;
            }
        }

        /* 加载动画样式 */
        #pharmacopoeia-loading {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 表格样式优化 */
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-top: 20px;
        }

        /* 药典表格容器特殊样式 */
        .table-section .table-container {
            padding: 0;
            margin-top: 0;
            border: 1px solid #e9ecef;
            box-shadow: none;
        }

        .table-section .table-container table {
            margin: 0;
        }

        /* 药典颜色标识 */
        .pharmacopoeia-badge {
            display: inline-block;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            margin-right: 8px;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            vertical-align: middle;
            position: relative;
        }

        .pharmacopoeia-badge::after {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .pharmacopoeia-text {
            font-size: 12px;
            font-weight: 600;
            color: #333;
            margin-right: 8px;
            vertical-align: middle;
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
        }

        .pharmacopoeia-item {
            display: inline-block;
            margin: 3px 5px;
            padding: 4px 8px;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
            border-radius: 16px;
            border: 1px solid rgba(0, 32, 96, 0.1);
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .pharmacopoeia-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* 简化的药典标识样式 - 用于表格中 */
        .pharmacopoeia-badges-inline {
            display: flex;
            align-items: center;
            gap: 6px;
            flex-wrap: wrap;
        }

        .pharmacopoeia-badge-simple {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            cursor: help;
            transition: all 0.2s ease;
        }

        .pharmacopoeia-badge-simple:hover {
            transform: scale(1.2);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
        }

        /* 药典按钮颜色标识 */
        .pharmacopoeia-btn .badge-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
            border: 2px solid white;
            vertical-align: middle;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .pharmacopoeia-btn .badge-indicator::after {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* 各药典的专属颜色 */
        .chp-2020 { background-color: #FF6B6B; } /* 红色 - 中国药典 */
        .usp-hmc { background-color: #4ECDC4; } /* 青色 - 美国药典 */
        .ph-eur { background-color: #45B7D1; } /* 蓝色 - 欧洲药典 */
        .jp-18 { background-color: #96CEB4; } /* 绿色 - 日本药典 */
        .kp-12 { background-color: #FFEAA7; } /* 黄色 - 韩国药典 */
        .ip-2018 { background-color: #DDA0DD; } /* 紫色 - 印度药典 */
        .ep-2005 { background-color: #F39C12; } /* 橙色 - 埃及药典 */
        .fb-6 { background-color: #58D68D; } /* 浅绿色 - 巴西药典 */

        /* 药典侧边栏样式 */
        .pharmacopoeia-sidebar {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            height: fit-content;
            position: sticky;
            top: 20px;
            border: 1px solid #e9ecef;
        }

        .sidebar-header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 12px;
            border-bottom: 2px solid #e9ecef;
            position: relative;
        }

        .sidebar-header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background: #002060;
        }

        .pharmacopoeia-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        /* 药典列表项样式 */
        .pharmacopoeia-item {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            display: flex;
            align-items: center;
            text-align: left;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .pharmacopoeia-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: transparent;
            transition: all 0.3s ease;
        }

        .pharmacopoeia-item:hover {
            transform: translateX(5px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            border-color: #002060;
        }

        .pharmacopoeia-item:hover::before {
            background: #002060;
        }

        .pharmacopoeia-item.active {
            background: linear-gradient(145deg, #002060, #003080);
            border-color: #002060;
            color: white;
            box-shadow: 0 6px 20px rgba(0, 32, 96, 0.3);
            transform: translateX(8px);
        }

        .pharmacopoeia-item.active::before {
            background: #ffffff;
            width: 6px;
        }

        .pharmacopoeia-item .item-content {
            margin-left: 8px;
            flex: 1;
        }

        .pharmacopoeia-item .item-title {
            font-size: 12px;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1px;
        }

        .pharmacopoeia-item .item-subtitle {
            font-size: 9px;
            opacity: 0.8;
            font-weight: 500;
            line-height: 1;
        }

        .pharmacopoeia-item.active .item-subtitle {
            opacity: 0.9;
        }

        /* 表格区域样式 */
        .table-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            min-height: 600px;
        }
        
        .table-container {
            margin-top: 20px;
            overflow-x: auto;
            width: 100%;
        }
        #toolbar {
            margin-bottom: 15px;
            text-align: right;
        }
        .bootstrap-table .fixed-table-toolbar .columns-dropdown {
            margin-left: 5px;
        }
        /* 增加列选择下拉框高度，显示所有选项 */
        .bootstrap-table .fixed-table-toolbar .dropdown-menu,
        .bootstrap-table .columns .dropdown-menu,
        .dropdown-menu.dropdown-menu-right,
        body .bootstrap-table .dropdown-menu {
            max-height: none !important;
            overflow-y: visible !important;
            height: auto !important;
            min-width: 200px !important;
            padding: 10px !important;
            position: absolute !important;
        }
        
        /* 确保复选框列表项样式 */
        .dropdown-menu .dropdown-item label,
        body .bootstrap-table .dropdown-menu .dropdown-item label {
            display: block;
            width: 100%;
            padding: 4px 0;
            margin: 0;
            white-space: nowrap;
        }
        
        /* 确保下拉菜单可见 */
        body .bootstrap-table .dropdown-menu.show {
            display: block !important;
            z-index: 9999 !important;
            transform: none !important;
        }
        .bootstrap-table .fixed-table-toolbar .search {
            margin-bottom: 20px;
        }
        .bootstrap-table .table {
            font-size: 14px;
            width: 100% !important;
        }
        .bootstrap-table .table td,
        .bootstrap-table .table th {
            padding: 0.5rem;
            white-space: nowrap;
            text-align: center; /* 表格文字居中显示 */
            vertical-align: middle; /* 垂直居中 */
        }
        .bootstrap-table .table thead th {
            border-bottom: 1px solid #dee2e6;
            background-color: #f8f9fa;
            text-align: center; /* 表头文字居中 */
            font-weight: 600; /* 表头稍微加粗 */
        }
        /* Study ID 链接样式 */
        .study-id-link {
            color: #002060 !important; /* 设置颜色为深蓝色 */
            font-weight: bold; /* 加粗 */
            text-decoration: none !important; /* 去除下方横线 */
        }
        .study-id-link:hover {
            opacity: 0.8; /* 悬停时稍微变淡 */
        }
        /* 拉丁文斜体样式 */
        .latin-name {
            font-style: italic;
        }
        .container {
            max-width: 100% !important;
            padding: 0 30px;
        }
        .row.justify-content-center {
            width: 100%;
            margin: 0;
            padding: 0;
        }
        .fixed-table-body {
            overflow-x: auto;
            overflow-y: auto;
            width: 100%;
        }
        /* 添加按钮图标样式 */
        .bootstrap-table .fixed-table-toolbar .bs-bars,
        .bootstrap-table .fixed-table-toolbar .columns,
        .bootstrap-table .fixed-table-toolbar .search {
            margin-left: 5px;
            margin-right: 5px;
        }
        .bootstrap-table .fixed-table-toolbar button i {
            margin-right: 5px;
        }
        /* 内容区域样式 */
        .content-section {
            background-color: #F9FAFB;
            border-radius: 12px;
            padding: 2rem;
            margin-top: 0;
            width: 100%;
            max-width: 2200px;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
        }
        /* 移除顶部空白 */
        body {
            margin: 0;
            padding: 0;
        }
        .container.content-section.mt-5 {
            margin-top: 1rem !important;
        }
        /* 分页导航样式 */
        .page-item.active .page-link {
            background-color: #002060 !important;
            border-color: #002060 !important;
            color: white !important;
        }
        
        .page-link {
            color: #002060 !important;
        }
        
        .page-link:hover {
            background-color: #f0f2f5;
        }
    </style>
</head>
<body>
    <!--导航栏-->
    <header>
        <!-- 左侧Logo和标题区域 -->
        <div class="left-content">
            <!-- Logo容器 -->
            <div class="logo-container">
                <img src="../../home/<USER>/logo.png" alt="MPRAM Logo" class="logo-image">
            </div>
            <!-- 标题和副标题容器 -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">MPRAM</h1>
                <p class="mb-0 small">Medicinal Plant Rhizosphere Associated Microbiome Database </p>
            </div>
        </div>
        <!-- 汉堡包按钮 -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- 导航链接区域 -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="browse.php" class="nav-link">Search</a></li>
                <li class="nav-item"><a href="Project.php" class="nav-link active">Browse</a></li>
                <li class="nav-item"><a href="../microbes/Microbes.php" class="nav-link">Microbes</a></li>
                <li class="nav-item"><a href="../microbes/Network.php" class="nav-link">Network</a></li>
                <li class="nav-item"><a href="../../map/map.html" class="nav-link">Map</a></li>
                <li class="nav-item"><a href="../help/help.php" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

    <!-- 使用 Bootstrap grid 系统来居中显示和自适应大小 -->
    <div class="container content-section">
        <h2 style="text-align: center; font-size: 1.5rem; color: #002060; font-weight: 600; padding-bottom: 0px; border-bottom: 1px">Herbal Studies Projects</h2>
        
        <!-- 添加药典筛选按钮 -->
        <!-- 标签页导航 -->
        <div class="tab-navigation" style="margin: 20px 0;">
            <ul class="nav nav-tabs justify-content-center" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="projects-tab" data-bs-toggle="tab" data-bs-target="#projects-content" type="button" role="tab" aria-controls="projects-content" aria-selected="true" style="color: #002060; border-color: #002060;">
                        <i class="fas fa-database me-2"></i>Projects
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pharmacopoeia-tab" data-bs-toggle="tab" data-bs-target="#pharmacopoeia-content" type="button" role="tab" aria-controls="pharmacopoeia-content" aria-selected="false" style="color: #002060; border-color: #002060;">
                        <i class="fas fa-book-medical me-2"></i>Pharmacopoeia
                    </button>
                </li>
            </ul>
        </div>
        
        <!-- 添加开发中提示的模态框 -->
        <div class="modal fade" id="developmentModal" tabindex="-1" aria-labelledby="developmentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="developmentModalLabel" style="color: #002060;">Notice</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p style="text-align: center; margin: 0;">To be developed</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 标签页内容 -->
        <div class="tab-content" id="mainTabsContent">
            <!-- Projects 标签页内容 -->
            <div class="tab-pane fade show active" id="projects-content" role="tabpanel" aria-labelledby="projects-tab">
                <div class="row justify-content-center">
                    <div class="table-container">
                        <table id="table"></table>
                    </div>
                </div>
            </div>

            <!-- Pharmacopoeia 标签页内容 -->
            <div class="tab-pane fade" id="pharmacopoeia-content" role="tabpanel" aria-labelledby="pharmacopoeia-tab">
                <!-- 药典说明 -->
                <div class="alert alert-info" style="margin: 20px 0; border-color: #002060; background-color: rgba(0, 32, 96, 0.05);">
                    <h6 style="color: #002060; margin-bottom: 10px;"><i class="fas fa-info-circle me-2"></i>Pharmacopoeia Database</h6>
                    <p style="margin: 0; color: #002060;">Browse medicinal plants from 8 authoritative pharmacopoeias worldwide. Click on a pharmacopoeia below to view its medicinal plant entries.</p>
                </div>

                <!-- 左右分栏布局 -->
                <div class="row">
                    <!-- 左侧：药典选择 -->
                    <div class="col-lg-2 col-md-3">
                        <div class="pharmacopoeia-sidebar">
                            <div class="sidebar-header">
                                <h6 style="color: #002060; font-weight: 600; font-size: 16px; margin-bottom: 15px;">
                                    <i class="fas fa-book-medical me-2"></i>Select Pharmacopoeia
                                </h6>
                                <span id="pharmacopoeia-count" class="badge bg-secondary" style="font-size: 12px; display: none;"></span>
                            </div>

                            <!-- 药典选择列表 -->
                            <div class="pharmacopoeia-list">
                                <button type="button" class="pharmacopoeia-item active" data-file="1_Pharmacopoeia_of_the_People's_Republic_of_China_details.csv" data-name="ChP 2020">
                                    <span class="pharmacopoeia-badge chp-2020"></span>
                                    <div class="item-content">
                                        <div class="item-title">ChP 2020</div>
                                        <div class="item-subtitle">China</div>
                                    </div>
                                </button>

                                <button type="button" class="pharmacopoeia-item" data-file="2_USP-Herbal_Medicines_Compendium_details.csv" data-name="USP-HMC">
                                    <span class="pharmacopoeia-badge usp-hmc"></span>
                                    <div class="item-content">
                                        <div class="item-title">USP-HMC</div>
                                        <div class="item-subtitle">USA</div>
                                    </div>
                                </button>

                                <button type="button" class="pharmacopoeia-item" data-file="3_European_Pharmacopoeia_details.csv" data-name="Ph. Eur. 10.0">
                                    <span class="pharmacopoeia-badge ph-eur"></span>
                                    <div class="item-content">
                                        <div class="item-title">Ph. Eur. 10.0</div>
                                        <div class="item-subtitle">Europe</div>
                                    </div>
                                </button>

                                <button type="button" class="pharmacopoeia-item" data-file="4_Japanese_Pharmacopoeia_details.csv" data-name="JP 18">
                                    <span class="pharmacopoeia-badge jp-18"></span>
                                    <div class="item-content">
                                        <div class="item-title">JP 18</div>
                                        <div class="item-subtitle">Japan</div>
                                    </div>
                                </button>

                                <button type="button" class="pharmacopoeia-item" data-file="5_Korean_Pharmacopoeia_details.csv" data-name="KP 12">
                                    <span class="pharmacopoeia-badge kp-12"></span>
                                    <div class="item-content">
                                        <div class="item-title">KP 12</div>
                                        <div class="item-subtitle">Korea</div>
                                    </div>
                                </button>

                                <button type="button" class="pharmacopoeia-item" data-file="6_Indian_Pharmacopoeia_details.csv" data-name="IP 2018">
                                    <span class="pharmacopoeia-badge ip-2018"></span>
                                    <div class="item-content">
                                        <div class="item-title">IP 2018</div>
                                        <div class="item-subtitle">India</div>
                                    </div>
                                </button>

                                <button type="button" class="pharmacopoeia-item" data-file="7_Egyptian_Pharmacopoeia_details.csv" data-name="EP 2005">
                                    <span class="pharmacopoeia-badge ep-2005"></span>
                                    <div class="item-content">
                                        <div class="item-title">EP 2005</div>
                                        <div class="item-subtitle">Egypt</div>
                                    </div>
                                </button>

                                <button type="button" class="pharmacopoeia-item" data-file="8_Farmacopeia_Brasileira_details.csv" data-name="FB 6">
                                    <span class="pharmacopoeia-badge fb-6"></span>
                                    <div class="item-content">
                                        <div class="item-title">FB 6</div>
                                        <div class="item-subtitle">Brazil</div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：表格 -->
                    <div class="col-lg-10 col-md-9">
                        <div class="table-section">
                            <div id="pharmacopoeia-toolbar" style="margin-bottom: 15px; text-align: right;">
                                <button type="button" class="btn btn-outline-success btn-sm" id="export-pharmacopoeia" style="border-color: #002060; color: #002060;">
                                    <i class="fas fa-download me-1"></i>Export CSV
                                </button>
                            </div>
                            <div id="pharmacopoeia-loading" style="text-align: center; padding: 40px; display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading pharmacopoeia data...</p>
                            </div>
                            <div class="table-container">
                                <table id="pharmacopoeia-table"></table>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 页脚区域 -->
    <footer class="container-fluid py-3 text-center">
        <p class="small">Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
    </footer>

    <script src="../../public/js/jquery-3.7.1.min.js"></script>
    <script src="../../public/js/bootstrap.bundle.min.js"></script>
    <script src="../../public/js/bootstrap-table.min.js"></script>
    <script src="../../public/js/common.js"></script>

    <script>
        // 汉堡包按钮控制函数
        function toggleNav() {
            const nav = document.querySelector('nav');
            const hamburger = document.querySelector('.hamburger');
            nav.classList.toggle('show');
            hamburger.classList.toggle('active');
        }
        
        var $table;

        function InitMainTable() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const searchQuery = urlParams.get('search_query');
            const searchBy = urlParams.get('search_by');

            // 设置表格初始化参数
            let tableOptions = {
                url: "../../api/getProjectData.php",
                method: 'GET',
                pagination: true,
                striped: true,
                search: true,
                searchAlign: 'right',
                showColumns: true,
                showToggle: true,
                showFullscreen: true,
                showRefresh: true,
                pageSize: 20,
                pageList: [10, 20, 50, 100],
                locale: 'zh-CN',
                toolbar: '#toolbar',
                toolbarAlign: 'right',
                width: '100%',
                queryParams: function(params) {
                    // 如果有搜索参数，添加到请求中
                    if (searchQuery && searchBy) {
                        params.search_query = searchQuery;
                        params.search_by = searchBy;
                    }
                    return params;
                },
                icons: {
                    fullscreen: 'fa-solid fa-expand',
                    columns: 'fa-solid fa-columns',
                    refresh: 'fa-solid fa-rotate',
                    toggleOff: 'fa-solid fa-table-list',
                    toggleOn: 'fa-solid fa-table-cells',
                    search: 'fa-solid fa-search',
                    clearSearch: 'fa-solid fa-times'
                },
                buttonsClass: 'btn btn-outline-secondary',
                iconsPrefix: 'fa',
                onRefresh: function() {
                    resetTable();
                    return false;
                },
                columns: [
                    { 
                        field: 'Study ID', 
                        title: 'Study ID', 
                        formatter: studyIdFormatter, 
                        sortable: true, 
                        visible: true,
                        align: 'center', /* 居中对齐 */
                        valign: 'middle', /* 垂直居中 */
                        sorter: function(a, b) {
                            // 提取数字部分
                            var numA = parseInt(a.replace(/[^\d]/g, '')) || 0;
                            var numB = parseInt(b.replace(/[^\d]/g, '')) || 0;
                            return numA - numB;
                        }
                    },
                    { field: 'Latin Name', title: 'Latin Name', formatter: studyNameFormatter, sortable: true, visible: true, align: 'center', valign: 'middle' },
                    { field: 'Chinese Name', title: 'Chinese Name', sortable: true, visible: true, align: 'center', valign: 'middle' },
                    { field: 'English Name', title: 'English Name', sortable: true, visible: true, align: 'center', valign: 'middle' },
                    { field: 'Studied microbes', title: 'Studied microbes', sortable: true, visible: true, align: 'center', valign: 'middle' },
                    { field: 'Soil Treatment Groups', title: 'Soil Treatment Groups', sortable: true, visible: true, align: 'center', valign: 'middle' },
                    { field: 'Cultivation Practice', title: 'Cultivation Practice', sortable: true, visible: true, align: 'center', valign: 'middle' },
                    { field: 'Source', title: 'Pharmacopoeia', sortable: true, visible: true, align: 'center', valign: 'middle' },
                    { field: 'Properties', title: 'Properties', sortable: true, visible: false, align: 'center', valign: 'middle' },
                    { field: 'Meridians', title: 'Meridians', sortable: true, visible: false, align: 'center', valign: 'middle' },
                    { field: 'Therapeutic class', title: 'Therapeutic class', sortable: true, visible: false, align: 'center', valign: 'middle' }
                ],
                onLoadError: function(status, res) {
                    console.error('数据加载失败:', status);
                    if (res && res.responseText) {
                        try {
                            var errorData = JSON.parse(res.responseText);
                            console.error('错误详情:', errorData);
                            alert('数据加载失败: ' + (errorData.error || '未知错误'));
                        } catch (e) {
                            console.error('解析错误响应失败:', e);
                            alert('数据加载失败，请检查数据库连接');
                        }
                    } else {
                        alert('数据加载失败，请检查网络连接');
                    }
                },
                onLoadSuccess: function(data) {
                    console.log('数据加载成功，共 ' + data.length + ' 条记录');
                }
            };

            $table = $('#table').bootstrapTable(tableOptions);

            // 如果有搜索参数，自动填充搜索框
            if (searchQuery) {
                $table.bootstrapTable('resetSearch', searchQuery);
            }
        }

        function studyNameFormatter(value, row) {
            if (!value) return '';
            return '<span class="latin-name">' + value + '</span>';
        }
        
        function studyIdFormatter(value, row) {
            if (!value) return '';
            return '<a href="Study.php?study_id=' + value + '" class="study-id-link">' + value + '</a>';
        }

        // 添加重置函数
        function resetTable() {
            // 重置排序
            $table.bootstrapTable('refreshOptions', {
                sortName: 'Study ID',
                sortOrder: 'asc',
                pageNumber: 1,
                pageSize: 20,
                searchText: ''
            });
            
            // 重置列的显示状态
            $table.bootstrapTable('hideAllColumns');
            $table.bootstrapTable('showColumn', 'Study ID');
            $table.bootstrapTable('showColumn', 'Latin Name');
            $table.bootstrapTable('showColumn', 'Chinese Name');
            $table.bootstrapTable('showColumn', 'English Name');
            $table.bootstrapTable('showColumn', 'Studied microbes');
            $table.bootstrapTable('showColumn', 'Soil Treatment Groups');
            $table.bootstrapTable('showColumn', 'Cultivation Practice');
            $table.bootstrapTable('showColumn', 'Source');
            
            // 重置药典筛选为ChP 2020
            $('.pharmacopoeia-filter .btn').removeClass('active').css({
                'background-color': '',
                'color': '#002060'
            });
            
            // 设置ChP 2020按钮为激活状态
            $('.pharmacopoeia-filter .btn[data-source="ChP 2020"]').addClass('active').css({
                'background-color': '#002060',
                'color': 'white'
            });
            
            // 应用ChP 2020筛选
            filterTableBySource('ChP 2020');
        }

        $(function () {
            InitMainTable();
            
            // 页面加载时默认应用ChP 2020筛选
            setTimeout(function() {
                filterTableBySource('ChP 2020');
            }, 500); // 延迟500毫秒执行，确保表格加载完成
            
            // 药典筛选按钮功能
            $('.pharmacopoeia-filter .btn').click(function() {
                var source = $(this).data('source');
                
                // 检查是否是开发中的功能
                if (source === 'JP 18' || source === 'CFDA') {
                    // 显示开发中提示
                    $('#developmentModal').modal('show');
                    return;
                }
                
                // 检查按钮状态
                if ($(this).hasClass('active')) {
                    // 如果已经是激活状态，则取消选中
                    $(this).removeClass('active').css({
                        'background-color': '',
                        'color': '#002060'
                    });
                    
                    // 不过滤，显示所有数据
                    $table.bootstrapTable('filterBy', {});
                } else {
                    // 更新按钮状态 - 先清除所有按钮的active状态
                    $('.pharmacopoeia-filter .btn').removeClass('active').css({
                        'background-color': '',
                        'color': '#002060'
                    });
                    
                    // 设置当前按钮为激活状态
                    $(this).addClass('active').css({
                        'background-color': '#002060',
                        'color': 'white'
                    });
                    
                    // 过滤表格数据
                    filterTableBySource(source);
                }
            });
            
            // 修复列选择下拉菜单的显示问题
            $(document).on('click', '.columns .dropdown-toggle', function() {
                setTimeout(function() {
                    var $dropdownMenu = $('.columns .dropdown-menu');
                    if ($dropdownMenu.length) {
                        $dropdownMenu.css({
                            'max-height': 'none',
                            'overflow-y': 'visible',
                            'height': 'auto',
                            'min-width': '200px',
                            'padding': '10px'
                        });
                        
                        // 确保所有选项可见
                        $dropdownMenu.find('.dropdown-item').css({
                            'display': 'block',
                            'white-space': 'nowrap'
                        });
                    }
                }, 100);
            });
        });
        
        // 根据药典筛选表格数据
        function filterTableBySource(source) {
            $table.bootstrapTable('filterBy', {
                source: source
            }, {
                'filterAlgorithm': function(row, filters) {
                    // 检查Source字段是否包含指定的药典
                    if (row.Source) {
                        return row.Source.includes(filters.source);
                    }
                    
                    return false;
                }
            });
        }

        // 药典功能相关代码
        var $pharmacopoeiaTable;
        var currentPharmacopoeiaFile = '1_Pharmacopoeia_of_the_People\'s_Republic_of_China_details.csv';

        // 药典颜色映射
        const pharmacopoeiaColors = {
            'Pharmacopoeia of the People\'s Republic of China': { class: 'chp-2020', name: 'ChP' },
            'USP - Herbal Medicines Compendium': { class: 'usp-hmc', name: 'USP' },
            'European Pharmacopoeia': { class: 'ph-eur', name: 'Ph.Eur' },
            'Japanese Pharmacopoeia': { class: 'jp-18', name: 'JP' },
            'Korean Pharmacopoeia': { class: 'kp-12', name: 'KP' },
            'Indian Pharmacopoeia': { class: 'ip-2018', name: 'IP' },
            'Egyptian Pharmacopoeia': { class: 'ep-2005', name: 'EP' },
            'Farmacopeia Brasileira': { class: 'fb-6', name: 'FB' }
        };

        // 生成药典标识HTML - 简化版本，只显示颜色圆点
        function generatePharmacopoeiaBadges(memberships) {
            if (!memberships) return '';

            const membershipList = memberships.split(';').map(m => m.trim());
            let html = '<div class="pharmacopoeia-badges-inline">';

            membershipList.forEach(membership => {
                const colorInfo = pharmacopoeiaColors[membership];
                if (colorInfo) {
                    html += `<span class="pharmacopoeia-badge-simple ${colorInfo.class}" title="${membership}"></span>`;
                } else {
                    // 如果没有找到匹配的药典，使用默认样式
                    html += `<span class="pharmacopoeia-badge-simple" style="background-color: #999;" title="${membership}"></span>`;
                }
            });

            html += '</div>';
            return html;
        }

        // 初始化药典表格
        function initPharmacopoeiaTable() {
            $pharmacopoeiaTable = $('#pharmacopoeia-table').bootstrapTable({
                columns: [
                    {
                        field: 'index',
                        title: 'No.',
                        align: 'center',
                        valign: 'middle',
                        width: 80,
                        formatter: function(value, row, index) {
                            return index + 1;
                        }
                    },
                    {
                        field: 'Species',
                        title: 'Species',
                        align: 'left',
                        valign: 'middle',
                        sortable: true,
                        width: 200
                    },
                    {
                        field: 'Chinese Name',
                        title: 'Chinese Name',
                        align: 'center',
                        valign: 'middle',
                        sortable: true,
                        width: 150,
                        formatter: function(value) {
                            if (value && value.trim()) {
                                return '<span style="color: #002060; font-weight: 600;">' + value + '</span>';
                            }
                            return '<span style="color: #999; font-style: italic;">N/A</span>';
                        }
                    },
                    {
                        field: 'Lineage',
                        title: 'Lineage',
                        align: 'left',
                        valign: 'middle',
                        sortable: true,
                        formatter: function(value) {
                            if (value && value.length > 80) {
                                return '<span title="' + value + '">' + value.substring(0, 80) + '...</span>';
                            }
                            return value;
                        }
                    },
                    {
                        field: 'Pharmacopoeia Memberships',
                        title: '<i class="fas fa-book-medical me-2" style="color: #002060;"></i>Pharmacopoeia Memberships',
                        align: 'left',
                        valign: 'middle',
                        sortable: true,
                        formatter: function(value) {
                            return generatePharmacopoeiaBadges(value);
                        }
                    }
                ],
                data: [],
                pagination: true,
                search: true,
                searchAlign: 'right',
                showColumns: true,
                showToggle: true,
                showFullscreen: true,
                showRefresh: true,
                pageSize: 20,
                pageList: [10, 20, 50, 100],
                locale: 'en-US',
                striped: true,
                icons: {
                    fullscreen: 'fa-solid fa-expand',
                    columns: 'fa-solid fa-columns',
                    refresh: 'fa-solid fa-rotate',
                    toggleOff: 'fa-solid fa-table-list',
                    toggleOn: 'fa-solid fa-table-cells',
                    search: 'fa-solid fa-search',
                    clearSearch: 'fa-solid fa-times'
                },
                buttonsClass: 'btn btn-outline-secondary',
                iconsPrefix: 'fa'
            });
        }

        // 加载药典数据
        function loadPharmacopoeiaData(filename) {
            $('#pharmacopoeia-loading').show();

            fetch('../../public/Pharmacopoeia/' + filename)
                .then(response => response.text())
                .then(csvText => {
                    // 解析CSV数据
                    const lines = csvText.split('\n');
                    const headers = lines[0].split(',');
                    const data = [];

                    for (let i = 1; i < lines.length; i++) {
                        if (lines[i].trim()) {
                            const values = parseCSVLine(lines[i]);
                            if (values.length >= 5) {
                                const row = {};
                                // 跳过第一列 Accession，从第二列开始
                                row['Species'] = values[1] || '';
                                row['Chinese Name'] = values[2] || '';
                                row['Lineage'] = values[3] || '';
                                row['Pharmacopoeia Memberships'] = values[4] || '';
                                data.push(row);
                            }
                        }
                    }

                    // 更新表格数据
                    $pharmacopoeiaTable.bootstrapTable('load', data);
                    $('#pharmacopoeia-loading').hide();

                    // 更新记录数量显示
                    const activeBtnName = $('.pharmacopoeia-item.active').data('name');
                    $('#pharmacopoeia-count').text(`${activeBtnName}: ${data.length} records`).show();
                })
                .catch(error => {
                    console.error('Error loading pharmacopoeia data:', error);
                    $('#pharmacopoeia-loading').hide();
                    alert('Failed to load pharmacopoeia data. Please try again.');
                });
        }

        // 解析CSV行（处理包含逗号的字段）
        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];

                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim().replace(/^"|"$/g, ''));
                    current = '';
                } else {
                    current += char;
                }
            }

            result.push(current.trim().replace(/^"|"$/g, ''));
            return result;
        }

        // 导出药典数据为CSV
        function exportPharmacopoeiaData() {
            const data = $pharmacopoeiaTable.bootstrapTable('getData');
            if (data.length === 0) {
                alert('No data to export');
                return;
            }

            const activeBtnName = $('.pharmacopoeia-item.active').data('name');
            const headers = ['No.', 'Species', 'Chinese Name', 'Lineage', 'Pharmacopoeia Memberships'];

            // 添加UTF-8 BOM来解决中文乱码问题
            let csvContent = '\uFEFF' + headers.join(',') + '\n';

            data.forEach((row, index) => {
                const csvRow = [
                    index + 1,
                    `"${(row['Species'] || '').replace(/"/g, '""')}"`,
                    `"${(row['Chinese Name'] || '').replace(/"/g, '""')}"`,
                    `"${(row['Lineage'] || '').replace(/"/g, '""')}"`,
                    `"${(row['Pharmacopoeia Memberships'] || '').replace(/"/g, '""')}"`
                ];
                csvContent += csvRow.join(',') + '\n';
            });

            // 创建下载链接，确保使用UTF-8编码
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${activeBtnName}_pharmacopoeia_data.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 显示成功提示
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                font-size: 14px;
                font-weight: 500;
            `;
            toast.innerHTML = `<i class="fas fa-check-circle me-2"></i>CSV file exported successfully!`;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            InitMainTable();

            // 初始化药典表格
            initPharmacopoeiaTable();

            // 药典项目点击事件
            $('.pharmacopoeia-item').click(function() {
                $('.pharmacopoeia-item').removeClass('active');
                $(this).addClass('active');

                const filename = $(this).data('file');
                currentPharmacopoeiaFile = filename;
                loadPharmacopoeiaData(filename);
            });

            // 标签页切换事件
            $('#pharmacopoeia-tab').on('shown.bs.tab', function() {
                // 当切换到药典标签页时，如果还没有加载数据，则加载默认数据
                if ($pharmacopoeiaTable.bootstrapTable('getData').length === 0) {
                    loadPharmacopoeiaData(currentPharmacopoeiaFile);
                }
            });

            // 导出按钮事件
            $('#export-pharmacopoeia').click(function() {
                exportPharmacopoeiaData();
            });
        });
    </script>
</body>
</html>
