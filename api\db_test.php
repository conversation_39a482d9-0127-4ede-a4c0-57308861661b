<?php
// 设置错误显示
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 响应数组
$response = [
    'status' => 'running',
    'tests' => []
];

// 测试1: 检查PHP版本
$response['tests']['php_version'] = [
    'name' => 'PHP版本检查',
    'result' => 'success',
    'details' => 'PHP版本: ' . phpversion()
];

// 测试2: 检查必要扩展
$required_extensions = ['mysqli', 'json', 'mbstring'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

if (empty($missing_extensions)) {
    $response['tests']['extensions'] = [
        'name' => '扩展检查',
        'result' => 'success',
        'details' => '所有必要扩展已加载'
    ];
} else {
    $response['tests']['extensions'] = [
        'name' => '扩展检查',
        'result' => 'error',
        'details' => '缺少扩展: ' . implode(', ', $missing_extensions)
    ];
}

// 测试3: 检查数据库连接
try {
    // 引入数据库连接
    include __DIR__ . '/../includes/config/db_connection.php';
    
    if (!isset($conn) || $conn->connect_error) {
        throw new Exception("数据库连接失败: " . ($conn->connect_error ?? "未知错误"));
    }
    
    $response['tests']['db_connection'] = [
        'name' => '数据库连接',
        'result' => 'success',
        'details' => '成功连接到数据库: ' . $conn->host_info
    ];
    
    // 测试4: 检查表是否存在
    $table = 'browse';
    $check_table = $conn->query("SHOW TABLES LIKE '$table'");
    
    if ($check_table->num_rows > 0) {
        $response['tests']['table_check'] = [
            'name' => '表检查',
            'result' => 'success',
            'details' => "表 '$table' 存在"
        ];
        
        // 测试5: 检查数据
        $count_query = $conn->query("SELECT COUNT(*) as total FROM $table");
        $count_result = $count_query->fetch_assoc();
        $total_records = $count_result['total'];
        
        $response['tests']['data_check'] = [
            'name' => '数据检查',
            'result' => 'success',
            'details' => "表中有 $total_records 条记录"
        ];
        
        // 测试6: 执行简单查询
        $sample_query = $conn->query("SELECT * FROM $table LIMIT 1");
        
        if ($sample_query->num_rows > 0) {
            $sample_data = $sample_query->fetch_assoc();
            $response['tests']['query_test'] = [
                'name' => '查询测试',
                'result' => 'success',
                'details' => '查询成功执行',
                'sample_data' => array_slice($sample_data, 0, 3) // 只显示前3个字段
            ];
        } else {
            $response['tests']['query_test'] = [
                'name' => '查询测试',
                'result' => 'warning',
                'details' => '查询执行成功，但没有返回数据'
            ];
        }
        
    } else {
        $response['tests']['table_check'] = [
            'name' => '表检查',
            'result' => 'error',
            'details' => "表 '$table' 不存在"
        ];
    }
    
} catch (Exception $e) {
    $response['tests']['db_connection'] = [
        'name' => '数据库连接',
        'result' => 'error',
        'details' => $e->getMessage()
    ];
}

// 测试7: 检查文件权限
$api_dir = __DIR__;
$is_writable = is_writable($api_dir);
$response['tests']['file_permissions'] = [
    'name' => '文件权限检查',
    'result' => $is_writable ? 'success' : 'warning',
    'details' => $is_writable ? 'API目录可写' : 'API目录不可写，可能影响日志记录'
];

// 总结测试结果
$errors = 0;
$warnings = 0;

foreach ($response['tests'] as $test) {
    if ($test['result'] === 'error') {
        $errors++;
    } else if ($test['result'] === 'warning') {
        $warnings++;
    }
}

if ($errors > 0) {
    $response['status'] = 'error';
    $response['message'] = "测试完成，发现 $errors 个错误和 $warnings 个警告";
} else if ($warnings > 0) {
    $response['status'] = 'warning';
    $response['message'] = "测试完成，发现 $warnings 个警告";
} else {
    $response['status'] = 'success';
    $response['message'] = "所有测试通过";
}

// 输出结果
echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

// 关闭数据库连接
if (isset($conn) && $conn) {
    $conn->close();
}
?> 