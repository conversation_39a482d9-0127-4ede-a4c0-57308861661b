import pandas as pd
import os
import sys

# --- 1. 配置路径 ---
# 请确保这些路径是正确的
BASE_PATH = r"D:\Software\XAMPP\htdocs\MPRAM"
MAIN_DATA_PATH = os.path.join(BASE_PATH, "数据和脚本")
OUTPUT_DIR_BASE = os.path.join(BASE_PATH, "HRRB")

# 定义主文件的路径
MAIN_FILES = {
    'ingredient': os.path.join(MAIN_DATA_PATH, 'ingredient', 'HERB_ingredient.csv'),
    'target': os.path.join(MAIN_DATA_PATH, 'target', 'HERB_target.csv'),
    'disease': os.path.join(MAIN_DATA_PATH, 'disease', 'HERB_disease.csv')
}

# 定义主文件对应的ID列名
ID_COLUMNS = {
    'ingredient': 'Ingredient_id',
    'target': 'Target_id',
    'disease': 'Disease_id'
}

# --- 2. 加载主数据文件 ---
print("开始加载主数据文件...")

# 检查主文件是否存在
for file_type, path in MAIN_FILES.items():
    if not os.path.exists(path):
        print(f"错误：主文件 '{path}' 不存在。请检查路径是否正确。")
        sys.exit() # 退出脚本

try:
    # 使用 pandas 读取 CSV 并设置索引以便快速查找
    main_ingredient_df = pd.read_csv(MAIN_FILES['ingredient']).set_index(ID_COLUMNS['ingredient'])
    main_target_df = pd.read_csv(MAIN_FILES['target']).set_index(ID_COLUMNS['target'])
    main_disease_df = pd.read_csv(MAIN_FILES['disease']).set_index(ID_COLUMNS['disease'])
    print("主数据文件加载成功！")
except Exception as e:
    print(f"加载主数据文件时出错: {e}")
    sys.exit()

# 将主 DataFrame 存储在字典中以便于访问
MAIN_DFS = {
    'ingredients': main_ingredient_df,
    'targets': main_target_df,
    'diseases': main_disease_df
}

# --- 3. 初始化统计计数器 ---
stats = {
    "subdirectories_scanned": 0,
    "ingredients_processed": 0,
    "targets_processed": 0,
    "diseases_processed": 0,
    "errors": []
}

# --- 4. 遍历子文件夹并处理文件 ---
print("\n开始遍历子文件夹并填充数据...")

# 检查输出目录是否存在
if not os.path.isdir(OUTPUT_DIR_BASE):
    print(f"错误：目标文件夹目录 '{OUTPUT_DIR_BASE}' 不存在。")
    sys.exit()

# 遍历以中药名命名的子文件夹
for herb_folder_name in os.listdir(OUTPUT_DIR_BASE):
    herb_folder_path = os.path.join(OUTPUT_DIR_BASE, herb_folder_name)
    
    # 确保它是一个目录
    if os.path.isdir(herb_folder_path):
        stats["subdirectories_scanned"] += 1
        print(f"\n--- 正在处理文件夹: {herb_folder_name} ---")

        # 定义需要处理的文件类型
        files_to_process = {
            'ingredients': 'ingredients.csv',
            'targets': 'targets.csv',
            'diseases': 'diseases.csv'
        }

        for file_key, file_name in files_to_process.items():
            file_path = os.path.join(herb_folder_path, file_name)

            # 检查文件是否存在
            if os.path.exists(file_path):
                try:
                    # 读取子文件夹中的CSV文件
                    sub_df = pd.read_csv(file_path)

                    # 检查文件是否为空
                    if sub_df.empty:
                        print(f"  - 文件 '{file_name}' 为空，跳过。")
                        continue

                    # 获取ID列表（假设ID在第一列）
                    id_column_name = sub_df.columns[0]
                    ids_to_find = sub_df[id_column_name].dropna().unique()
                    
                    if len(ids_to_find) == 0:
                        print(f"  - 文件 '{file_name}' 中没有有效的ID，跳过。")
                        continue

                    # 从主DataFrame中查找数据
                    main_df = MAIN_DFS[file_key]
                    # 使用 .reindex() 来保持顺序并处理找不到的ID
                    # 使用 .loc[] 会在找不到ID时报错，reindex更稳健
                    filled_data = main_df.reindex(ids_to_find)

                    # 将填充好的数据写回原文件（覆盖）
                    # reset_index() 可以将索引ID恢复为普通列
                    filled_data.reset_index(inplace=True)
                    filled_data.to_csv(file_path, index=False, encoding='utf-8-sig')

                    # 更新统计计数
                    stats[f"{file_key}_processed"] += 1
                    print(f"  - 成功处理并填充文件: {file_name} ({len(filled_data)} 条记录)")

                except Exception as e:
                    error_message = f"处理文件 '{file_path}' 时发生错误: {e}"
                    print(f"  - 错误: {error_message}")
                    stats["errors"].append(error_message)
            else:
                print(f"  - 文件 '{file_name}' 不存在，跳过。")

# --- 5. 打印最终统计结果 ---
print("\n" + "="*50)
print("              所有任务已完成 - 最终统计结果")
print("="*50)
print(f"总共扫描的子文件夹数量: {stats['subdirectories_scanned']}")
print(f"成功处理的 ingredients.csv 文件数量: {stats['ingredients_processed']}")
print(f"成功处理的 targets.csv 文件数量: {stats['targets_processed']}")
print(f"成功处理的 diseases.csv 文件数量: {stats['diseases_processed']}")
print("-"*50)

if stats["errors"]:
    print(f"\n处理过程中遇到 {len(stats['errors'])} 个错误:")
    for i, err in enumerate(stats["errors"]):
        print(f"  {i+1}. {err}")
else:
    print("\n处理过程中没有发生任何错误。")

print("\n脚本执行完毕。")
