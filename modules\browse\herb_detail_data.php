<?php
header('Content-Type: application/json');

// 获取参数
$type = $_GET['type'] ?? '';
$name = $_GET['name'] ?? '';
$herb = $_GET['herb'] ?? '';

// 验证参数
if (empty($type) || empty($name) || empty($herb)) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit;
}

// 验证类型
$allowed_types = ['ingredient', 'target', 'disease'];
if (!in_array($type, $allowed_types)) {
    echo json_encode(['success' => false, 'message' => 'Invalid type parameter']);
    exit;
}

// 安全检查：防止路径遍历攻击
$herb = basename($herb);

// 构建文件路径
$herb_folder = $_SERVER['DOCUMENT_ROOT'] . '/MPRAM/HRRB/' . $herb;
$csv_files = [
    'ingredient' => $herb_folder . '/ingredients.csv',
    'target' => $herb_folder . '/targets.csv',
    'disease' => $herb_folder . '/diseases.csv'
];

$csv_file = $csv_files[$type] ?? '';

if (!file_exists($csv_file)) {
    echo json_encode(['success' => false, 'message' => 'Data file not found']);
    exit;
}

try {
    // 读取CSV文件
    $handle = fopen($csv_file, 'r');
    if (!$handle) {
        throw new Exception('Cannot open CSV file');
    }
    
    // 读取表头
    $headers = fgetcsv($handle);
    if (!$headers) {
        throw new Exception('Cannot read CSV headers');
    }
    
    // 读取数据
    $data = [];
    while (($row = fgetcsv($handle)) !== FALSE) {
        // 确保行数据与表头数量匹配
        while (count($row) < count($headers)) {
            $row[] = '';
        }
        $data[] = array_slice($row, 0, count($headers));
    }
    
    fclose($handle);
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'data' => $data,
        'headers' => $headers,
        'total' => count($data)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'Error reading data: ' . $e->getMessage()
    ]);
}
?>
