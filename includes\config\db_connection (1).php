<?php
// 允许跨域请求的头部信息
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// 数据库连接参数
$servername = "localhost";
$username = "root";
$password = "bidd@2012";
$dbname = "HRMA";

// 创建数据库连接
$conn = mysqli_connect($servername, $username, $password, $dbname);

// 检查连接是否成功
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// 设置字符集
mysqli_set_charset($conn, 'utf8');

return $conn;

?>