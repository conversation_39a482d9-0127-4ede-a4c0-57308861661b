# 导入pdas库  txt转换为csv xlsx文件脚本
import pandas as pd

# --- 根据您的截图，我们已经确定了以下参数 ---

# 1. 您提供的文件路径
file_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\target\HERB_target_info_v2 (1).txt"

# 2. 分隔符 (截图显示为TSV，即制表符 Tab)
correct_separator = '\t'

# 3. 编码格式 (截图显示为UTF-8)
correct_encoding = 'utf-8'

# -------------------------------------------------

print("参数已确定，开始转换...")

try:
    # 使用正确的参数读取您的TXT文件
    df = pd.read_csv(
        file_path, 
        sep=correct_separator,
        encoding=correct_encoding
    )
    
    print("文件读取成功，正在写入新文件...")
    
    # 定义输出文件的路径和名称 (保存在与原文件相同的目录下)
    output_csv_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\target\HERB_target.csv"
    output_excel_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\target\HERB_target.xlsx"

    # 1. 转换为CSV文件
    # 使用 'utf-8-sig' 编码可以让Windows下的Excel双击打开时正确显示中文
    df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
    print(f"✅ 成功转换为CSV文件，已保存至: {output_csv_path}")

    # 2. 转换为Excel文件
    df.to_excel(output_excel_path, index=False, sheet_name='HERB_Data')
    print(f"✅ 成功转换为Excel文件，已保存至: {output_excel_path}")

except FileNotFoundError:
    print(f"❌ 错误：找不到文件 {file_path}。请确认路径是否正确。")
except Exception as e:
    print(f"❌ 转换过程中发生未知错误: {e}")