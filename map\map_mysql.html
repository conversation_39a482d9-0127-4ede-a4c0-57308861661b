<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HRMA - 中药材根际微生物组数据库地图视图，展示全球154项研究的分布。">
    <meta name="keywords" content="中药材,微生物组,根际微生物,HRMA,数据库,地图">
    <meta name="author" content="HRMA开发团队">
    <title>HRMA - 地理分布地图</title>
    <!-- 添加favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../home/<USER>/logo.png">
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入通用样式 -->
    <link rel="stylesheet" href="../public/css/common.css">
    <!-- 引入ECharts和PapaParse库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>
    <!-- 引入自定义CSS样式 -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inknut+Antiqua:wght@400;500;600&display=swap');
        
        /* 导航栏层级控制 */
        header {
            position: relative;
            z-index: 1000 !important;
        }

        nav {
            z-index: 1000 !important;
        }

        /* 地图页面特定样式 */
        .map-container-wrapper {
            margin-top: 20px;
            padding: 2rem !important;
            width: 100%;
            position: relative;
            z-index: 1;
        }
        
        /* 地图点击样式 */
        .echarts-scatter-symbol {
            cursor: pointer !important;
        }
        
        #map-container {
            width: 100%;
            height: calc(100vh - 200px);
            min-height: 350px;
            margin: 0;
            padding: 0;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            background: white;
            overflow: hidden;
            position: relative;
            z-index: 1;
        }

        .map-controls {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: rgba(255, 255, 255, 0.8);
            padding: 8px;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1010;
        }

        .control-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 10px;
            background: white;
            color: #2C2C2C;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        .control-button:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        #loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            font-family: 'Inknut Antiqua', serif;
            color: #2C2C2C;
            backdrop-filter: blur(10px);
            z-index: 1010;
        }
        
        /* 页面标题样式 */
        .map-title {
            margin-bottom: 15px;
            text-align: center;
            padding: 0 10px;
        }
        
        .map-title h2 {
            color: #002060;
            font-weight: 600;
            font-size: 1.5rem;
        }
        
        .map-title p {
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 767px) {
            .map-controls {
                top: auto;
                transform: none;
                bottom: 70px;
                right: 10px;
                flex-direction: row;
                padding: 5px;
            }
            
            .control-button {
                width: 35px;
                height: 35px;
                font-size: 16px;
            }
            
            #map-container {
                height: calc(100vh - 230px);
                min-height: 300px;
                border-radius: 15px;
            }
            
            .map-title h2 {
                font-size: 1.3rem;
            }
            
            .map-title p {
                font-size: 0.8rem;
            }

            /* 移动端导航菜单样式调整 */
            nav.show {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: #F9FAFB;
                padding: 1rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            }
            
            #legend {
                bottom: 70px;
                left: 10px;
                top: auto;
                transform: none;
                max-width: 60%;
                max-height: 200px;
            }
        }
        
        @media (max-width: 480px) {
            .map-container-wrapper {
                margin-top: 10px;
                padding: 10px 0;
            }
            
            .logo-container {
                height: 50px;
            }
            
            #map-container {
                height: calc(100vh - 250px);
                min-height: 250px;
                border-radius: 10px;
            }
        }

        /* 图例样式 */
        #legend {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            left: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            max-width: 280px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #ccc transparent;
            backdrop-filter: blur(10px);
            z-index: 100;
        }
        
        #legend::-webkit-scrollbar {
            width: 5px;
        }
        
        #legend::-webkit-scrollbar-thumb {
            background-color: #ccc;
            border-radius: 5px;
        }
        
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #ddd;
            color: #333;
            text-align: center;
            font-size: 14px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 6px 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }
        
        .legend-item:hover {
            background-color: #f5f5f5;
            border-color: #e0e0e0;
        }
        
        .legend-item.active {
            background-color: #eaf2fd;
            border-color: #b8d4f5;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .color-box {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            border-radius: 3px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }
        
        .legend-label {
            flex-grow: 1;
            font-size: 12px;
            word-break: break-word;
            line-height: 1.3;
        }
        
        .legend-tip {
            font-style: italic;
            padding-top: 5px;
            margin-top: 5px;
            border-top: 1px dotted #ddd;
        }
    </style>
</head>
<body>
    <!-- 导航区域 -->
    <header>
        <!-- 左侧Logo和标题区域 -->
        <div class="left-content">
            <!-- Logo容器 -->
            <div class="logo-container">
                <img src="../home/<USER>/logo.png" alt="HRMA Logo" class="logo-image">
            </div>
            <!-- 标题和副标题容器 -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">HRMA</h1>
                <p class="mb-0 small">Herbal Rhizosphere Microbiome Atlas</p>
            </div>
        </div>
        <!-- 汉堡包按钮 -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- 导航链接区域 -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="../modules/browse/browse.php" class="nav-link">Browse</a></li>
                <li class="nav-item"><a href="../modules/browse/Project.php" class="nav-link">Herbs</a></li>
                <li class="nav-item"><a href="../modules/microbes/Microbes.php" class="nav-link">Microbes</a></li>
                <li class="nav-item"><a href="../modules/microbes/Network.php" class="nav-link">Network</a></li>
                <li class="nav-item"><a href="map.html" class="nav-link active">Map</a></li>
                <li class="nav-item"><a href="../help/help.html" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

    <!-- 主要内容区域 -->
    <div class="container-fluid py-3 bg-section map-container-wrapper">
        <div class="row justify-content-center">
            <div class="col-12">
                <!-- 标题区域 -->
                <div class="map-title">
                    <h2>Geographic Distribution of Herbal Studies</h2>
                    <p class="text-muted">Tips: click on dots for details</p>
                </div>
                
                <div class="map-controls">
                    <button class="control-button" onclick="zoomIn()" title="Zoom In">+</button>
                    <button class="control-button" onclick="zoomOut()" title="Zoom Out">-</button>
                    <button class="control-button" onclick="resetView()" title="Reset View">↺</button>
                    <button class="control-button" onclick="toggleMap()" id="mapToggleBtn" title="Toggle Map">🌏</button>
                </div>
                <div id="map-container"></div>
                <div id="loading">Loading map data...</div>
                <div id="legend"></div>
            </div>
        </div>
    </div>
    
    <!-- 页脚区域 -->
    <footer class="container-fluid py-3 text-center">
        <p class="small">Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
    </footer>
    
    <!-- 引入Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 汉堡包按钮控制脚本 -->
    <script>
        function toggleNav() {
            const nav = document.querySelector('nav');
            const hamburger = document.querySelector('.hamburger');
            nav.classList.toggle('show');
            hamburger.classList.toggle('active');
        }
    </script>

    <script>
        // 定义土壤处理组的颜色映射
        const soilTreatmentColors = {
            'Control': '#2E86C1',                // 蓝色
            'Wild': '#28B463',                   // 绿色
            'Cultivated': '#F39C12',             // 橙色
            'Mixed planting': '#8E44AD',         // 紫色
            'Monoculture': '#E74C3C',            // 红色
            'Organic': '#1ABC9C',                // 青绿色
            'Fertilized': '#D35400',             // 棕色
            'No fertilizer': '#2980B9',          // 深蓝色
            'Traditional': '#27AE60',            // 深绿色
            'With cover': '#F1C40F',             // 黄色
            'No cover': '#9B59B6',               // 亮紫色
            'Disease and Health': '#8A2BE2',     // 紫罗兰色
            'Microbial Inoculant Inoculation': '#FF4500', // 橙红色
            'Disease and Health；Microbial Inoculant Inoculation': '#8A2BE2', // 与Disease and Health使用相同颜色
            'Ecological Restoration': '#3498DB', // 湖蓝色
            'Conservation': '#16A085',           // 茶绿色
            'Ancient Trees': '#9B59B6',          // 紫色
            'Pest Control': '#E67E22',           // 深橙色
            'Soil Amendment': '#C0392B',         // 暗红色
            '未知': '#95A5A6'                     // 灰色 - 用于没有指定处理组的情况
        };
        
        // 备用颜色，用于没有预定义颜色的处理组
        const fallbackColors = [
            '#1F618D', '#117A65', '#A04000', '#7D3C98', '#145A32', 
            '#B03A2E', '#2C3E50', '#7E5109', '#0E6655', '#633974',
            '#5499C7', '#58D68D', '#EB984E', '#BB8FCE', '#48C9B0'
        ];
        
        // 已使用的备用颜色索引
        let fallbackColorIndex = 0;
        
        // 存储动态分配的颜色
        const dynamicColorMap = {};
        
        // 获取土壤处理组颜色的函数，确保总是能返回一个颜色
        function getTreatmentColor(treatment) {
            if (!treatment) return '#95A5A6'; // 默认灰色
            
            // 检查是否已有预定义颜色
            if (soilTreatmentColors[treatment]) {
                return soilTreatmentColors[treatment];
            }
            
            // 检查是否已经动态分配过颜色
            if (dynamicColorMap[treatment]) {
                return dynamicColorMap[treatment];
            }
            
            // 如果没有完全匹配，检查是否包含已知处理组名称
            for (const knownTreatment in soilTreatmentColors) {
                if (knownTreatment !== '未知' && treatment.includes(knownTreatment)) {
                    console.log(`匹配到部分处理组: ${treatment} -> 使用 ${knownTreatment} 的颜色`);
                    dynamicColorMap[treatment] = soilTreatmentColors[knownTreatment];
                    return soilTreatmentColors[knownTreatment];
                }
            }
            
            // 如果都没匹配到，分配一个备用颜色
            const fallbackColor = fallbackColors[fallbackColorIndex % fallbackColors.length];
            fallbackColorIndex++;
            
            // 记录新分配的颜色
            console.log(`为未知处理组分配颜色: ${treatment} -> ${fallbackColor}`);
            dynamicColorMap[treatment] = fallbackColor;
            
            return fallbackColor;
        }
        
        // 处理土壤处理组数据
        function processTreatmentsData(data) {
            const treatmentData = {};
            if (data.treatments && Array.isArray(data.treatments)) {
                // 记录所有独特的处理组
                const uniqueTreatments = new Set();
                
                data.treatments.forEach(row => {
                    if (row.study_id) {
                        // 保存原始土壤处理组名称
                        const treatment = row.soil_treatment_groups || '未知';
                        
                        uniqueTreatments.add(treatment);
                        
                        treatmentData[row.study_id] = {
                            treatment: treatment,
                            latinName: row.latin_name || '',
                            chineseName: row.chinese_name || ''
                        };
                    }
                });
                
                // 记录所有独特的处理组
                console.log('独特的土壤处理组:', Array.from(uniqueTreatments));
                
                // 为每个独特的处理组预先分配颜色
                uniqueTreatments.forEach(treatment => {
                    getTreatmentColor(treatment);
                });
            }
            
            console.log(`共加载 ${Object.keys(treatmentData).length} 条土壤处理组数据`);
            return treatmentData;
        }
        
        let myChart = null;
        let geoData = null;
        let sampleData = null;
        let effectData = null;
        let treatmentData = null;
        let currentZoom = 1;
        let currentMap = 'world';
        let chinaGeoJson = null;

        // Initialize ECharts instance
        function initChart() {
            if (myChart !== null) {
                myChart.dispose();
            }
            myChart = echarts.init(document.getElementById('map-container'), null, {locale: 'en', renderer: 'canvas'});
            myChart.showLoading({
                text: 'Loading map data...',
                color: '#4CAF50',
                textColor: '#333',
                maskColor: 'rgba(255, 255, 255, 0.8)',
                zlevel: 0
            });
        }

        // Toggle map type
        function toggleMap() {
            currentMap = currentMap === 'world' ? 'china' : 'world';
            const btn = document.getElementById('mapToggleBtn');
            btn.textContent = currentMap === 'world' ? '🌏' : '🇨🇳';
            updateMap();
        }

        // Zoom controls
        function zoomIn() {
            if (!myChart) return;
            currentZoom = Math.min(currentZoom * 1.5, 8);
            myChart.setOption({
                geo: {
                    zoom: currentZoom
                }
            });
        }

        function zoomOut() {
            if (!myChart) return;
            currentZoom = Math.max(currentZoom / 1.5, 0.5);
            myChart.setOption({
                geo: {
                    zoom: currentZoom
                }
            });
        }

        function resetView() {
            if (!myChart) return;
            currentZoom = 1;
            const center = currentMap === 'world' ? [0, 30] : [104, 35];
            myChart.setOption({
                geo: {
                    zoom: currentZoom,
                    center: center
                }
            });
        }

        // Load geographic data
        async function loadGeoData() {
            try {
                const response = await fetch('./world-countries-merged.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                
                if (!data.type || !data.features) {
                    throw new Error('Map data format is incorrect');
                }
                
                return data;
            } catch (error) {
                console.error('Failed to load world map data:', error);
                document.getElementById('loading').textContent = 'Failed to load the map. Please make sure world-countries-merged.json file exists and is formatted correctly.';
                document.getElementById('loading').style.color = 'red';
                return null;
            }
        }

        // Load China map data
        async function loadChinaMap() {
            try {
                const response = await fetch('./china-map.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Failed to load China map:', error);
                document.getElementById('loading').textContent = 'Failed to load China map. Please check your network connection.';
                document.getElementById('loading').style.color = 'red';
                return null;
            }
        }

        // Load sample data
        async function loadSampleData() {
            try {
                // 修改 API 路径为绝对路径
                const response = await fetch('../api/get_map_data.php');
                
                if (!response.ok) {
                    throw new Error(`API响应错误: ${response.status} - ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API返回的数据:', data);
                
                if (!data || (data.error && typeof data.error === 'string')) {
                    throw new Error(data.error || '无效的数据格式');
                }
                
                // 处理功效数据
                effectData = {};
                if (data.effect && Array.isArray(data.effect)) {
                    data.effect.forEach(row => {
                        if (row.study_id) {
                            effectData[row.study_id] = {
                                category: row.effect_category || '其他',
                                studyName: row.latin_name,
                                chineseName: row.chinese_name
                            };
                        }
                    });
                }
                console.log(`共加载 ${Object.keys(effectData).length} 条功效数据`);
                
                // 处理土壤处理组数据
                treatmentData = processTreatmentsData(data);
                
                // 处理地理数据
                const locationMap = new Map();
                let geoMatchCount = 0;
                let treatmentMatchCount = 0;
                
                // 遍历每条地理位置数据
                if (data.geo && Array.isArray(data.geo)) {
                    data.geo.forEach(item => {
                        let studyId = item.study_id;
                        if (!studyId) return;
                        
                        let lat, lng;
                        let locationKey;
                        
                        // 获取坐标
                        if (!item.latitude || !item.longitude || 
                            item.latitude === 'n.a.' || item.longitude === 'n.a.' ||
                            !parseFloat(item.latitude) || !parseFloat(item.longitude)) {
                            // 使用位置名称查找坐标
                            const coords = getLocationCoordinates(item.province, item.country);
                            if (!coords) return;
                            lat = coords.lat;
                            lng = coords.lng;
                            locationKey = `${item.province},${item.country}`;
                        } else {
                            lat = parseFloat(item.latitude);
                            lng = parseFloat(item.longitude);
                            if (isNaN(lat) || isNaN(lng)) return;
                            locationKey = `${lat},${lng}`;
                        }
                        
                        // 获取功效信息
                        const effect = effectData[studyId];
                        if (effect) geoMatchCount++;
                        
                        // 获取土壤处理组信息
                        const treatment = treatmentData[studyId];
                        if (treatment) treatmentMatchCount++;
                        
                        // 聚合位置数据
                        if (!locationMap.has(locationKey)) {
                            locationMap.set(locationKey, {
                                value: [lng, lat],
                                location: `${item.province}, ${item.country}`,
                                categories: new Map(),
                                treatments: new Map(),
                                samples: new Set(),
                                studyIds: new Set(),
                                studyDetails: new Map()
                            });
                        }
                        
                        const locationData = locationMap.get(locationKey);
                        
                        // 更新功效类别计数
                        const category = effect ? effect.category : '其他';
                        if (!locationData.categories.has(category)) {
                            locationData.categories.set(category, new Set());
                        }
                        locationData.categories.get(category).add(studyId);
                        
                        // 更新土壤处理组计数
                        const soilTreatment = treatment ? treatment.treatment : '未知';
                        if (!locationData.treatments.has(soilTreatment)) {
                            locationData.treatments.set(soilTreatment, new Set());
                        }
                        locationData.treatments.get(soilTreatment).add(studyId);
                        
                        locationData.samples.add(item.sample_name || '');
                        locationData.studyIds.add(studyId);
                        
                        // 保存研究详情
                        if (!locationData.studyDetails.has(studyId)) {
                            locationData.studyDetails.set(studyId, {
                                studyName: (effect ? effect.studyName : '') || (treatment ? treatment.latinName : ''),
                                chineseName: (effect ? effect.chineseName : '') || (treatment ? treatment.chineseName : ''),
                                treatment: soilTreatment
                            });
                        }
                    });
                }
                
                console.log(`地理数据匹配: 功效=${geoMatchCount}, 土壤处理组=${treatmentMatchCount}`);
                
                // 将Map转换为数组格式
                return Array.from(locationMap.entries()).map(([key, location]) => {
                    let mainTreatment = '未知';
                    let maxCount = 0;
                    
                    location.treatments.forEach((studies, treatment) => {
                        if (studies.size > maxCount) {
                            maxCount = studies.size;
                            mainTreatment = treatment;
                        }
                    });
                    
                    const studyIds = Array.from(location.studyIds);
                    const studyDetails = [];
                    studyIds.forEach(id => {
                        if (location.studyDetails.has(id)) {
                            studyDetails.push(location.studyDetails.get(id));
                        }
                    });
                    
                    const firstValidDetail = studyDetails.find(detail => detail && (detail.studyName || detail.chineseName)) || {};
                    
                    return {
                        value: location.value,
                        location: location.location,
                        treatment: mainTreatment,
                        studyCount: location.studyIds.size,
                        sampleCount: location.samples.size,
                        studyIds: studyIds,
                        studyName: firstValidDetail.studyName || '',
                        chineseName: firstValidDetail.chineseName || '',
                        studyDetails: studyDetails,
                        tooltip: function() {
                            let displayIds = studyIds;
                            let idText = displayIds.join(', ');
                            if (displayIds.length > 3) {
                                idText = displayIds.slice(0, 3).join(', ') + ` and ${displayIds.length - 3} more`;
                            }
                            
                            const treatments = Array.from(location.treatments.keys()).filter(t => t !== '未知');
                            let treatmentText = treatments.join(', ');
                            if (treatments.length > 2) {
                                treatmentText = treatments.slice(0, 2).join(', ') + ` and ${treatments.length - 2} more`;
                            }
                            if (treatments.length === 0) treatmentText = 'Unknown';
                            
                            const detail = firstValidDetail;
                            
                            return `Study ID: ${idText}<br>` +
                                   `Sample Count: ${location.samples.size}<br>` +
                                   `Latin Name: ${detail.studyName || 'Unknown'}<br>` +
                                   `Chinese Name: ${detail.chineseName || 'Unknown'}<br>` +
                                   `Soil Treatment: ${treatmentText}<br>` +
                                   `Coordinates: ${location.value[1].toFixed(2)}°, ${location.value[0].toFixed(2)}°`;
                        }
                    };
                });
            } catch (error) {
                console.error('加载样本数据失败:', error);
                console.group('数据加载错误详情');
                console.error('错误信息:', error.message);
                console.error('错误堆栈:', error.stack);
                console.groupEnd();
                
                // 显示用户友好的错误信息
                document.getElementById('loading').innerHTML = `
                    <div style="color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; padding: 1rem; border-radius: 0.25rem;">
                        <h4 style="margin-top: 0;">数据加载失败</h4>
                        <p style="margin-bottom: 0.5rem;">抱歉，加载地图数据时出现错误。</p>
                        <small>技术详情: ${error.message}</small>
                        <div style="margin-top: 1rem;">
                            <button onclick="location.reload()" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-sync-alt"></i> 重试
                            </button>
                        </div>
                    </div>
                `;
                
                return null;
            }
        }

        /**
         * 根据省份和国家获取地理坐标
         * 当经纬度数据不可用时，从预定义的位置表中查找坐标
         * @param {string} province - 省份名称
         * @param {string} country - 国家名称
         * @returns {Object|null} 包含lat和lng属性的对象或null（未找到时）
         */
        function getLocationCoordinates(province, country) {
            // 位置查找表 - 常用地点的经纬度映射
            const locationTable = {
                'Beijing,China': { lat: 39.9042, lng: 116.4074 },
                'Shanghai,China': { lat: 31.2304, lng: 121.4737 },
                'Guangdong,China': { lat: 23.3790, lng: 113.7633 },
                'Hubei,China': { lat: 30.7378, lng: 112.2384 },
                'Yunnan,China': { lat: 24.4753, lng: 101.3431 },
                'Jilin,China': { lat: 43.6661, lng: 126.1923 },
                'Sichuan,China': { lat: 30.6171, lng: 102.7103 },
                'Heilongjiang,China': { lat: 47.1216, lng: 128.1348 },
                'Liaoning,China': { lat: 41.9437, lng: 122.5297 },
                'Shandong,China': { lat: 36.1689, lng: 118.7402 },
                'Ningxia,China': { lat: 37.2692, lng: 106.1655 },
                'NingXia,China': { lat: 37.2692, lng: 106.1655 },
                '宁夏,China': { lat: 37.2692, lng: 106.1655 },
                'HeiBei,China': { lat: 39.3054, lng: 116.7107 },
                'Shanxi,China': { lat: 37.2426, lng: 111.8569 },
                'Qinghai,China': { lat: 35.7452, lng: 96.4077 },
                'Tibet,China': { lat: 30.1534, lng: 88.7879 },
                'Anhui,China': { lat: 31.8612, lng: 117.2866 },
                'Gansu,China': { lat: 36.0611, lng: 103.8343 },
                'Zhejiang,China': { lat: 29.1416, lng: 119.7889 },
                'Fujian,China': { lat: 26.0789, lng: 117.9874 },
                'Jiangsu,China': { lat: 32.9711, lng: 119.4550 },
                'Jiangxi,China': { lat: 27.6140, lng: 115.7221 },
                'Hunan,China': { lat: 27.6253, lng: 111.8569 },
                'Guizhou,China': { lat: 26.8430, lng: 107.2903 },
                'Guangxi,China': { lat: 23.7248, lng: 108.8076 },
                'Hainan,China': { lat: 19.5664, lng: 109.9497 },
                'Chongqing,China': { lat: 29.5630, lng: 106.5516 },
                'Shaanxi,China': { lat: 35.3601, lng: 108.9286 },
                'Tianjin,China': { lat: 39.3434, lng: 117.3616 },
                'Inner Mongolia,China': { lat: 44.0935, lng: 113.9448 },
                'Xinjiang,China': { lat: 42.5246, lng: 87.5396 },
                'Hong Kong,China': { lat: 22.3193, lng: 114.1694 },
                'Macau,China': { lat: 22.1987, lng: 113.5439 },
                'Taiwan,China': { lat: 23.6978, lng: 120.9605 },
                
                // 国际位置
                'Madrid,Spain': { lat: 40.4168, lng: -3.7038 }
            };
            
            const key = `${province},${country}`;
            
            // 首先尝试精确匹配
            if (locationTable[key]) {
                return locationTable[key];
            }
            
            // 国家级别的默认位置
            if (country === 'Spain') {
                return { lat: 40.4168, lng: -3.7038 }; // 默认西班牙使用马德里的坐标
            }
            
            return null; // 无法找到匹配的位置
        }

        /**
         * 解析纬度字符串为小数格式
         * 支持多种格式的纬度字符串解析
         * @param {string} latStr - 纬度字符串
         * @returns {number} 解析后的小数格式纬度值
         */
        function parseLatitude(latStr) {
            if (!latStr || latStr === 'n.a.') return 0;
            return parseFloat(latStr);
        }

        /**
         * 解析经度字符串为小数格式
         * 支持多种格式的经度字符串解析
         * @param {string} lngStr - 经度字符串
         * @returns {number} 解析后的小数格式经度值
         */
        function parseLongitude(lngStr) {
            if (!lngStr || lngStr === 'n.a.') return 0;
            return parseFloat(lngStr);
        }

        // 更新地图
        function updateMap() {
            const loading = document.getElementById('loading');
            
            // 检查必要数据是否已加载
            if (!geoData || !sampleData) {
                loading.textContent = 'Data missing. Cannot update map.';
                loading.style.color = 'red';
                return;
            }
            
            // 注册地图数据到ECharts
            if (currentMap === 'world') {
                // 世界地图模式
                echarts.registerMap('world', geoData);
                completeMapUpdate();
            } else {
                // 中国地图模式
                if (!chinaGeoJson) {
                    // 如果中国地图数据未加载，先加载
                    loading.textContent = 'Loading China map...';
                    loading.style.display = 'block';
                    loadChinaMap().then(data => {
                        chinaGeoJson = data;
                        if (chinaGeoJson) {
                            echarts.registerMap('china', chinaGeoJson);
                            completeMapUpdate();
                        }
                    });
                    return;
                }
                echarts.registerMap('china', chinaGeoJson);
                completeMapUpdate();
            }
        }

        // 完成地图更新
        function completeMapUpdate() {
            if (!myChart || !sampleData) return;
            
            myChart.showLoading({
                text: 'Updating map data...',
                color: '#4CAF50',
                textColor: '#333',
                maskColor: 'rgba(255, 255, 255, 0.8)'
            });

            // 分组数据按土壤处理组而非功效类别
            const treatmentGroups = {};
            sampleData.forEach(item => {
                const treatment = item.treatment || '未知';
                if (!treatmentGroups[treatment]) {
                    treatmentGroups[treatment] = [];
                }
                treatmentGroups[treatment].push(item);
            });
            
            // 为每个土壤处理组创建一个系列
            const series = Object.entries(treatmentGroups).map(([treatment, points]) => {
                // 根据当前地图类型过滤数据
                const filteredPoints = currentMap === 'china' 
                    ? points.filter(point => point.location && point.location.includes('China'))
                    : points;
                
                // 过滤出有效的点
                const validPoints = filteredPoints.filter(p => 
                    Array.isArray(p.value) && p.value.length >= 2 && 
                    !isNaN(p.value[0]) && !isNaN(p.value[1])
                );
                
                // 只返回有数据的系列
                if (validPoints.length === 0) return null;
                
                return {
                    name: treatment,
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    data: validPoints.map(point => ({
                        value: [...point.value, point.sampleCount],  // 值包含经度、纬度和样本数量
                        studyIds: point.studyIds,
                        studyName: point.studyName,
                        chineseName: point.chineseName,
                        tooltip: function() {
                            // 如果有多个研究ID，只显示前3个，其余用"等"表示
                            let displayIds = point.studyIds;
                            let idText = displayIds.join(', ');
                            if (displayIds.length > 3) {
                                idText = displayIds.slice(0, 3).join(', ') + ` and ${displayIds.length - 3} more`;
                            }
                            
                            // 获取土壤处理组信息
                            let treatmentText = treatment;
                            if (treatment === '未知') treatmentText = 'Unknown';
                            
                            return `Study ID: ${idText}<br>` +
                                   `Sample Count: ${point.sampleCount}<br>` +
                                   `Latin Name: ${point.studyName || 'Unknown'}<br>` +
                                   `Chinese Name: ${point.chineseName || 'Unknown'}<br>` +
                                   `Soil Treatment: ${treatmentText}<br>` +
                                   `Coordinates: ${point.value[1].toFixed(2)}°, ${point.value[0].toFixed(2)}°`;
                        }
                    })),
                    // 气泡大小 - 根据样本数量调整
                    symbolSize: function(val) {
                        const sampleCount = val[2] || 1;
                        // 最小6px，最大15px，根据样本数量的平方根比例放大
                        return Math.min(6 + Math.sqrt(sampleCount) * 2, 15);
                    },
                    // 气泡样式
                    itemStyle: {
                        color: getTreatmentColor(treatment),  // 根据土壤处理组设置颜色
                        borderColor: '#fff',
                        borderWidth: 1.5,
                        opacity: 0.8,
                        shadowBlur: 5,
                        shadowColor: 'rgba(0, 0, 0, 0.3)'
                    },
                    // 鼠标悬停样式
                    emphasis: {
                        itemStyle: {
                            borderWidth: 2.5,
                            borderColor: '#333',
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        },
                        label: {
                            show: true,
                            formatter: function(params) {
                                return 'Click for details';
                            },
                            position: 'top',
                            backgroundColor: 'rgba(50,50,50,0.8)',
                            padding: [4, 8],
                            borderRadius: 4,
                            color: '#fff',
                            fontSize: 12
                        }
                    }
                };
            }).filter(Boolean); // 过滤掉null值

            // 配置ECharts选项
            const option = {
                backgroundColor: '#fff',
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (typeof params.data.tooltip === 'function') {
                            return params.data.tooltip();
                        }
                        return params.data.tooltip;
                    }
                },
                geo: {
                    map: currentMap,
                    roam: true,  // 允许缩放和平移
                    zoom: currentZoom,
                    center: currentMap === 'world' ? [0, 30] : [104, 35],
                    scaleLimit: {
                        min: 0.5,
                        max: 8
                    },
                    itemStyle: {
                        areaColor: '#e8f4f8',
                        borderColor: '#a8d5e5',
                        borderWidth: 0.5
                    },
                    emphasis: {
                        itemStyle: {
                            areaColor: '#bfe6e6'
                        }
                    }
                },
                series: series
            };

            // 应用配置并隐藏加载指示器
            myChart.setOption(option, true);
            myChart.hideLoading();
            document.getElementById('loading').style.display = 'none';
            
            // 创建自定义图例
            createLegend(treatmentGroups);
            
            // 添加点击事件，实现跳转到Study.php页面
            myChart.off('click');
            myChart.on('click', function(params) {
                if (params.componentType === 'series' && params.data && params.data.studyIds) {
                    // 获取第一个研究ID作为默认跳转ID
                    const studyId = params.data.studyIds[0];
                    if (studyId) {
                        console.log('点击了地图点，准备跳转到Study.php，study_id=', studyId);
                        console.log('所有可用的studyIds=', params.data.studyIds);
                        
                        // 如果只有一个研究ID，直接跳转
                        if (params.data.studyIds.length === 1) {
                            // 使用studyId参数正确跳转到Study.php
                            const url = `../modules/browse/Study.php?study_id=${encodeURIComponent(studyId)}`;
                            console.log('跳转到:', url);
                            // 尝试在新窗口打开
                            window.open(url, '_blank');
                        } else {
                            // 如果有多个研究ID，弹出选择框
                            const studyIds = params.data.studyIds;
                            const container = document.createElement('div');
                            container.style.position = 'fixed';
                            container.style.top = '50%';
                            container.style.left = '50%';
                            container.style.transform = 'translate(-50%, -50%)';
                            container.style.background = 'white';
                            container.style.padding = '20px';
                            container.style.borderRadius = '10px';
                            container.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
                            container.style.zIndex = '2000';
                            container.style.maxWidth = '90%';
                            container.style.maxHeight = '80vh';
                            container.style.overflow = 'auto';
                            
                            const title = document.createElement('h4');
                            title.textContent = '选择要查看的研究';
                            title.style.marginBottom = '15px';
                            title.style.borderBottom = '1px solid #eee';
                            title.style.paddingBottom = '10px';
                            container.appendChild(title);
                            
                            const list = document.createElement('div');
                            list.style.display = 'flex';
                            list.style.flexDirection = 'column';
                            list.style.gap = '10px';
                            
                            studyIds.forEach(id => {
                                const item = document.createElement('a');
                                item.href = `../modules/browse/Study.php?study_id=${encodeURIComponent(id)}`;
                                item.textContent = `Study ID: ${id}`;
                                item.target = "_blank";
                                item.style.padding = '8px 15px';
                                item.style.background = '#f5f5f5';
                                item.style.borderRadius = '5px';
                                item.style.textDecoration = 'none';
                                item.style.color = '#333';
                                item.style.fontWeight = 'normal';
                                item.style.display = 'block';
                                
                                item.onmouseover = function() {
                                    this.style.background = '#e9e9e9';
                                };
                                
                                item.onmouseout = function() {
                                    this.style.background = '#f5f5f5';
                                };
                                
                                list.appendChild(item);
                            });
                            
                            container.appendChild(list);
                            
                            const closeBtn = document.createElement('button');
                            closeBtn.textContent = '关闭';
                            closeBtn.style.marginTop = '15px';
                            closeBtn.style.padding = '8px 15px';
                            closeBtn.style.background = '#ddd';
                            closeBtn.style.border = 'none';
                            closeBtn.style.borderRadius = '5px';
                            closeBtn.style.cursor = 'pointer';
                            
                            closeBtn.onclick = function() {
                                document.body.removeChild(container);
                                document.body.removeChild(overlay);
                            };
                            
                            container.appendChild(closeBtn);
                            
                            // 创建一个半透明遮罩
                            const overlay = document.createElement('div');
                            overlay.style.position = 'fixed';
                            overlay.style.top = '0';
                            overlay.style.left = '0';
                            overlay.style.width = '100%';
                            overlay.style.height = '100%';
                            overlay.style.background = 'rgba(0, 0, 0, 0.5)';
                            overlay.style.zIndex = '1999';
                            
                            overlay.onclick = function() {
                                document.body.removeChild(container);
                                document.body.removeChild(overlay);
                            };
                            
                            document.body.appendChild(overlay);
                            document.body.appendChild(container);
                        }
                    }
                }
            });
        }
        
        // 创建图例
        function createLegend(treatmentGroups) {
            const legendDiv = document.getElementById('legend');
            if (!legendDiv) return;
            
            legendDiv.innerHTML = '';
            
            // 标题
            const titleDiv = document.createElement('div');
            titleDiv.className = 'legend-title';
            titleDiv.textContent = 'Soil Treatment Groups';
            legendDiv.appendChild(titleDiv);
            
            // 获取所有有效的土壤处理组（有点的才显示）
            const validTreatments = Object.keys(treatmentGroups).filter(t => 
                treatmentGroups[t] && treatmentGroups[t].length > 0
            );
            
            console.log('有效的土壤处理组:', validTreatments);
            console.log('所有动态分配的颜色:', dynamicColorMap);
            
            // 按照点的数量排序
            validTreatments.sort((a, b) => 
                treatmentGroups[b].length - treatmentGroups[a].length
            );
            
            // 创建图例项
            validTreatments.forEach(treatment => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'legend-item';
                itemDiv.setAttribute('data-treatment', treatment);
                
                const colorBox = document.createElement('span');
                colorBox.className = 'color-box';
                const color = getTreatmentColor(treatment);
                colorBox.style.backgroundColor = color;
                
                const labelSpan = document.createElement('span');
                labelSpan.className = 'legend-label';
                labelSpan.textContent = `${treatment === '未知' ? 'Unknown' : treatment} (${treatmentGroups[treatment].length})`;
                
                // 在控制台记录每个图例项的颜色
                console.log(`图例项: ${treatment}, 颜色: ${color}, 数量: ${treatmentGroups[treatment].length}`);
                
                // 添加点击事件，点击时聚焦到该类别
                itemDiv.onclick = function() {
                    const clickedTreatment = this.getAttribute('data-treatment');
                    console.log(`点击了图例项: ${clickedTreatment}`);
                    
                    // 修改当前所有系列的透明度为0.2
                    const option = myChart.getOption();
                    option.series.forEach((series, index) => {
                        if (series.name === clickedTreatment) {
                            // 聚焦当前类别，透明度为1
                            option.series[index].itemStyle.opacity = 1;
                            console.log(`聚焦系列: ${series.name}`);
                        } else {
                            // 其他类别透明度降低
                            option.series[index].itemStyle.opacity = 0.2;
                        }
                    });
                    myChart.setOption(option);
                    
                    // 标记当前选中的图例项
                    document.querySelectorAll('.legend-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    this.classList.add('active');
                };
                
                // 双击事件，重置所有系列的透明度
                itemDiv.ondblclick = function() {
                    console.log('双击重置图例');
                    
                    const resetOption = myChart.getOption();
                    resetOption.series.forEach((series, index) => {
                        resetOption.series[index].itemStyle.opacity = 0.8;
                    });
                    myChart.setOption(resetOption);
                    
                    // 移除所有选中状态
                    document.querySelectorAll('.legend-item').forEach(item => {
                        item.classList.remove('active');
                    });
                };
                
                // 添加鼠标样式，表明可以点击
                itemDiv.style.cursor = 'pointer';
                
                itemDiv.appendChild(colorBox);
                itemDiv.appendChild(labelSpan);
                legendDiv.appendChild(itemDiv);
            });
            
            // 添加提示信息
            const tipDiv = document.createElement('div');
            tipDiv.className = 'legend-tip';
            tipDiv.textContent = 'Click: Focus | Double-click: Reset';
            tipDiv.style.fontSize = '11px';
            tipDiv.style.marginTop = '10px';
            tipDiv.style.color = '#777';
            tipDiv.style.textAlign = 'center';
            
            legendDiv.appendChild(tipDiv);
        }

        /**
         * 处理窗口大小变化
         * 当浏览器窗口大小改变时，调整地图大小
         */
        window.addEventListener('resize', function() {
            if (myChart) {
                myChart.resize();
            }
        });

        /**
         * 初始化应用
         * 加载所需数据并设置地图
         */
        async function init() {
            initChart();

            try {
                // 并行加载数据
                const [geoDataResult, sampleDataResult] = await Promise.all([
                    loadGeoData(),
                    loadSampleData()
                ]);
                
                geoData = geoDataResult;
                sampleData = sampleDataResult;
                
                if (!geoData) {
                    throw new Error('Failed to load geographic data');
                }
                
                if (!sampleData || sampleData.length === 0) {
                    throw new Error('No sample data found');
                }
                
                console.log(`加载了 ${sampleData.length} 条样本数据`);
                
                // 更新地图显示
                updateMap();
                
                // 添加窗口大小变化时的自适应
                window.addEventListener('resize', () => {
                    if (myChart) {
                        myChart.resize();
                    }
                });
                
            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('loading').innerHTML = 
                    `<span style="color:red">Error: ${error.message}</span>`;
            }
        }

        // 当页面加载完成后执行init函数
        document.addEventListener('DOMContentLoaded', init);

        // 根据当前缩放级别和地图类型获取地图中心点
        function getMapCenterByZoom() {
            return currentMap === 'world' ? [0, 30] : [104, 35];
        }
    </script>
</body>
</html> 