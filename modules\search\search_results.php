<?php
include __DIR__ . '/../../includes/config/db_connection.php';

// 设置字符集为 utf8mb4
$conn->set_charset("utf8mb4");

// 接收搜索参数
$search_by = isset($_POST['search_by']) ? $_POST['search_by'] : 'Study Name';
$search_query = isset($_POST['search_query']) ? trim($_POST['search_query']) : '';

// 字段映射
$field_map = [
    'Study ID' => 'Study ID',
    'Study Name' => 'Study Name', 
    'Chinese Name' => 'Chinese Name',
    'English Name' => 'English Name',
    'Studied microbes' => 'Studied microbes',
    'Soil Treatment Groups' => 'Soil Treatment Groups',
    'Cultivation Practice' => 'Cultivation Practice',
    'Property Flavor' => 'Property Flavor   ',
    'Channel Tropism' => 'Channel Tropism',
    'Effect' => 'Effect',
    'Effect Category' => 'Effect Category'
];

// 确保 search_by 的值是有效的
if (!array_key_exists($search_by, $field_map)) {
    die("无效的搜索字段");
}

// 检查搜索内容
if (empty($search_query)) {
    die("无效的搜索内容，请输入有效的搜索内容。");
}

// 构建查询
$sql = "SELECT * FROM browse WHERE `{$field_map[$search_by]}` LIKE ?"; // 确保表名为 browse
$stmt = $conn->prepare($sql);

// 错误处理
if (!$stmt) {
    die("查询准备失败: " . $conn->error);
}

$search_term = "%$search_query%";
$stmt->bind_param("s", $search_term);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/style.css">
    <style>
        body {
            background-color: #f8f9fa; /* 背景颜色 */
        }
        .result-container {
            margin-top: 70px; /* 确保内容不被固定导航栏遮挡 */
            padding: 20px;
            background-color: #ffffff; /* 白色背景 */
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h2 {
            color: #343a40; /* 深色标题 */
        }
        .alert {
            margin-top: 20px;
        }
        .table {
            margin-top: 20px;
        }

        /* 导航栏样式 */
        .navbar {
            background: #f8f9fa !important;
            box-shadow: none;
            padding: 0;
            height: 68px;
            min-height: 68px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e5e5e5;
        }

        .navbar > .container {
            height: 100%;
            display: flex;
            align-items: center;
            padding: 0 0px;
        }

        .navbar-brand {
            color: #5DC4A7 !important;  /* 更深的绿色 */
            font-weight: normal;
            padding: 0;
            height: 100%;
            display: flex;
            align-items: center;
            font-size: 1.2rem;
            margin: 0;
            font-family: 'Comic Sans MS', 'Chalkboard SE', 'Marker Felt', sans-serif;
            letter-spacing: 0.5px;
        }

        .navbar-brand:hover {
            color: #4BA88D !important;  /* 悬停时的颜色也相应调深 */
        }

        .navbar-brand img {
            height: 32px;
            width: auto;
            margin-right: 10px;
        }

        .navbar-nav {
            height: 100%;
            display: flex;
            align-items: stretch;  /* 改为stretch以使子元素填充高度 */
            margin: 0;
            padding: 0;
        }

        .navbar-nav .nav-item {
            height: 100%;
            display: flex;
            align-items: center;
            padding: 0;
            margin: 0;
        }

        .navbar-nav .nav-link {
            color: #666 !important;
            padding: 13px 15px !important;  /* 使用固定的上下内边距 */
            font-size: 1rem;
            transition: all 0.2s;
            height: 100%;  /* 使用100%高度 */
            display: flex;
            align-items: center;
            margin: 0;
            position: relative;
            border-radius: 8px;
        }

        .navbar-nav .nav-link:hover {
            color: #333 !important;
            background-color: #f0f0f0;
        }

        .navbar-nav .nav-link.active {
            color: #333 !important;
            background-color: #e9ecef;
        }

        .navbar-toggler {
            border: 1px solid #ddd !important;
            height: 36px;
            width: 36px;
            padding: 0;
            margin: 16px 0;  /* 添加上下margin以居中 */
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
        }

        .navbar-toggler-icon {
            width: 20px;
            height: 20px;
        }

        @media (max-width: 991.98px) {
            .navbar-nav {
                height: auto;
                padding: 5px 0;
            }
            
            .navbar-nav .nav-item {
                height: auto;
            }
            
            .navbar-nav .nav-link {
                height: 42px;
                margin: 2px 0;
                padding: 0 15px !important;
            }

            .navbar-collapse {
                position: absolute;
                top: 68px;
                left: 0;
                right: 0;
                background-color: #f8f9fa;
                border-bottom: 1px solid #e5e5e5;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../../public/index.php">
                <img src="../../public/img/logo.png" alt="1" width="30" height="24">
                Herbal Rhizosphere Microbiome Database
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="../../public/index.php">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="../browse/Project.php">Project</a></li>
                    <li class="nav-item"><a class="nav-link" href="../sample/Sample.php">Sample</a></li>
                    <li class="nav-item"><a class="nav-link" href="../microbes/Microbes.php">Microbes</a></li>
                    <li class="nav-item"><a class="nav-link" href="#">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#">Help</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <section class="p-5">
        <div class="container result-container">
            <h2 class="mb-4">Search Results</h2>
            
            <?php if ($result->num_rows > 0): ?>
                <table class="table table-striped table-bordered">
                    <thead class="thead-dark">
                        <tr>
                            <th>Study ID</th>
                            <th>Study Name</th>
                            <th>Chinese Name</th>
                            <th>English Name</th>
                            <th>Studied microbes</th>
                            <th>Soil Treatment Groups</th>
                            <th>Cultivation Practice</th>
                            <th>Property</th>
                            <th>Panel</th>
                            <th>Tropics</th>
                            <th>Effect Category</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <a href="../browse/Study.php?id=<?= htmlspecialchars($row['Study ID']) ?>" 
                                   class="text-primary text-decoration-none">
                                    <?= htmlspecialchars($row['Study ID']) ?>
                                </a>
                            </td>
                            <td><?= htmlspecialchars($row['Study Name']) ?></td>
                            <td><?= htmlspecialchars($row['Chinese Name']) ?></td>
                            <td><?= htmlspecialchars($row['English Name']) ?></td>
                            <td><?= htmlspecialchars($row['Studied microbes']) ?></td>
                            <td><?= htmlspecialchars($row['Soil Treatment Groups']) ?></td>
                            <td><?= htmlspecialchars($row['Cultivation Practice']) ?></td>
                            <td><?= htmlspecialchars($row['Property']) ?></td>
                            <td><?= htmlspecialchars($row['Panel']) ?></td>
                            <td><?= htmlspecialchars($row['Tropics']) ?></td>
                            <td><?= htmlspecialchars($row['Effect Category']) ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-warning">没有找到与 "<?= htmlspecialchars($search_query) ?>" 相关的结果。</div>
            <?php endif; ?>
        </div>
    </section>

    <?php
    // 关闭连接
    $stmt->close();
    $conn->close();
    ?>
</body>
</html>