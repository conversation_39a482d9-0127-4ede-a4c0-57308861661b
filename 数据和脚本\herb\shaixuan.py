# 导入pandas库，用于数据处理
import pandas as pd

# ---------------- 您需要筛选的药草列表 (手动清理版) ----------------
# 这是为您手动清理和优化好的最终中药列表
# 我直接使用了集合(set)，这是一种高效的数据结构，非常适合后续的筛选操作
chinese_names_to_keep = {
    "党参", "三七", "平贝母", "西洋参", "人参", "枸杞子", "沙棘", "灵芝", "白头翁", "白术", 
    "白芷", "百合", "石榴皮", "红花", "大腹皮", "小蓟", "山豆根", "八角茴香", "地黄", "制川乌", 
    "核桃仁", "酸枣仁", "太子参", "甘草", "当归", "麦冬", "黄精", "淡豆豉", "菊花", "紫苏叶", 
    "灯心草", "天麻", "花椒", "阿魏", "附子", "麦芽", "玫瑰花", "降香", "砂仁", "荔枝核", 
    "辣椒", "马齿苋", "赤芍", "金银花", "青葙子", "重楼", "秦皮", "野菊花", "黄芩", "黄连", 
    "椿皮", "油松节", "金铁锁", "南五味子", "亚麻子", "蓖麻子", "鸭跖草", "半夏", "干姜", "柴胡", 
    "丹参", "牛膝", "西红花", "急性子", "白及", "白茅根", "艾叶", "松花粉", "蒲黄", "大豆黄卷", 
    "紫菀", "石斛", "广藿香", "苍术", "陈皮", "苦参", "川贝母", "鸡冠花", "松节"
}
print(f"已加载 {len(chinese_names_to_keep)} 种待筛选的中药。")
# ----------------------------------------------------

# 根据您的文件结构确定的中文名列
chinese_name_column = 'Herb_cn_name'

# 之前确认过的文件路径
input_csv_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\HERB数据库数据\HERB_herb_info_v2_converted.csv"
output_csv_path = r"D:\Software\XAMPP\htdocs\MPRAM\数据和脚本\HERB数据库数据\HERB_filtered_herbs_by_cn_name.csv"

try:
    # 1. 读取CSV文件
    print(f"正在读取文件: {input_csv_path}")
    df = pd.read_csv(input_csv_path)
    print("文件读取成功。")

    # 2. 执行筛选
    print(f"开始筛选... 将仅在 '{chinese_name_column}' 列中查找匹配项。")
    filtered_df = df[
        df[chinese_name_column].isin(chinese_names_to_keep)
    ].copy() 
    
    # 3. 处理并保存结果
    if not filtered_df.empty:
        print(f"筛选完成，共找到 {len(filtered_df)} 条匹配的药草记录。")
        
        # 将所有空白单元格填充为 "NA"
        print("正在将空白单元格填充为 'NA'...")
        filtered_df.fillna('NA', inplace=True)

        # 保存到新文件
        filtered_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
        print(f"✅ 成功！处理完成的结果已保存至新文件: {output_csv_path}")
    else:
        print("⚠️ 筛选完成，但没有找到任何匹配的记录。")
    
    # 4. 报告未匹配项
    print("\n" + "="*50)
    print("正在检查未匹配到的药草...")
    found_chinese_names = set(filtered_df[chinese_name_column].unique())
    
    unmatched_herbs = chinese_names_to_keep - found_chinese_names

    if not unmatched_herbs:
        print("✅ 好消息！您提供的所有药草都已成功匹配。")
    else:
        print(f"⚠️ 注意：在您的列表中，有 {len(unmatched_herbs)} 种药草未在文件中找到匹配项：")
        for i, herb_name in enumerate(sorted(list(unmatched_herbs))):
            print(f"  {i+1}. {herb_name}")
    print("="*50)

except FileNotFoundError:
    print(f"❌ 错误：找不到源文件 {input_csv_path}。请确认路径和文件名是否正确。")
except KeyError as e:
    print(f"❌ 错误：列名 '{chinese_name_column}' 未找到。请检查CSV文件。")
except Exception as e:
    print(f"❌ 发生未知错误: {e}")