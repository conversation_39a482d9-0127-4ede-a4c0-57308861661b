<?php
// 开启错误显示
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/php_error.log');

// 添加更多调试信息
echo "<!-- PHP is running in microbe_detail.php -->\n";
echo "<!-- Current PHP version: " . phpversion() . " -->\n";
echo "<!-- Current working directory: " . getcwd() . " -->\n";
echo "<!-- Script path: " . __FILE__ . " -->\n";
echo "<!-- Include path: " . get_include_path() . " -->\n";
echo "<!-- DIR: " . __DIR__ . " -->\n";

// 创建日志目录
$log_dir = __DIR__ . '/../../logs';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0777, true);
}

// 获取URL参数
$microbe_id = isset($_GET['id']) ? $_GET['id'] : '';  // 添加id参数
$microbe_name = isset($_GET['name']) ? $_GET['name'] : '';
$level = isset($_GET['level']) ? $_GET['level'] : '';
$group = isset($_GET['group']) ? $_GET['group'] : 'bacteria'; // 默认为bacteria，可以是fungi

// 确保group名称正确，转换为小写并确保值为'bacteria'或'fungi'
$group = strtolower($group);
if ($group !== 'bacteria' && $group !== 'fungi') {
    $group = 'bacteria'; // 默认使用bacteria
}

// 添加调试信息
echo "<!-- Debug: Microbe ID: " . htmlspecialchars($microbe_id) . " -->\n";
echo "<!-- Debug: Microbe name: " . htmlspecialchars($microbe_name) . " -->\n";
echo "<!-- Debug: Level: " . htmlspecialchars($level) . " -->\n";
echo "<!-- Debug: Group: " . htmlspecialchars($group) . " -->\n";

// 连接数据库
try {
    echo "<!-- 尝试加载数据库连接文件 -->\n";
    $db_conn_file = __DIR__ . '/../../includes/config/db_connection.php';
    echo "<!-- 数据库连接文件路径: " . $db_conn_file . " -->\n";
    
    if (file_exists($db_conn_file)) {
        echo "<!-- 数据库连接文件存在 -->\n";
        require_once($db_conn_file);
        echo "<!-- 数据库连接文件已加载 -->\n";
    } else {
        echo "<!-- 数据库连接文件不存在 -->\n";
        throw new Exception("数据库连接文件不存在: " . $db_conn_file);
    }
    
    // 数据库连接检查
    if (!isset($conn) || !$conn) {
        echo "<!-- 数据库连接对象不存在或为空 -->\n";
        throw new Exception("数据库连接失败");
    } else {
        echo "<!-- 数据库连接已建立 -->\n";
    }

    // 确定要查询的表名
    $table_name = $group;
    
    // 添加表格存在检查
    $check_table_query = "SHOW TABLES LIKE '$table_name'";
    $check_result = $conn->query($check_table_query);
    if (!$check_result || $check_result->num_rows == 0) {
        error_log("表'$table_name'不存在");
        throw new Exception("表'$table_name'不存在");
    }
    
    // 添加字段检查
    $fields_query = "SHOW COLUMNS FROM $table_name";
    $fields_result = $conn->query($fields_query);
    if (!$fields_result) {
        error_log("无法获取表'$table_name'的字段: " . $conn->error);
        throw new Exception("无法获取表结构");
    }
    
    $fields = [];
    while ($field = $fields_result->fetch_assoc()) {
        $fields[] = $field['Field'];
    }
    
    error_log("表'$table_name'的字段: " . implode(", ", $fields));
    
    // 准备SQL查询，确保只查询存在的字段
    $select_fields = ["id", "name", "taxonomy_path"];
    
    // 检查其他可能的字段是否存在
    $optional_fields = ["nr_samples", "nr_projects", "nr_hosts", "hosts", "abundance_data"];
    foreach ($optional_fields as $field) {
        if (in_array($field, $fields)) {
            $select_fields[] = $field;
        }
    }
    
    // 构建查询
    $query = "SELECT " . implode(", ", $select_fields) . " FROM $table_name WHERE id = ?";
    error_log("SQL查询: " . $query);
    
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        error_log("准备语句失败: " . $conn->error . ", SQL: " . $query);
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数
    $stmt->bind_param('s', $microbe_id);
    
    // 添加调试信息
    error_log("查询参数 microbe_id: " . $microbe_id . ", 表名: " . $table_name);

    // 执行查询
    if (!$stmt->execute()) {
        throw new Exception("执行查询失败: " . $stmt->error);
    }

    // 获取结果
    $result = $stmt->get_result();
    $microbe_data = $result->fetch_assoc();

    if (!$microbe_data) {
        throw new Exception("未找到微生物数据");
    }

    // 添加原始数据调试信息
    error_log("从数据库获取的原始数据: " . print_r($microbe_data, true));

    // 检查abundance_data字段是否存在
    $abundance_data = [];
    if (isset($microbe_data['abundance_data'])) {
        error_log("abundance_data 字段内容: " . $microbe_data['abundance_data']);
        
        // 处理abundance_data
        if (!empty($microbe_data['abundance_data'])) {
            // 添加调试日志
            error_log("Raw abundance_data from database: " . $microbe_data['abundance_data']);
            
            // 尝试直接解码
            $decoded_data = json_decode($microbe_data['abundance_data'], true);
            
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded_data)) {
                // 如果数据已经是正确的格式，直接使用
                $abundance_data = $decoded_data;
            } else {
                // 如果解码失败，尝试其他格式
                $rows = explode("\n", trim($microbe_data['abundance_data']));
                foreach ($rows as $row) {
                    $parts = explode("\t", trim($row));
                    if (count($parts) >= 2) {
                        $abundance_data[] = [
                            'name' => trim($parts[0]),
                            'value' => floatval(trim($parts[1]))
                        ];
                    }
                }
            }
            
            // 验证数据
            if (empty($abundance_data)) {
                error_log("No valid abundance data found after processing");
                $abundance_data = [];
            }
            
            error_log("Final processed abundance_data: " . json_encode($abundance_data));
        }
    } else {
        error_log("abundance_data 字段不存在");
    }

    // 检查hosts表是否存在，如果存在则进行调试查询
    $check_hosts_table = "SHOW TABLES LIKE 'hosts'";
    $hosts_table_exists = $conn->query($check_hosts_table);
    
    if ($hosts_table_exists && $hosts_table_exists->num_rows > 0) {
        // 添加调试查询
        $debug_query = "SELECT * FROM hosts WHERE id = ?";
        $debug_stmt = $conn->prepare($debug_query);
        
        if ($debug_stmt) {
            $debug_stmt->bind_param('s', $microbe_id);
            $debug_stmt->execute();
            $debug_result = $debug_stmt->get_result();
            
            if ($debug_result && $debug_result->num_rows > 0) {
                $debug_data = $debug_result->fetch_assoc();
                error_log("调试查询结果: " . print_r($debug_data, true));
                
                if (isset($debug_data['abundance_data'])) {
                    error_log("hosts表中的abundance_data: " . $debug_data['abundance_data']);
                }
            } else {
                error_log("hosts表中未找到ID为 " . $microbe_id . " 的记录");
            }
            
            $debug_stmt->close();
        } else {
            error_log("调试查询准备失败: " . $conn->error);
        }
    } else {
        error_log("hosts表不存在");
    }

    // 处理hosts字段
    $hosts = [];
    if (isset($microbe_data['hosts']) && !empty($microbe_data['hosts'])) {
        // 尝试解析JSON
        $hosts_data = json_decode($microbe_data['hosts'], true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($hosts_data)) {
            $hosts = $hosts_data;
        } else {
            // 如果不是JSON，尝试按逗号分割
            $hosts = explode(',', $microbe_data['hosts']);
        }
    }
    
    // 如果hosts字段为空或解析失败，使用默认值
    if (empty($hosts)) {
        $hosts = ['Unknown'];
    }

    // 添加调试信息到页面
    echo "<!-- Debug: microbe_id = " . htmlspecialchars($microbe_id) . " -->\n";
    echo "<!-- Debug: SQL Query = " . htmlspecialchars($query) . " -->\n";
    echo "<!-- Debug: Raw abundance_data = " . htmlspecialchars($microbe_data['abundance_data']) . " -->\n";
    echo "<!-- Debug: Parsed abundance_data = " . htmlspecialchars(json_encode($abundance_data)) . " -->\n";
    echo "<!-- Debug: Raw hosts = " . htmlspecialchars($microbe_data['hosts']) . " -->\n";
    echo "<!-- Debug: Parsed hosts = " . htmlspecialchars(json_encode($hosts)) . " -->\n";

    $stmt->close();

} catch (Exception $e) {
    error_log("错误: " . $e->getMessage());
    die("加载数据时出错。请稍后再试或联系管理员。");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars(str_replace('_', ' ', $microbe_name)); ?> Details</title>
    <!-- 添加favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">
    
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/bootstrap-table.min.css">
    <link rel="stylesheet" href="../../public/css/common.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.4.0/css/all.css">
    
    <!-- 添加 Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-2.27.1.min.js"></script>
    
    <!-- 添加 jQuery 和 Bootstrap 相关文件 -->
    <script src="../../public/js/jquery-3.7.1.min.js"></script>
    <script src="../../public/js/bootstrap.bundle.min.js"></script>
    <script src="../../public/js/bootstrap-table.min.js"></script>
    
    <style>
    /* 修改容器样式 */
    .container {
        max-width: 100% !important;
        padding: 2rem !important;
        margin: 0 auto !important;
        width: 100% !important;
    }

    /* 表格样式 */
    .table-container {
        margin: 20px 0;
        width: 100%;
    }
    
    .table {
        margin-bottom: 0;
        width: 100% !important;
    }
    
    .table th {
        background-color: #f8f9fa;
        font-weight: 500;
        color: #333;
        width: 200px;
        padding: 12px 8px;
        border: 1px solid #dee2e6;
    }
    
    .table td {
        padding: 12px 8px;
        border: 1px solid #dee2e6;
    }
    
    .table tr:hover {
        background-color: #f8f9fa;
    }
    
    .taxonomy-path {
        font-style: italic;
        color: #333;
    }

    /* 调整内容区域样式 */
    .info-section, .abundance-chart {
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        background-color: #fff;
        padding: 20px;
    }

    .info-section h4 {
        color: #002060;
        margin-bottom: 20px;
        font-weight: 600;
    }

    /* 优化返回按钮样式 */
    .back-button {
        display: inline-flex;
        height: 35px;
        align-items: center;
        padding: 8px 16px;
        margin: 0 0 0 20px;
        background-color: #fff;
        color: #002060;
        border: 1px solid #002060;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-button:hover {
        background-color: #002060;
        color: #fff;
        text-decoration: none;
    }

    .back-button i {
        margin-right: 8px;
        font-size: 14px;
    }

    /* 确保图表容器铺满 */
    #abundanceChart {
        width: 100% !important;
        height: auto !important;
        min-height: 200px;
        margin: 0 auto;
    }

    /* 调整图表说明文字样式 */
    .chart-note {
        margin: 10px 0 20px 0;
        font-size: 14px;
        color: #666;
        padding: 10px;
    }

    .abundance-chart h5 {
        margin-bottom: 15px;
        color: #002060;
        font-weight: 500;
    }

    /* 移动设备适配 */
    @media (max-width: 768px) {
        .container {
            max-width: 95% !important;
            width: 95% !important;
        }
    }
    </style>
</head>
<body>
    <!-- Navigation bar -->
    <header>
        <!-- Left Logo and title area -->
        <div class="left-content">
            <!-- Logo container -->
            <div class="logo-container">
                <img src="../../home/<USER>/logo.png" alt="MPRAM Logo" class="logo-image">
            </div>
            <!-- Title and subtitle container -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">MPRAM</h1>
                <p class="mb-0 small">Medicinal Plant Rhizosphere Associated Microbiome Database </p>
            </div>
        </div>
        <!-- Hamburger button -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- Navigation links area -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="../browse/browse.php" class="nav-link">Search</a></li>
                <li class="nav-item"><a href="../browse/Project.php" class="nav-link">Browse</a></li>
                <li class="nav-item"><a href="Microbes.php" class="nav-link active">Microbes</a></li>
                <li class="nav-item"><a href="Network.php" class="nav-link">Network</a></li>
                <li class="nav-item"><a href="../../map/map.html" class="nav-link">Map</a></li>
                <li class="nav-item"><a href="../help/help.html" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

    <div class="container content-section" style="margin-top: 80px;">
        <div style="position: relative; text-align: center; margin-bottom: 20px;">
            <a href="javascript:history.back()" class="back-button" style="position: absolute; left: 0; top: 50%; transform: translateY(-50%);">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <h4 style="margin: 0; display: inline-block;"><?php echo htmlspecialchars($microbe_name); ?> Details</h4>
        </div>

        <div class="info-section">
            <div class="table-container">
                <table class="table table-hover table-bordered">
                    <tbody>
                        <tr>
                            <th width="200">Level</th>
                            <td><?php echo htmlspecialchars($level); ?></td>
                        </tr>
                        <tr>
                            <th>Taxonomy</th>
                            <td class="taxonomy-path"><?php echo htmlspecialchars($microbe_data['taxonomy_path']); ?></td>
                        </tr>
                        <tr>
                            <th>Name</th>
                            <td id="organism-name-display"><?php echo htmlspecialchars($microbe_name); ?></td>
                        </tr>
                        <tr>
                            <th>NCBI Links</th>
                            <td id="ncbi-links-container">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th>Projects</th>
                            <td><?php echo number_format($microbe_data['nr_projects']); ?></td>
                        </tr>
                        <tr>
                            <th>Hosts</th>
                            <td><?php echo htmlspecialchars(implode(', ', $hosts)); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="abundance-chart">
            <h5>Relative Abundance of This Taxon Across Hosts</h5>
            <div id="abundanceChart"></div>
            <div class="chart-note">
                <i class="fas fa-info-circle"></i>
                The box plot shows the distribution of raw abundance values for each host. The box represents the interquartile range (IQR), the line inside the box is the median, and the whiskers extend to the minimum and maximum values within 1.5 times the IQR.
            </div>
        </div>
    </div>

    <!-- Footer area -->
    <footer class="container-fluid py-3 text-center">
        <p>Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
    </footer>

    <script>
        function toggleNav() {
            const nav = document.querySelector('nav');
            const hamburger = document.querySelector('.hamburger');
            nav.classList.toggle('show');
            hamburger.classList.toggle('active');
        }
    </script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 添加错误处理
        try {
            // 获取原始数据并记录日志
            const rawAbundanceData = <?php echo json_encode($abundance_data, JSON_PARTIAL_OUTPUT_ON_ERROR); ?>;
            console.log('处理数据开始，原始数据:', rawAbundanceData);
            
            // 检查数据有效性
            if (!rawAbundanceData || !Array.isArray(rawAbundanceData) || rawAbundanceData.length === 0) {
                console.error('Abundance data is invalid or empty:', rawAbundanceData);
                document.getElementById('abundanceChart').innerHTML = 
                    '<div style="text-align: center; padding: 20px; color: orange;">No abundance data available for this microbe.</div>';
                return;
            }
            
            // 处理数据为Plotly.js的盒须图格式
            let hostData = {};
            
            // 根据数据格式处理
            rawAbundanceData.forEach(item => {
                if (item.hasOwnProperty('host') && Array.isArray(item.raw_values)) {
                    const host = item.host || 'Unknown';
                    // 将raw_values转换为数字数组
                    const values = item.raw_values
                        .map(v => parseFloat(v))
                        .filter(v => !isNaN(v));
                    
                    if (values.length > 0) {
                        if (!hostData[host]) {
                            hostData[host] = [];
                        }
                        hostData[host] = hostData[host].concat(values);
                    }
                }
            });
            
            // 转换数据为盒须图格式
            let hosts = Object.keys(hostData);
            
            // 对宿主按中位数值排序
            hosts.sort((a, b) => {
                const medianA = calculateMedian(hostData[a]);
                const medianB = calculateMedian(hostData[b]);
                return medianB - medianA; // 降序排列
            });
            
            if (hosts.length === 0) {
                console.error('处理后无有效数据');
                document.getElementById('abundanceChart').innerHTML = 
                    '<div style="text-align: center; padding: 20px; color: orange;">No valid abundance data available for this microbe.</div>';
                return;
            }
            
            // 创建颜色数组
            const colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEEAD',
                '#D4A5A5', '#9B59B6', '#3498DB', '#E67E22', '#2ECC71',
                '#FF87AB', '#A6D785', '#FFB98B', '#AEC6CF', '#836FFF',
                '#98FB98', '#DDA0DD', '#F0E68C', '#87CEEB', '#FFA07A',
                '#20B2AA', '#FF69B4', '#BA55D3', '#4682B4', '#9ACD32'
            ];
            
            // 为每个宿主分配颜色
            const hostColors = {};
            hosts.forEach((host, index) => {
                hostColors[host] = colors[index % colors.length];
            });

            // 设置图表高度，根据宿主数量动态调整
            const chartHeight = Math.max(800, hosts.length * 40);
            document.getElementById('abundanceChart').style.height = chartHeight + 'px';

            // 创建图表数据
            let plotData = hosts.map(host => ({
                type: 'box',
                name: host,
                x: hostData[host],
                orientation: 'h',
                boxpoints: 'suspectedoutliers',  // 只显示可疑的异常值
                marker: {
                    size: 4
                },
                line: {
                    width: 1
                }
            }));
            
            // 创建图表布局
            const layout = {
                title: 'Relative Abundance of This Taxon Across Hosts',
                xaxis: {
                    title: 'Relative abundance (%)',
                    zeroline: true
                },
                yaxis: {
                    title: '',
                    automargin: true
                },
                height: Math.max(300, hosts.length * 30), // 根据数据量动态调整高度
                margin: {
                    l: 200,
                    r: 50,
                    t: 50,
                    b: 50
                },
                showlegend: false,
                autosize: true
            };
            
            // 创建配置选项
            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false
            };
            
            // 渲染图表
            Plotly.newPlot('abundanceChart', plotData, layout, config);
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                Plotly.relayout('abundanceChart', {
                    autosize: true
                });
            });
            
        } catch (error) {
            console.error('渲染图表时出错:', error);
            document.getElementById('abundanceChart').innerHTML = 
                `<div style="text-align: center; padding: 20px; color: red;">Error rendering chart: ${error.message}</div>`;
        }
    });
    
    // 计算中位数的辅助函数
    function calculateMedian(values) {
        if (!values || values.length === 0) return 0;
        
        const sorted = [...values].sort((a, b) => a - b);
        const middle = Math.floor(sorted.length / 2);
        
        if (sorted.length % 2 === 0) {
            return (sorted[middle - 1] + sorted[middle]) / 2;
        } else {
            return sorted[middle];
        }
    }
    </script>

    <script>
        $(function() {
            // 移除 Bootstrap Table 初始化
            $('.table').addClass('table-hover table-bordered');
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取菌株名称并处理
            let organismName = document.getElementById('organism-name-display').textContent.trim();
            
            // 处理菌种名称，移除特殊后缀
            function cleanOrganismName(name) {
                // 移除常见的分类学后缀
                const suffixesToRemove = [
                    '_unclassified',
                    '_uncultured',
                    '_group',
                    '_clade',
                    '_complex',
                    '_sensu_stricto',
                    '_and_relatives',
                    '_environmental_samples',
                    '_bacterium',
                    '_gen_Incertae_sedis' // 添加了_gen_Incertae_sedis后缀
                ];
                
                let cleanedName = name;
                
                // 移除所有指定的后缀
                suffixesToRemove.forEach(suffix => {
                    cleanedName = cleanedName.replace(new RegExp(suffix, 'gi'), '');
                });
                
                // 移除下划线，替换为空格
                cleanedName = cleanedName.replace(/_/g, ' ');
                
                // 移除多余的空格
                cleanedName = cleanedName.replace(/\s+/g, ' ').trim();
                
                return cleanedName;
            }
            
            // 清理菌种名称
            const cleanedOrganismName = cleanOrganismName(organismName);
            
            // 从taxonomy_path中提取taxonomy ID（如果有的话）
            const taxonomyPath = document.querySelector('.taxonomy-path').textContent.trim();
            const taxonomyIdMatch = taxonomyPath.match(/\[tax_id: (\d+)\]/);
            const taxonomyId = taxonomyIdMatch ? taxonomyIdMatch[1] : null;
            
            // 定义NCBI数据库链接
            const ncbiDatabases = [
                {
                    name: 'Taxonomy',
                    url: taxonomyId 
                        ? `https://www.ncbi.nlm.nih.gov/Taxonomy/Browser/wwwtax.cgi?id=${taxonomyId}`
                        : `https://www.ncbi.nlm.nih.gov/Taxonomy/Browser/wwwtax.cgi?name=${encodeURIComponent(cleanedOrganismName)}`
                },
                {
                    name: 'Nucleotide',
                    url: `https://www.ncbi.nlm.nih.gov/nuccore/?term=${encodeURIComponent(cleanedOrganismName + ' [Organism]')}`
                },
                {
                    name: 'Protein',
                    url: `https://www.ncbi.nlm.nih.gov/protein/?term=${encodeURIComponent(cleanedOrganismName + ' [Organism]')}`
                },
                {
                    name: 'Genome',
                    url: `https://www.ncbi.nlm.nih.gov/genome/?term=${encodeURIComponent(cleanedOrganismName + ' [Organism]')}`
                },
                {
                    name: 'BioProject',
                    url: `https://www.ncbi.nlm.nih.gov/bioproject/?term=${encodeURIComponent(cleanedOrganismName + ' [Organism]')}`
                },
                {
                    name: 'PubMed',
                    url: `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(cleanedOrganismName)}`
                }
            ];
            
            // 生成链接HTML
            const linksContainer = document.getElementById('ncbi-links-container');
            const linksHtml = ncbiDatabases.map(db => {
                return `<a href="${db.url}" target="_blank" class="btn btn-outline-primary btn-sm m-1" 
                    title="Search for ${cleanedOrganismName} in NCBI ${db.name}">
                    <i class="fas fa-external-link-alt me-1"></i>${db.name}
                </a>`;
            }).join('');
            
            // 更新容器内容
            linksContainer.innerHTML = linksHtml;
            
            // 添加处理后的菌种名称提示（可选）
            if (organismName !== cleanedOrganismName) {
                linksContainer.insertAdjacentHTML('afterbegin', 
                    `<small class="text-muted d-block mb-2">
                        Searching for: ${cleanedOrganismName}
                    </small>`
                );
            }
        });
    </script>
</body>
</html> 