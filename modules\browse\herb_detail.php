<?php
// 设置执行时间和内存限制
ini_set('max_execution_time', 300);
ini_set('memory_limit', '512M');

// 创建一个获取链接URL的函数
function getLinkUrl($db_type, $id_value) {
    // 清理ID值，移除可能的空格和特殊字符
    $id_value = trim($id_value);

    // 如果ID值为空或为N/A，返回空链接
    if (empty($id_value) || $id_value === 'N/A' || $id_value === '0') {
        return '#';
    }

    $url = '#';
    switch ($db_type) {
        // Ingredient相关链接
        case 'PubChem':
            // 确保PubChem ID是数字
            if (is_numeric($id_value)) {
                $id_value = (int)$id_value; // 移除小数点
                $url = "https://pubchem.ncbi.nlm.nih.gov/compound/{$id_value}";
            }
            break;
        case 'SymMap':
            $url = "https://www.symmap.org/detail/ingredient/{$id_value}";
            break;
        case 'TCMID':
            $url = "https://www.tcmid.org/molecule/{$id_value}";
            break;
        case 'TCMSP':
            $url = "https://tcmsp-e.com/molecule.php?qn={$id_value}";
            break;
        case 'TCM_ID':
            $url = "https://tcm.cmu.edu.tw/compound-detail.php?herb_id={$id_value}";
            break;
        case 'DrugBank':
            $url = "https://go.drugbank.com/drugs/{$id_value}";
            break;
        case 'NPASS':
            $url = "https://bidd.group/NPASS/npass_detail.php?id={$id_value}";
            break;
        case 'HIT':
            $url = "https://hit.idrblab.org/compoundinfo/{$id_value}";
            break;
        case 'CAS':
            $url = "https://commonchemistry.cas.org/detail?cas_rn={$id_value}&search={$id_value}";
            break;
        
        // Target相关链接
        case 'Taxonomy':
            if (is_numeric($id_value)) {
                $url = "https://www.ncbi.nlm.nih.gov/Taxonomy/Browser/wwwtax.cgi?id={$id_value}";
            }
            break;
        case 'Map_location':
            // 地图位置通常没有直接链接，但可以搜索
            if (!empty($id_value) && $id_value !== 'N/A') {
                $url = "https://www.ncbi.nlm.nih.gov/search/all/?term=" . urlencode($id_value);
            }
            break;
        case 'Chromosome':
            // 染色体信息可以链接到NCBI
            if (!empty($id_value) && $id_value !== 'N/A') {
                $url = "https://www.ncbi.nlm.nih.gov/search/all/?term=chromosome+" . urlencode($id_value);
            }
            break;
        case 'Entrez':
            if (is_numeric($id_value)) {
                $url = "https://www.ncbi.nlm.nih.gov/gene/{$id_value}";
            }
            break;
        case 'HGNC':
            // HGNC ID通常是HGNC:数字格式
            if (strpos($id_value, 'HGNC:') === 0) {
                $hgnc_id = str_replace('HGNC:', '', $id_value);
                $url = "https://www.genenames.org/data/gene-symbol-report/#!/hgnc_id/{$hgnc_id}";
            } elseif (is_numeric($id_value)) {
                $url = "https://www.genenames.org/data/gene-symbol-report/#!/hgnc_id/{$id_value}";
            }
            break;
        case 'Ensembl':
            $url = "https://www.ensembl.org/Homo_sapiens/Gene/Summary?g={$id_value}";
            break;
        case 'MGI':
            $url = "https://www.informatics.jax.org/marker/{$id_value}";
            break;
        case 'TTD':
            $url = "https://db.idrblab.net/ttd/data/target/details/{$id_value}";
            break;
        case 'OMIM':
            if (is_numeric($id_value)) {
                $url = "https://www.omim.org/entry/{$id_value}";
            }
            break;
        
        // Disease相关链接
        case 'UMLS_disease_type':
            $url = "https://uts.nlm.nih.gov/uts/umls/concept/{$id_value}";
            break;
        case 'MeSH_disease_class':
            // MeSH ID通常以D开头
            if (strpos($id_value, 'D') === 0 || is_numeric($id_value)) {
                $url = "https://meshb.nlm.nih.gov/record/ui?ui={$id_value}";
            }
            break;
        case 'HPO_disease_class':
            // HPO ID处理
            if (strpos($id_value, 'HP:') === 0) {
                $url = "https://hpo.jax.org/app/browse/term/{$id_value}";
            } else {
                $url = "https://hpo.jax.org/app/browse/term/HP:{$id_value}";
            }
            break;
        case 'DO_disease_class':
            // DO ID处理
            if (strpos($id_value, 'DOID:') === 0) {
                $url = "https://disease-ontology.org/?id={$id_value}";
            } else {
                $url = "https://disease-ontology.org/?id=DOID:{$id_value}";
            }
            break;
        case 'DisGeNET':
            // DisGeNET ID通常是C开头的
            if (strpos($id_value, 'C') === 0) {
                $url = "https://www.disgenet.org/browser/0/1/0/{$id_value}/";
            } else {
                $url = "https://www.disgenet.org/browser/0/1/0/C{$id_value}/";
            }
            break;
        case 'MeSH':
            if (strpos($id_value, 'D') === 0 || is_numeric($id_value)) {
                $url = "https://meshb.nlm.nih.gov/record/ui?ui={$id_value}";
            }
            break;
        case 'HPO':
            if (strpos($id_value, 'HP:') === 0) {
                $url = "https://hpo.jax.org/app/browse/term/{$id_value}";
            } else {
                $url = "https://hpo.jax.org/app/browse/term/HP:{$id_value}";
            }
            break;
        case 'DO':
            if (strpos($id_value, 'DOID:') === 0) {
                $url = "https://disease-ontology.org/?id={$id_value}";
            } else {
                $url = "https://disease-ontology.org/?id=DOID:{$id_value}";
            }
            break;
        case 'ICD10':
            $url = "https://icd.who.int/browse10/2019/en#/{$id_value}";
            break;
        
        // 内部链接
        case 'Ingredient':
            $return_url = isset($_GET['return_url']) ? '&return_url=' . urlencode($_GET['return_url']) : '';
            $url = "herb_detail.php?type=ingredient&herb={$_GET['herb']}&name={$id_value}{$return_url}";
            break;
        case 'Target':
            $return_url = isset($_GET['return_url']) ? '&return_url=' . urlencode($_GET['return_url']) : '';
            $url = "herb_detail.php?type=target&herb={$_GET['herb']}&name={$id_value}{$return_url}";
            break;
        case 'Disease':
            $return_url = isset($_GET['return_url']) ? '&return_url=' . urlencode($_GET['return_url']) : '';
            $url = "herb_detail.php?type=disease&herb={$_GET['herb']}&name={$id_value}{$return_url}";
            break;
    }
    return $url;
}

// 获取参数
$type = isset($_GET['type']) ? $_GET['type'] : '';
$name = isset($_GET['name']) ? $_GET['name'] : '';
$herb = isset($_GET['herb']) ? $_GET['herb'] : '';
$detail_type = $type; // 保存类型到detail_type变量，用于后续判断

// 验证参数
if (empty($type) || empty($name) || empty($herb)) {
    die("参数不完整，请提供type、name和herb参数");
}

// 根据类型确定文件路径和标题
$file_path = '';
$title = '';
$display_name = '';
$show_lipinski_radar = false; // 标记是否显示药物五规则雷达图

switch ($type) {
    case 'ingredient':
        $file_path = $_SERVER['DOCUMENT_ROOT'] . '/MPRAM/HRRB/' . $herb . '/ingredients.csv';
        $title = 'Herb Ingredient Details';
        $display_name = 'Ingredient Name';
        $search_field = 'Ingredient_name';
        $show_lipinski_radar = true; // 仅在成分类型时显示药物五规则雷达图
        break;
    case 'target':
        $file_path = $_SERVER['DOCUMENT_ROOT'] . '/MPRAM/HRRB/' . $herb . '/targets.csv';
        $title = 'Herb Target Details';
        $display_name = 'Target Name';
        $search_field = 'Gene_symbol';
        break;
    case 'disease':
        $file_path = $_SERVER['DOCUMENT_ROOT'] . '/MPRAM/HRRB/' . $herb . '/diseases.csv';
        $title = 'Herb Disease Details';
        $display_name = 'Disease Name';
        $search_field = 'Disease_name';
        break;
    default:
        die("Invalid type parameter");
}

// 检查文件是否存在
if (!file_exists($file_path)) {
    die("File not found: " . $file_path);
}

// 读取CSV文件
$data = [];
$headers = [];
$found = false;
$chart_data = [];
$lipinski_fields = ['MolWt', 'NumHAcceptors', 'NumHDonors', 'MolLogP', 'NumRotatableBonds']; // 药物五规则相关字段
$lipinski_data = []; // 存储药物五规则数据
$other_data = []; // 存储其他字段数据

if (($handle = fopen($file_path, "r")) !== FALSE) {
    // 读取表头
    $headers = fgetcsv($handle);
    
    // 查找指定字段的索引
    $search_index = array_search($search_field, $headers);
    
    if ($search_index === false) {
        die("Field not found: " . $search_field);
    }
    
    // 查找匹配的行
    while (($row = fgetcsv($handle)) !== FALSE) {
        if (isset($row[$search_index]) && $row[$search_index] == $name) {
            $data = $row;
            $found = true;
            
            // 收集用于图表的数据
            foreach ($headers as $index => $header) {
                // 跳过ID列和非数值列
                if (stripos($header, 'id') === 0 && strlen($header) <= 3) {
                    continue;
                }
                
                // 检查是否为数值型数据
                if (isset($row[$index]) && is_numeric($row[$index]) && $row[$index] > 0) {
                    $chart_data[$header] = floatval($row[$index]);
                }
                
                // 分类药物五规则数据和其他数据
                if (in_array($header, $lipinski_fields) && isset($row[$index])) {
                    $lipinski_data[$header] = $row[$index];
                } else {
                    $other_data[$header] = isset($row[$index]) ? $row[$index] : 'N/A';
                }
            }
            break;
        }
    }
    fclose($handle);
}

if (!$found) {
    die("No matching data found");
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?> - MPRAM</title>
    <meta name="description" content="MPRAM - Detailed information for herbal medicine components, targets and diseases">
    <meta name="keywords" content="MPRAM,herbal medicine,components,targets,diseases,research data">
    <meta name="author" content="MPRAM Development Team">
    
    <!-- DNS prefetch -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="../../public/css/bootstrap.min.css" as="style">
    <link rel="preload" href="../../public/js/jquery-3.7.1.min.js" as="script">
    <link rel="preload" href="../../home/<USER>/logo.png" as="image">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="icon" type="image/png" sizes="128x128" href="../../home/<USER>/logo.png">
    <link rel="shortcut icon" type="image/png" sizes="256x256" href="../../home/<USER>/logo.png">
    <link rel="apple-touch-icon" sizes="256x256" href="../../home/<USER>/logo.png">

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../../public/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../public/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 添加 ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <style>
        /* 基础布局样式 */
        body {
            background-color: #f8f9fa;
        }

        /* 内容包装器样式 */
        .content-wrapper {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            padding: 25px;
        }

        /* 卡片样式 */
        .card {
            border: 1px solid rgba(0, 32, 96, 0.1);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .card-header {
            background-color: rgba(0, 32, 96, 0.05);
            border-bottom: 1px solid rgba(0, 32, 96, 0.1);
            padding: 0.85rem 1.5rem;
        }

        .card-header h5 {
            color: #002060;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
        }

        /* 表格容器样式 */
        .table-responsive {
            margin: 0;
            overflow: hidden;
        }

        /* 表格样式 */
        .table {
            margin-bottom: 0;
            width: 100%;
        }

        .table thead th {
            background-color: rgba(0, 32, 96, 0.05);
            border-bottom: 1px solid rgba(0, 32, 96, 0.1);
            color: #002060;
            font-weight: 600;
            padding: 12px 15px;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .table td, .table th {
            padding: 12px 15px;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 32, 96, 0.05);
        }
        
        .table tbody tr:hover {
            background-color: rgba(0, 32, 96, 0.02);
        }

        /* 详情表格样式 */
        .detail-table th:first-child {
            width: 30%;
        }

        .detail-table th:nth-child(2) {
            width: 70%;
        }

        .detail-table th:nth-child(3) {
            width: 20%;
        }
        
        /* 属性表格样式 */
        .property-table th:first-child {
            width: 30%;
        }

        .property-table th:nth-child(2) {
            width: 70%;
        }
        
        /* 数据库链接样式 */
        .database-links-container {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 8px;
        }
        
        .database-link {
            display: inline-block;
            padding: 3px 8px;
            color: #002060;
            background-color: #f8f9fa;
            border-radius: 4px;
            white-space: nowrap;
        }
        
        .database-link strong {
            font-weight: 600;
            margin-right: 3px;
        }
        
        .db-link {
            color: #002060;
            text-decoration: none;
            border-bottom: 1px dotted #002060;
            transition: all 0.2s ease;
            display: inline-block;
            margin-right: 0;
        }
        
        .db-link:hover {
            color: #003399;
            text-decoration: none;
            border-bottom: 1px solid #003399;
        }
        
        /* 调整链接之间的间距 */
        .db-link + .db-link {
            margin-left: 5px;
        }
        
        .database-separator {
            color: #6c757d;
            margin: 0 2px;
            font-weight: 300;
        }

        /* 返回按钮样式 */
        .back-btn {
            margin-bottom: 20px;
        }

        .back-btn .btn {
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .back-btn .btn:hover {
            background: linear-gradient(145deg, #5a6268, #495057);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .back-btn .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        /* 雷达图容器样式 */
        #radarChart {
            width: 100%;
            height: 400px;
            margin: 0 auto;
        }

        /* 判断结果样式 */
        .judgment-yes {
            color: #28a745;
            font-weight: 500;
        }

        .judgment-no {
            color: #dc3545;
            font-weight: 500;
        }
        
        /* 药物五规则表格样式 */
        .table-active {
            background-color: rgba(0, 123, 255, 0.1);
        }
        
        /* Drug-likeness 样式 - 新设计 */
        .drug-likeness-container .card {
            border: none;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .drug-likeness-container .card-body {
            padding: 1.5rem;
            background: linear-gradient(to right, #f8f9fa, #ffffff);
        }
        
        .drug-likeness-excellent {
            background: linear-gradient(135deg, rgba(0, 32, 96, 0.05), rgba(0, 32, 96, 0.15)) !important;
            border-top: 3px solid #002060;
        }
        
        .drug-likeness-good {
            background: linear-gradient(135deg, rgba(0, 32, 96, 0.05), rgba(0, 32, 96, 0.15)) !important;
            border-top: 3px solid #002060;
        }
        
        .drug-likeness-moderate {
            background: linear-gradient(135deg, rgba(0, 32, 96, 0.05), rgba(0, 32, 96, 0.15)) !important;
            border-top: 3px solid #002060;
        }
        
        .drug-likeness-poor {
            background: linear-gradient(135deg, rgba(0, 32, 96, 0.05), rgba(0, 32, 96, 0.15)) !important;
            border-top: 3px solid #002060;
        }
        
        .drug-likeness-neutral {
            background: linear-gradient(135deg, rgba(0, 32, 96, 0.05), rgba(0, 32, 96, 0.15)) !important;
            border-top: 3px solid #002060;
        }
        
        .drug-likeness-container .fas {
            color: #495057;
            opacity: 0.8;
        }
        
        .drug-likeness-excellent .fas,
        .drug-likeness-good .fas,
        .drug-likeness-moderate .fas,
        .drug-likeness-poor .fas,
        .drug-likeness-neutral .fas {
            color: #002060;
        }
        
        .drug-likeness-judgment .badge {
            font-size: 0.9rem;
            padding: 0.4rem 1.5rem;
            border-radius: 30px;
            background-color: #002060;
            color: white;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .drug-likeness-excellent .badge,
        .drug-likeness-good .badge,
        .drug-likeness-moderate .badge,
        .drug-likeness-poor .badge,
        .drug-likeness-neutral .badge {
            background-color: #002060;
            color: white;
        }
        
        .drug-likeness-value .h3 {
            font-weight: 600;
            color: #002060;
        }
        
        .qed-info-icon {
            color: #002060;
            font-size: 0.9rem;
            margin-left: 5px;
            transition: color 0.2s;
            opacity: 0.7;
        }
        
        .qed-info-icon:hover {
            color: #002060;
            opacity: 1;
            text-decoration: none;
        }
        
        /* 工具提示样式 */
        .tooltip-inner {
            max-width: 300px;
            padding: 10px;
            background-color: rgba(33, 37, 41, 0.95);
            border-radius: 6px;
            font-size: 0.9rem;
            text-align: left;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
        }
        
        /* 确保两侧卡片高度一致 */
        .equal-height-row {
            display: flex;
            flex-wrap: wrap;
        }
        
        .equal-height-row > div {
            display: flex;
            flex-direction: column;
        }
        
        .equal-height-row .card {
            flex: 1;
            height: 100%;
        }
        


        /* 响应式调整 */
        @media (max-width: 768px) {
            .content-wrapper {
                padding: 15px;
            }
            
            .card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!--导航栏-->
    <header>
        <!-- 左侧Logo和标题区域 -->
        <div class="left-content">
            <!-- Logo容器 -->
            <div class="logo-container">
                <img src="../../home/<USER>/logo.png" alt="MPRAM Logo" class="logo-image">
            </div>
            <!-- 标题和副标题容器 -->
            <div class="title-container">
                <h1 class="fs-4 mb-0">MPRAM</h1>
                <p class="mb-0 small">Medicinal Plant Rhizosphere Associated Microbiome Database </p>
            </div>
        </div>
        <!-- 汉堡包按钮 -->
        <button class="hamburger" onclick="toggleNav()">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <!-- 导航链接区域 -->
        <nav>
            <ul class="nav">
                <li class="nav-item"><a href="../../index.html" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="browse.php" class="nav-link">Search</a></li>
                <li class="nav-item"><a href="Project.php" class="nav-link active">Browse</a></li>
                <li class="nav-item"><a href="../microbes/Microbes.php" class="nav-link">Microbes</a></li>
                <li class="nav-item"><a href="../microbes/Network.php" class="nav-link">Network</a></li>
                <li class="nav-item"><a href="../../map/map.html" class="nav-link">Map</a></li>
                <li class="nav-item"><a href="../help/help.php" class="nav-link">Help</a></li>
            </ul>
        </nav>
    </header>

    <section class="p-5 text-sm-start">
        <div class="container-fluid px-4">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="content-wrapper">
                        <div class="back-btn">
                            <button onclick="goBack()" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left"></i> Back to Study
                            </button>
                        </div>
                        
                        <h3 class="text-center mb-4" style="color: #002060; font-weight: 600; font-size: 1.5rem;">
                            <?php echo $title; ?>: <?php echo htmlspecialchars($name); ?>
                        </h3>
                        
                                                <!-- 主要数据部分 -->

                        
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Property Details</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped property-table mb-0">
                                        <thead>
                                            <tr>
                                                <th>Term</th>
                                                <th>Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            // 收集所有ID字段
                                            $id_fields = [];
                                            
                                            // 根据页面类型定义适当的ID字段
                                            $id_patterns = [];
                                            
                                            // 根据不同类型页面设置不同的ID字段
                                            if ($detail_type == 'ingredient') {
                                                $id_patterns = [
                                                    'CAS_id', 'SymMap_id', 'TCMID_id', 'TCMSP_id', 
                                                    'TCM_ID_id', 'PubChem_id', 'DrugBank_id', 
                                                    'NPASS_id', 'HIT_id', 'Ingredient_id'
                                                ];
                                            } elseif ($detail_type == 'target') {
                                                $id_patterns = [
                                                    'Target_id', 'Taxonomy_id', 'Map_location', 'Chromosome',
                                                    'Entrez_id', 'OMIM_id', 'HGNC_id', 'Ensembl_id',
                                                    'MGI_id', 'TTD_id'
                                                ];
                                            } elseif ($detail_type == 'disease') {
                                                $id_patterns = [
                                                    'Disease_id', 'UMLS_disease_type_id', 'MeSH_disease_class_id',
                                                    'HPO_disease_class_id', 'DO_disease_class_id', 'DisGeNET_id',
                                                    'MeSH_id', 'HPO_id', 'DO_id', 'ICD10_id', 'OMIM_id'
                                                ];
                                            }
                                            
                                            // 保存Drug_likeness字段的值，但从表格中移除
                                            if (isset($other_data['Drug_likeness'])) {
                                                $drug_likeness_value = $other_data['Drug_likeness'];
                                                unset($other_data['Drug_likeness']);
                                            }
                                            
                                            // 强制移除特定ID字段，确保它们不会显示在表格中
                                            unset($other_data['Ingredient_id']);
                                            unset($other_data['Target_id']);
                                            unset($other_data['Disease_id']);
                                            
                                            // 收集所有ID字段和值（临时保存以便在Links部分显示）
                                            $temp_id_data = [];
                                            foreach ($id_patterns as $id_pattern) {
                                                if (isset($other_data[$id_pattern]) && !empty($other_data[$id_pattern]) && $other_data[$id_pattern] !== 'N/A') {
                                                    $temp_id_data[$id_pattern] = $other_data[$id_pattern];
                                                    
                                                    // 提取数据库名称（移除_id后缀）
                                                    $db_name = str_replace('_id', '', $id_pattern);
                                                    
                                                    // 处理可能的浮点数值（如PubChem: 21637637.0）
                                                    $id_value = $other_data[$id_pattern];
                                                    if (is_numeric($id_value) && floor($id_value) == $id_value) {
                                                        $id_value = (int)$id_value;
                                                    }
                                                    
                                                    // 保存ID及其类型
                                                    $id_fields[$db_name] = [
                                                        'value' => $id_value,
                                                        'type' => $db_name
                                                    ];
                                                    
                                                    // 从其他数据中移除
                                                    unset($other_data[$id_pattern]);
                                                }
                                            }
                                            
                                            // 显示非ID字段和非药物五规则字段
                                            foreach ($other_data as $field => $value) {
                                                // 跳过包含"id"的字段（不区分大小写）、特定ID字段和空值
                                                if ((stripos($field, 'id') !== false) ||  // 任何包含"id"的字段都跳过
                                                    $field === 'Ingredient_id' || 
                                                    $field === 'Target_id' || 
                                                    $field === 'Disease_id' ||  
                                                    empty($value) || $value === 'N/A') {
                                                    continue;
                                                }
                                                
                                                echo '<tr>';
                                                echo '<td>' . htmlspecialchars($field) . '</td>';
                                                echo '<td>' . htmlspecialchars($value) . '</td>';
                                                echo '</tr>';
                                            }
                                            
                                            // 如果有ID字段，显示Links行
                                            if (!empty($id_fields)) {
                                                echo '<tr>';
                                                echo '<td>Links to other databases</td>';
                                                echo '<td class="database-links-container">';
                                                $counter = 0;
                                                foreach ($id_fields as $db_name => $id_info) {
                                                    $id_value = $id_info['value'];
                                                    $db_type = $id_info['type'];
                                                    
                                                    // 跳过空值
                                                    if (empty($id_value) || $id_value == 'N/A') {
                                                        continue;
                                                    }
                                                    
                                                    // 处理特殊情况：将Ingredient_id显示为HERB
                                                    if ($db_type == 'Ingredient') {
                                                        $db_name = 'HERB';
                                                    }
                                                    
                                                    // 检查ID是否包含多个值（以分号分隔）
                                                    if (strpos($id_value, ';') !== false) {
                                                        $id_values = explode(';', $id_value);
                                                        echo '<span class="database-link"><strong>' . htmlspecialchars($db_name) . '</strong>: ';
                                                        
                                                        for ($i = 0; $i < count($id_values); $i++) {
                                                            $current_id = trim($id_values[$i]);
                                                            
                                                            // 跳过空值
                                                            if (empty($current_id) || $current_id == 'N/A') {
                                                                continue;
                                                            }
                                                            
                                                            // 根据不同的数据库类型构建不同的URL
                                                            $url = getLinkUrl($db_type, $current_id);
                                                            
                                                            echo '<a href="' . $url . '" target="_blank" class="db-link">' . htmlspecialchars($current_id) . '</a>';
                                                            
                                                            // 除了最后一个ID外，添加分号和空格
                                                            if ($i < count($id_values) - 1) {
                                                                echo '; ';
                                                            }
                                                        }
                                                        
                                                        echo '</span>';
                                                    } else {
                                                        // 单个ID的处理
                                                        // 根据不同的数据库类型构建不同的URL
                                                        $url = getLinkUrl($db_type, $id_value);
                                                        
                                                        echo '<span class="database-link"><strong>' . htmlspecialchars($db_name) . '</strong>: ';
                                                        echo '<a href="' . $url . '" target="_blank" class="db-link">' . htmlspecialchars($id_value) . '</a></span>';
                                                    }
                                                    
                                                    // 添加分隔符，除了最后一个元素
                                                    if ($counter < count($id_fields) - 1) {
                                                        echo '<span class="database-separator">|</span>';
                                                    }
                                                    $counter++;
                                                }
                                                echo '</td>';
                                                echo '</tr>';
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                                                 <?php if ($show_lipinski_radar && !empty($lipinski_data)): ?>
                        <!-- 药物五规则部分 -->
                        <h4 class="mb-3">Drug-likeness</h4>
                        <div class="row equal-height-row">
                            <!-- 药物五规则表格部分 -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            Lipinski's Rule of Five
                                            <a href="#" class="lipinski-info-icon" data-toggle="tooltip" data-html="true" data-placement="top" title="The Lipinski rule-of-five is a rule of thumb used to determine whether a molecule is likely to be orally available, or bioavailable. It consists of five criteria: molecular weight less than 500 daltons, no more than 5 hydrogen bond donors, no more than 10 hydrogen bond acceptors, no more than 10 rotatable bonds, and logP not exceeding 5.">
                                                <i class="fas fa-question-circle" style="font-size: 0.8rem; opacity: 0.7;"></i>
                                            </a>
                                        </h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <div class="table-responsive">
                                            <table class="table table-striped detail-table mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>Parameter</th>
                                                        <th>Value</th>
                                                        <th>Judgment</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    // 用于雷达图的数据
                                                    $radar_data = [];
                                                    $radar_labels = [];
                                                    $judgment_data = [];
                                                    
                                                    // 显示药物五规则字段数据
                                                    foreach ($lipinski_fields as $field) {
                                                        $value = isset($lipinski_data[$field]) ? $lipinski_data[$field] : 'N/A';
                                                        $judgment = 'N/A';
                                                        $judgment_class = '';
                                                        
                                                        if (is_numeric($value)) {
                                                            $radar_data[] = floatval($value);
                                                            $radar_labels[] = $field;
                                                            
                                                            // 根据Lipinski规则进行判断
                                                            if ($field == 'MolWt') {
                                                                $judgment = (floatval($value) <= 500) ? 'Yes' : 'No';
                                                            } elseif ($field == 'NumHDonors') {
                                                                $judgment = (floatval($value) <= 5) ? 'Yes' : 'No';
                                                            } elseif ($field == 'NumHAcceptors') {
                                                                $judgment = (floatval($value) <= 10) ? 'Yes' : 'No';
                                                            } elseif ($field == 'MolLogP') {
                                                                $judgment = (floatval($value) <= 5) ? 'Yes' : 'No';
                                                            } elseif ($field == 'NumRotatableBonds') {
                                                                $judgment = (floatval($value) <= 10) ? 'Yes' : 'No';
                                                            }
                                                            
                                                            $judgment_data[] = $judgment;
                                                            
                                                            // 添加判断结果的CSS类
                                                            if ($judgment === 'Yes') {
                                                                $judgment_class = 'judgment-yes';
                                                            } elseif ($judgment === 'No') {
                                                                $judgment_class = 'judgment-no';
                                                            }
                                                        }
                                                        
                                                        echo '<tr>';
                                                        echo '<td>' . htmlspecialchars($field) . '</td>';
                                                        echo '<td>' . htmlspecialchars($value) . '</td>';
                                                        echo '<td class="' . $judgment_class . '">' . $judgment . '</td>';
                                                        echo '</tr>';
                                                    }
                                                    
                                                    // 如果之前没有设置Drug_likeness值，尝试再次查找
                                                    if (!isset($drug_likeness_value) && isset($other_data['Drug_likeness'])) {
                                                        $drug_likeness_value = $other_data['Drug_likeness'];
                                                    }
                                                    
                                                    // 不在表格中显示Drug_likeness，将在表格外显示
                                                    ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 药物五规则图表部分 -->
                            <div class="col-md-6">
                                <div class="card mb-4" style="height: 100%;">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Lipinski's Rule Visualization</h5>
                                    </div>
                                                        <div class="card-body d-flex flex-column align-items-center justify-content-center p-0">
                        <div id="radarChart" style="width: 100%; height: 300px; cursor: pointer;"></div>

                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Drug_likeness 单独显示部分 -->
                        <?php if (isset($drug_likeness_value) && $drug_likeness_value !== null): ?>
                            <?php
                            $dl_judgment = 'N/A';
                            $dl_class = 'drug-likeness-neutral';
                            $dl_icon = 'fa-info-circle';
                            
                            if (is_numeric($drug_likeness_value)) {
                                // 假设Drug_likeness > 0.5为好
                                if (floatval($drug_likeness_value) > 0.7) {
                                    $dl_judgment = 'Excellent';
                                    $dl_class = 'drug-likeness-excellent';
                                    $dl_icon = 'fa-check-circle';
                                } elseif (floatval($drug_likeness_value) > 0.5) {
                                    $dl_judgment = 'Good';
                                    $dl_class = 'drug-likeness-good';
                                    $dl_icon = 'fa-thumbs-up';
                                } elseif (floatval($drug_likeness_value) > 0.3) {
                                    $dl_judgment = 'Moderate';
                                    $dl_class = 'drug-likeness-moderate';
                                    $dl_icon = 'fa-dot-circle';
                                } else {
                                    $dl_judgment = 'Poor';
                                    $dl_class = 'drug-likeness-poor';
                                    $dl_icon = 'fa-thumbs-down';
                                }
                            }
                            ?>
                            <div class="drug-likeness-container mt-4 mb-4">
                                <div class="card">
                                    <div class="card-body <?php echo $dl_class; ?>">
                                        <div class="row align-items-center">
                                            <div class="col-lg-1 col-md-2 text-center mb-3 mb-md-0">
                                                <i class="fas <?php echo $dl_icon; ?> fa-3x"></i>
                                            </div>
                                            <div class="col-lg-11 col-md-10">
                                                <div class="d-md-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h5 class="mb-2">
                                                            Quantitative estimation of drug-likeness
                                                            <a href="#" class="qed-info-icon" data-toggle="tooltip" data-html="true" data-placement="top" title="Quantitative estimation of drug-likeness (QED) stands for quantitative estimation of drug-likeness. The empirical rationale of the QED measure reflects the underlying distribution of molecular properties including molecular weight, logP, topological polar surface area, number of hydrogen bond donors and acceptors, the number of aromatic rings and rotatable bonds, and the presence of unwanted chemical functionalities.">
                                                                <i class="fas fa-question-circle"></i>
                                                            </a>
                                                        </h5>
                                                        <div class="drug-likeness-value">
                                                            <span class="h3"><?php echo htmlspecialchars($drug_likeness_value); ?></span>
                                                        </div>
                                                    </div>
                                                    <div class="drug-likeness-judgment mt-3 mt-md-0">
                                                        <span class="badge"><?php echo $dl_judgment; ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <?php endif; ?>
                        
                        <!-- 调试信息 -->
                        <?php if (isset($_GET['debug']) && $_GET['debug'] == 1): ?>
                        <div class="card mt-3">
                            <div class="card-header">Debug Info</div>
                            <div class="card-body">
                                <p>Drug_likeness value: <?php echo isset($drug_likeness_value) ? $drug_likeness_value : 'Not set'; ?></p>
                                <p>Data type: <?php echo isset($detail_type) ? $detail_type : 'Not set'; ?></p>
                                <p>Data type: <?php echo isset($detail_type) ? $detail_type : 'Not set'; ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚区域 -->
    <footer class="container-fluid py-3 text-center">
        <p>Copyright © 2025, Ningbo University, Institute of Drug Discovery Technology.</p>
    </footer>

    <!-- 引入JavaScript文件 -->
    <script src="../../public/js/jquery-3.7.1.min.js"></script>
    <script src="../../public/js/bootstrap.bundle.min.js"></script>
    <script src="../../public/js/common.js"></script>
    <script>
        function toggleNav() {
            const nav = document.querySelector('nav');
            const hamburger = document.querySelector('.hamburger');
            nav.classList.toggle('show');
            hamburger.classList.toggle('active');
        }
        
        // 初始化工具提示
        document.addEventListener('DOMContentLoaded', function() {
            // 检查jQuery和Bootstrap是否已加载
            if (typeof $ !== 'undefined' && typeof $.fn.tooltip !== 'undefined') {
                $('[data-toggle="tooltip"]').tooltip();
            } else {
                console.warn('Bootstrap tooltip could not be initialized. jQuery or Bootstrap JS might be missing.');
            }
        });
        
        <?php if ($show_lipinski_radar && !empty($radar_labels)): ?>
        // 初始化药物五规则雷达图
        document.addEventListener('DOMContentLoaded', function() {
            // 获取PHP生成的数据
            const radarLabels = <?php echo json_encode($radar_labels ?? []); ?>;
            const radarData = <?php echo json_encode($radar_data ?? []); ?>;
            const judgmentData = <?php echo json_encode($judgment_data ?? []); ?>;
            
            // 如果没有数据，不显示图表
            if (!radarLabels.length || !radarData.length || !document.getElementById('radarChart')) {
                if (document.getElementById('radarChart')) {
                    document.getElementById('radarChart').innerHTML = '<div class="text-center p-5">No Lipinski rule data available for visualization</div>';
                }
                return;
            }
            
            // Lipinski规则标准值
            const lipinskiStandards = {
                'MolWt': 500,
                'NumHDonors': 5,
                'NumHAcceptors': 10,
                'MolLogP': 5,
                'NumRotatableBonds': 10
            };
            
            // 准备指标数据和最大值计算
            const indicators = radarLabels.map((label, index) => {
                let maxValue = 10; // 默认值
                let standardValue = 0;
                let actualValue = radarData[index];
                
                // 获取各维度的标准值
                if (label === 'MolWt') {
                    standardValue = 500;
                } else if (label === 'NumHDonors') {
                    standardValue = 5;
                } else if (label === 'NumHAcceptors') {
                    standardValue = 10;
                } else if (label === 'MolLogP') {
                    standardValue = 5;
                } else if (label === 'NumRotatableBonds') {
                    standardValue = 10;
                }
                
                // 根据标准和实际值设置最大值
                // 如果实际值超过标准值，则最大值为实际值的1.1倍
                // 否则最大值为标准值的1.1倍
                if (actualValue > standardValue) {
                    maxValue = actualValue * 1.1;
                } else {
                    maxValue = standardValue * 1.1;
                }
                
                return {
                    name: label,
                    max: maxValue
                };
            });
            
            // 创建实际值数据
            const actualData = radarData;
            
            // 标准值 - Lipinski规则阈值
            const standardData = radarLabels.map(label => {
                if (label === 'MolWt') return 500;
                if (label === 'NumHDonors') return 5;
                if (label === 'NumHAcceptors') return 10;
                if (label === 'MolLogP') return 5;
                if (label === 'NumRotatableBonds') return 10;
                return 5; // 默认值
            });
            
            // 初始化图表
            const chartDom = document.getElementById('radarChart');
            const myChart = echarts.init(chartDom);
            
            // 图表配置
            const option = {
                                                  title: {
                    text: 'Lipinski\'s Rule of Five',
                    left: 'center',
                    top: 0,
                    textStyle: {
                        color: '#002060',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                                 tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(0,32,96,0.85)',
                    borderColor: 'rgba(0,32,96,0.2)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#fff'
                    },
                    axisPointer: {
                        type: 'shadow',
                        lineStyle: {
                            color: '#002060',
                            width: 1
                        },
                        shadowStyle: {
                            color: 'rgba(0,32,96,0.1)'
                        }
                    },
                    position: function (point, params, dom, rect, size) {
                        // 固定在鼠标位置的右上角
                        return [point[0] + 10, point[1] - 60];
                    },
                                        formatter: function(params) {
                        // 获取当前悬浮的维度
                        const dataIndex = params.dataIndex;
                        const name = radarLabels[dataIndex];
                        const actualValue = params.value;

                        // 获取正确的标准值
                        let standardValue;
                        if (name === 'MolWt') {
                            standardValue = 500;
                        } else if (name === 'NumHDonors') {
                            standardValue = 5;
                        } else if (name === 'NumHAcceptors') {
                            standardValue = 10;
                        } else if (name === 'MolLogP') {
                            standardValue = 5;
                        } else if (name === 'NumRotatableBonds') {
                            standardValue = 10;
                        } else {
                            standardValue = 5; // 默认值
                        }

                         // 使用更友好的名称
                         const nameMap = {
                             'MolWt': 'Molecular Weight',
                             'NumHDonors': 'H-Bond Donors',
                             'NumHAcceptors': 'H-Bond Acceptors',
                             'MolLogP': 'LogP Value',
                             'NumRotatableBonds': 'Rotatable Bonds'
                         };

                         const label = nameMap[name] || name;

                         let color, result;
                         if (name === 'MolWt') {
                             result = actualValue <= 500 ? 'Compliant' : 'Exceeds';
                             color = actualValue <= 500 ? '#28a745' : '#dc3545';
                         } else if (name === 'NumHDonors') {
                             result = actualValue <= 5 ? 'Compliant' : 'Exceeds';
                             color = actualValue <= 5 ? '#28a745' : '#dc3545';
                         } else if (name === 'NumHAcceptors') {
                             result = actualValue <= 10 ? 'Compliant' : 'Exceeds';
                             color = actualValue <= 10 ? '#28a745' : '#dc3545';
                         } else if (name === 'MolLogP') {
                             result = actualValue <= 5 ? 'Compliant' : 'Exceeds';
                             color = actualValue <= 5 ? '#28a745' : '#dc3545';
                         } else if (name === 'NumRotatableBonds') {
                             result = actualValue <= 10 ? 'Compliant' : 'Exceeds';
                             color = actualValue <= 10 ? '#28a745' : '#dc3545';
                         }
                         
                         return `<div style="padding: 12px; background-color: rgba(0,32,96,0.95); border-radius: 8px; color: white; min-width: 200px; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">
                             <div style="font-weight: bold; margin-bottom: 12px; color: white; font-size: 14px; text-align: center; border-bottom: 1px solid rgba(255,255,255,0.3); padding-bottom: 8px;">${label}</div>
                             <div style="margin-bottom: 8px; color: white; font-size: 13px; line-height: 1.5;">
                                <div style="margin-bottom: 6px; font-weight: bold;">Actual Value: ${actualValue}</div>
                                <div style="font-weight: bold;">Standard Value: ${standardValue}</div>
                             </div>
                             <div style="margin-top: 12px; text-align: center; padding: 6px; background-color: ${color}; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                                <span style="color: white; font-weight: bold; font-size: 12px;">${result}</span>
                             </div>
                         </div>`;
                    }
                },
                legend: {
                    data: ['Compound', 'Rule Threshold'],
                    bottom: 0
                },
                                                  radar: {
                    indicator: indicators,
                    radius: '55%',
                    center: ['50%', '45%'],
                    splitNumber: 4,
                    shape: 'polygon',
                    scale: true,
                    axisName: {
                        color: '#002060',
                        fontSize: 10,
                        padding: [3, 5],
                        formatter: function(text) {
                            // 为不同的轴使用全称
                            const nameMap = {
                                'MolWt': 'Molecular Weight',
                                'NumHDonors': 'H-Bond Donors',
                                'NumHAcceptors': 'H-Bond Acceptors',
                                'MolLogP': 'LogP Value',
                                'NumRotatableBonds': 'Rotatable Bonds'
                            };
                            return nameMap[text] || text;
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: ['rgba(0, 32, 96, 0.1)', 'rgba(0, 32, 96, 0.2)']
                        }
                    },
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['rgba(255, 255, 255, 0.5)', 'rgba(240, 245, 255, 0.5)']
                        }
                    },
                    axisLabel: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(0, 32, 96, 0.3)'
                        }
                    }
                },
                series: [
                    {
                        name: 'Compound',
                        type: 'radar',
                        data: [
                            {
                                value: actualData,
                                name: 'Compound',
                                lineStyle: {
                                    width: 2,
                                    color: '#002060' // 深蓝色
                                },
                                areaStyle: {
                                    color: 'rgba(0, 32, 96, 0.2)' // 淡蓝色填充
                                },
                                symbolSize: 6
                            }
                        ]
                    },
                    {
                        name: 'Rule Threshold',
                        type: 'radar',
                        data: [
                            {
                                value: standardData,
                                name: 'Rule Threshold',
                                lineStyle: {
                                    width: 2,
                                    color: '#32CD32' // 绿色
                                },
                                areaStyle: {
                                    color: 'rgba(50, 205, 50, 0.1)' // 淡绿色填充
                                },
                                symbolSize: 6
                            }
                        ]
                    }
                ]
            };
        <?php else: ?>
        // 页面没有雷达图
        document.addEventListener('DOMContentLoaded', function() {
            // 空函数，在非成分页面不初始化图表
        });
        <?php endif; ?>
            
            <?php if ($show_lipinski_radar && !empty($radar_labels)): ?>
            // 应用配置
            myChart.setOption(option);
            
            // 响应窗口大小调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        <?php endif; ?>
        });

        // 返回功能
        function goBack() {
            // 首先尝试从URL参数中获取返回地址
            const urlParams = new URLSearchParams(window.location.search);
            const returnUrl = urlParams.get('return_url');

            if (returnUrl && returnUrl.trim() !== '') {
                // 如果有返回URL参数，直接跳转并恢复滚动位置
                const decodedUrl = decodeURIComponent(returnUrl);

                // 保存滚动位置恢复标记到localStorage（跨标签页共享）
                localStorage.setItem('shouldRestoreScroll', 'true');

                // 尝试在原标签页中打开，如果失败则在当前标签页打开
                try {
                    // 检查是否是新标签页
                    if (window.opener) {
                        // 如果有opener，尝试在原窗口中导航
                        window.opener.location.href = decodedUrl;
                        window.close();
                    } else {
                        // 否则在当前窗口中导航
                        window.location.href = decodedUrl;
                    }
                } catch (e) {
                    // 如果出错，直接在当前窗口导航
                    window.location.href = decodedUrl;
                }
                return;
            }

            // 检查localStorage中是否有Study页面信息（跨标签页共享）
            const studyPageUrl = localStorage.getItem('studyPageUrl');
            if (studyPageUrl && studyPageUrl.trim() !== '') {
                // 保存滚动位置恢复标记
                localStorage.setItem('shouldRestoreScroll', 'true');

                try {
                    if (window.opener) {
                        window.opener.location.href = studyPageUrl;
                        window.close();
                    } else {
                        window.location.href = studyPageUrl;
                    }
                } catch (e) {
                    window.location.href = studyPageUrl;
                }
                return;
            }

            // 检查document.referrer是否包含Study.php
            if (document.referrer && document.referrer.includes('Study.php')) {
                // 如果是从Study页面来的，返回到Study页面
                try {
                    if (window.opener) {
                        window.opener.focus();
                        window.close();
                    } else {
                        window.history.back();
                    }
                } catch (e) {
                    window.history.back();
                }
                return;
            }

            // 最后的降级方案
            try {
                if (window.opener) {
                    window.opener.location.href = 'Project.php';
                    window.close();
                } else {
                    window.location.href = 'Project.php';
                }
            } catch (e) {
                window.location.href = 'Project.php';
            }
        }
    </script>
</body>
</html>