<?php
// 允许跨域请求的头部信息
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

require_once(__DIR__ . '/../includes/config/db_connection.php');
$conn = require(__DIR__ . '/../includes/config/db_connection.php');

mysqli_set_charset($conn, 'utf8');

// 获取数据
$sql = "SELECT * FROM sample";
$result = $conn->query($sql);

$data = array();
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
}

// 设置响应头为JSON
header('Content-Type: application/json');

// 返回JSON数据
echo json_encode($data);

$conn->close();
?>