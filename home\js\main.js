// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Search box functionality
    const searchInput = document.querySelector('.input-group .form-control');
    const searchButton = document.querySelector('.input-group .btn-primary');
    
    if (searchInput && searchButton) {
        // Trigger search when search button is clicked
        searchButton.addEventListener('click', function() {
            performSearch(searchInput.value);
        });
        
        // Trigger search when Enter key is pressed in search box
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch(searchInput.value);
            }
        });
    }
    
    // Image hover effects
    const categoryImages = document.querySelectorAll('.text-center img');
    categoryImages.forEach(img => {
        img.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        img.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // Responsive navigation
    function adjustNavigation() {
        const header = document.querySelector('header');
        const navigation = document.querySelector('header nav');
        
        if (window.innerWidth < 768) {
            if (navigation) {
                navigation.classList.add('w-100');
                navigation.classList.add('mt-3');
            }
        } else {
            if (navigation) {
                navigation.classList.remove('w-100');
                navigation.classList.remove('mt-3');
            }
        }
    }
    
    // Initial navigation adjustment
    adjustNavigation();
    
    // Adjust navigation on window resize
    window.addEventListener('resize', adjustNavigation);
});

// Search functionality
function performSearch(query) {
    if (!query.trim()) {
        alert('Please enter search keywords!');
        return;
    }
    
    // This can be replaced with actual search logic
    console.log('Search keywords:', query);
} 